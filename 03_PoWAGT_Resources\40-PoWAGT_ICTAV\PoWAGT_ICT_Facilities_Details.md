# PoWAT ICT Facilities


## System Development and Management Environment

### Team Members
<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>

===================================================================================


### Google Tools
#### Google Space
```
google space name: FHWA Ghosts Phase 1
google space URL: https://mail.google.com/mail/u/0/#chat/space/AAQAMWA0Z88
```

#### Google Drive
#### Google Drive Folder containing the current database

I have uploaded the entire contents of my thumb drive (mine is the master) to https://drive.google.com/drive/folders/1l00OR9ujjipBw9dJFtcIRInmJ07S2nHE?usp=drive_link and you will be able to have full access from there to everything, including the records that have been uploaded today.

===================================================================================

### Atlassian Tools

#### Atlassian Environment
```
admin user email: <EMAIL>
admin user password: Damboring_1886
admin user name: FHWA Admin
organisation name: Family History WA
team name: FHWA Phase 1 All Team
team name: FHWA Phase 1 Devs
atlassian domain URL: fhwa.atlassian.net
date created: 2025-06-18 09:45:25
products:
    Jira (fhwa.atlassian.net | E-43N-3WF-TSN-WVF)
        https://fhwa.atlassian.net/jira/software/projects/SCRUM/boards/1/backlog
    Confluence (fhwa.atlassian.net | E-43N-3WR-RKN-JQG)
        https://fhwa.atlassian.net/wiki/home
    Jira Product Discovery (fhwa.atlassian.net | E-43N-3WV-EFU-34X)
        https://fhwa.atlassian.net/jira/polaris/projects/MDP/ideas/view/7451967
    Jira Service Management (fhwa.atlassian.net | E-43N-3XH-ZEN-722)
        https://fhwa.atlassian.net/jira/servicedesk/projects/SUP/getting-started
    Compass (fhwa.atlassian.net | E-43N-3XR-SW7-8XP)
        https://fhwa.atlassian.net/compass/scorecard/************************1ae3b17045a2
    Bitbucket
        https://bitbucket.org/fhwa/workspace/overview/
        bitbucket.org/fhwa
    Loom (fhwa.atlassian.net | E-43N-3Y6-8B9-CU2)
        https://www.loom.com/looms
        https://www.loom.com/welcome?recorded=false
        chrome extension: https://chromewebstore.google.com/detail/loom-%E2%80%93-screen-recorder-sc/liecbddmkiiihnedobmlmillhodjkdmb?recorded=false&pli=1
```

#### Setting Up Atlassian from afresh
##### Starting Fresh with Atlassian: A Guide to Creating Your New Organization and Custom Subdomain for Jira and Confluence

For individuals and teams looking to establish a new, independent workspace within the Atlassian ecosystem, creating a new organization with a unique subdomain is the first step. This process involves signing up for a new Atlassian account, which in turn generates a new organization and a default site. From there, you can customize your domain for a more branded experience and begin inviting your team members.

Here's a comprehensive guide to getting started:

###### **Step 1: Sign Up for a New Atlassian Account and Organization**

To begin, you'll need to sign up for a new Atlassian account. This process is integrated with the creation of your first product site (either Jira or Confluence).

1.  **Navigate to the Atlassian Website:** Go to the official Atlassian website.
2.  **Choose Your First Product:** Select either Jira Software or Confluence to start your free trial. This action will initiate the creation of your new organization.
3.  **Provide Your Email Address:** You will be prompted to enter your email address. Since you don't have an existing account, Atlassian will guide you through the new account creation process.
4.  **Create Your Site:** During the signup process, you will be asked to choose a name for your site. This will form your initial, default URL, which will look something like `your-site-name.atlassian.net`. This site is housed within your newly created Atlassian organization.

Upon completion, you will have a new Atlassian account and an organization that contains your Jira or Confluence site.

###### **Step 2: Setting Up Your Custom Subdomain**

After establishing your organization with the default `.atlassian.net` address, you can add a custom domain to provide a more professional and memorable URL for your team.

**Important Note on Subdomain Structure:** Atlassian currently requires a specific structure for custom domains: `<subdomain>.<subdomain>.<domain>`. For instance, you would need to use something like `jira.team.yourcompany.com` rather than a simple `jira.yourcompany.com`. However, you can set up a redirect from a simpler URL to your Atlassian-hosted address.

**Steps to Add a Custom Domain:**

1.  **Access Organization Settings:** Log in to your Atlassian site. From your product, navigate to the **Administration** section. This is typically found in the settings or gear icon menu. In the administration area, locate and go to your **Organization settings**.
2.  **Navigate to Domains:** Within your organization settings, find the **Domains** or **Custom Domains** section.
3.  **Add Your Custom Domain:** Initiate the process to add a new custom domain. You will be asked to enter the domain you wish to use.
4.  **Verify Domain Ownership:** To ensure you own the domain you are adding, Atlassian will require you to verify it. This is typically done by adding a specific TXT record to your domain's DNS settings. Your domain registrar or DNS hosting provider will have instructions on how to do this.
5.  **Configure DNS Records:** Once your domain is verified, you will need to configure a CNAME record in your DNS settings. This record will point your chosen custom subdomain to your Atlassian site's original `.atlassian.net` address. Atlassian will provide the exact CNAME value you need to use.
6.  **Activate the Custom Domain:** After your DNS changes have propagated (which can take some time), you can return to your Atlassian organization settings to activate the custom domain.

Once activated, your team will be able to access your Jira and Confluence instances through the new custom subdomain.

###### **Step 3: Adding Users to Your New Organization**

With your organization and custom domain in place, you can now build your team.

1.  **Go to User Management:** In your Atlassian organization's administration area, find the **Users** or **Directory** section.
2.  **Invite Users:** You will have the option to invite new users by entering their email addresses.
3.  **Assign Product Access:** As you invite users, you can assign them access to the specific products you have set up, such as Jira Software and/or Confluence.

Invited users will receive an email prompting them to join your organization. Once they have signed up, they will be able to log in and collaborate with you in your new, custom-branded Atlassian workspace.


#### Email 2 re Atlassian Tools

Hi

We are getting going with development of the new database/website for People of Western Australia's Ghost Towns.

We will use a set of tools from Atlassian (Jira, Confluence, Bitbucket, etc) to assist with the development. These will allow us to maintain all information, documentation and code relevant to the project. We will have our own Atlassian sub-domain (https://fhwa.atlassian.net/). In addition, we can use various of the Google facilities (mail, chat, Docs, Drive, etc) when and as necessary.

We will use the free tier of the Atlassian products - to keep costs down for the society. This limits us to mostly less than 10 users and sometimes less than 3. As a consequence, we have set up "roles" to use the products, each identified by an email from Google Workspace for FHWA. Individuals will be allocated to roles for this project, but those individuals may change for subsequent projects (the roles will remain the same). Thus the administrative role for this Phase 1 <NAME_EMAIL> and has been allocated to Tony Sutherland.  The Developer <NAME_EMAIL> and has been allocated to Sam Bradley. 

The roles and people allocated are:
1. FHWA_DEV: Sam Bradley (<EMAIL>)
2. FHWA_PM: Tracie Mulvey (<EMAIL>)
3. FHWA_IT: Christine Harris (<EMAIL>)
4. FHWA_BA: Tony Sutherland  (<EMAIL>)
5. FHWA_SME1: Rob Bennet (<EMAIL>)
6. FHWA_SME2: Ann Giles (<EMAIL>)
7. FHWA_GEN: Any other people (<EMAIL>)
8. FHWA_ADMIN: Tony Sutherland (<EMAIL>)

As you can see, there <NAME_EMAIL> role which will accommodate the general set of members who may be involved in the project. Various people may be allocated <NAME_EMAIL> role. Note that additional people may also be allocated <NAME_EMAIL> and <EMAIL> roles.

Each of the roles will be added as a team member in the FHWA Atlassian organisation.  When this happens, you will get an email to verify being added.
To make life a little easier, we can setup email forwarding from the role email (see above) to your own email address, so that you don't have to keep logging into the new email to see what you have been notified about. Once again, when the forwarding is being setup, you will get an email verifying the forwarding (it will come the role email address).

When everything is setup, various roles will be invited to review or comment on documentation or other details maintained in Jira or Confluence, or invited to chats in Google (if needed). When documents or information changes, you will typically receive an email. When you make a comment in Atlassian or Google products, your role email address will identify you as having made that comment.

Feel free to contact others via the role emails.  Using these role emails will also allow a record to be maintained in Google Workspace for future reference purposes.

A specific password has been created to use for both the Google Workspace email for the role, as well as the Atlassian team member. The password has been changed in Google Workspace already.  If asked for in Atlassian, please enter the password allocated below.
Please DO NOT change the password - we need to be able to get back into the role email and Atlassian account at a later time.

Your role email is:
The password allocated is:

Thanks
Tony Sutherland

PS Since all these facilities are shared and to be maintained into the future, please ensure that only emails, chats, documents and information relevant and pertinent to the project are used or shared.


#### Email 1 re Atlassian Tools
Hi All

I think we agreed that we would use the Atlassian toolset to assist in the management, documentation and development of our new system. The Atlassian toolset is a reasonably comprehensive set of facilities for software product developers and managers, used world-wide by numerous organisations large and small.

Atlassian offers a free tier usage for most products, varying from ten (10) free users to three (3) free users. Some products have no free usage availability.

If we find that we need some additional users for particular purposes, or that the Atlassian toolset is not adequate, we can integrate with other tools which may already be used within Family History WA or can be used for free.

As an example, Atlassian Loom is a meeting video recording solution which offers automatic transcription of the video, as well as collaboration capabilities, etc. The free tier allows for 25 recordings only.  Thus, it is unlikely to be suitable for ongoing communications use within FHWA (where Google Meet is now used and should continue to be used), but could be considered just for initial requirements gathering etc.

As a further example, we may need to very widely distribute certain documents for review and comment by many people (more than 10). We may find that using Confluence and specified generic roles (see below) may be unsuitable, and therefore should consider the use of Google Docs for these particular widespread-use documents.

We can also use Google Drive as a backup for documentation and details stored in Atlassian products.

Because of the limit to numbers of free users, and due to the potentially wide user community, I propose that we use Roles as logins. For the purposes of the current project, each role can be allocated to one or more people in FHWA etc.  In the future, the roles will remain the same but the allocation to people may differ (for instance, the DEV, PM, BA may change people).

The proposed roles and allocations are:

| Seq | Role       | Person(s) Allocated     | Password         |
| --: | ---------- | ----------------------- | _--------------_ |
|   1 | FHWA_DEV   | Sam Bradley             | Marne_1925       |
|   2 | FHWA_PM    | Tracie Mulvey           | Xantippe_1923    |
|   3 | FHWA_IT    | Christine Harris        | Courtlea_1924    |
|   4 | FHWA_BA    | Tony Sutherland         | EastPithara_1952 |
|   5 | FHWA_SME1  | Rob Bennet, Terri Usher | Namban_1911      |
|   6 | FHWA_SME2  | Karen Lemnell           | Dalaroo_1894     |
|   7 | FHWA_GEN   | Any other people        | Walebing_1846    |
|   8 | FHWA_ADMIN | Tony Sutherland         | Damboring_1886   |

FHWA_DEV ; FHWA_PM ; FHWA_IT ; FHWA_BA ; FHWA_SME1 ; FHWA_SME2 ; FHWA_GEN ; FHWA_ADMIN ;

The concept is that each of us logs in using the Role as their Atlassian ID. Some IDs will allow multiple people to login (Atlassian allows 2 or more logins from the same ID at the same time - as I have tested it so far).

When making comments or editing etc, the Role will identify who has performed the change or made the comment. This will work for all single allocation roles but multiple allocation roles (such as FHWA_SME1 or FHWA_GEN) will not be able to identify precisely who made the comment or change. For the situation of a small number of multiple users (ie FHWA_SME1), we can easily ask each whether they made the change if it is critical to know (in most instances, it really will not matter). For the FHWA_GEN, we will not easily know, but I suggest that this role does NOT have edit capabilities, but only has read and comment capabilities.  We will be able to see what they have to say but they won't be able to change anything. We might like to apply this to the FHWA_SMEn roles as well.

Atlassian requires an email address to login. Thus, each of the roles will need an email to be created.  Could this be fhwa.org.au email addresses - is there capacity to do this? Can the created email addresses be organised to forward all emails to another email address, being that of the allocated user?  If it can't, then we can create individual gmail addresses as needs be, or I can create email addresses using my own domain and server (bilyendi.com) - which will also do the auto forwarding as well.

I think Atlassian requires a master user for the organisation. I propose that the FHWA_ADMIN role is used for that purpose. Once this is created, then a team can be created, with the other roles created and joining the team.  I can create all the roles, the organisation and the team once we have decided to pursue this path and have created the email addresses.

If there are no objections to the proposed concept for users on this project, all I need is an answer with respect to setting up email addresses.  Once we have this, I can get on with setting everything else up.

Thanks
Tony

---------------------------------------------------------------------

Product Name: Jira
    Use: Manage all elements of the system development and deployment, including development tasks, issues raised and resolved.
    URL: https://www.atlassian.com/software/jira
    Description: Jira is a powerful software for tracking issues, managing agile projects, and automating workflows for development teams.
    Number Free Users: 10
    Proposed User Roles: All

Product Name: Confluence
    Use: Prepare and maintain project/system documentation, including reviewing and commenting on documents. Other knowledge sharing needs.
    URL: https://www.atlassian.com/software/confluence
    Description: Confluence is a knowledge management and team collaboration platform for creating, sharing, and organizing documents and project information.
    Number Free Users: 10
    Proposed User Roles: All

Product Name: Bitbucket
    Use: Maintain up to date and historical copies of code and documents.
    URL: https://www.atlassian.com/software/bitbucket
    Description: Elevate your software delivery from planning to production and beyond, with built-in AI, CI/CD, and a best-in-class Jira integration.
    Number Free Users: 5
    Proposed User Roles: FHWA_DEV ; FHWA_IT ; FHWA_BA ; FHWA_ADMIN ;

Product Name: Jira Product Discovery
    Use: Maintain a Roadmap of future releases of the Family History system, including registering all ideas about future requirements etc.
    URL: https://www.atlassian.com/software/jira/product-discovery
    Description: Capture insights, prioritize ideas, and build roadmaps - all in Jira
    Number Free Users: 3
    Proposed User Roles: FHWA_PM ; FHWA_IT ; FHWA_ADMIN ;

Product Name: Compass
    Use: Overview of the whole development process, metrics and process control.
    URL: https://www.atlassian.com/software/compass
    Description: Catalog everything your developers need to stay in the flow and improve software health with Atlassian’s out-of-the-box internal developer platform.
    Number Free Users: 3 full users, Unlimited basic users
    Proposed User Roles: FHWA_PM ; FHWA_IT ; FHWA_ADMIN ;

Product Name: Loom
    Use: Provide text transciption of meetings.
    URL: https://www.atlassian.com/software/loom
    Description: a meeting video recording solution which offers automatic transcription of the video, as well as collaboration capabilities, etc.
    Number Free Users: 25 recordings in total.
    Proposed User Roles:

Product Name: SourceTree
    Use: Manage GIT repositories, which keep all the code for the system in it.  VSCode may be preferred.
    URL: https://www.atlassian.com/software/sourcetree
    Description: Sourcetree simplifies how you interact with your Git repositories so you can focus on coding. Visualize and manage your repositories through Sourcetree's simple Git GUI.
    Number Free Users: Free Download
    Proposed User Roles:

Product Name: System of Work
    Use: Information about how to put everything together
    URL: https://www.atlassian.com/system-of-work/practices
    Description: Discover what Atlassian's Teamwork Lab has learned about how successful technology-driven organizations make teamwork work.
    Number Free Users:
    Proposed User Roles: All

Product Name:
    Use:
    URL:
    Description:
    Number Free Users:
    Proposed User Roles:

### Alternatives to Slack

Tony Sutherland <<EMAIL>>
	Mon 16 Jun, 16:23 (2 days ago)
	
https://g.co/gemini/share/0f46493be808

For teams of up to 10 users seeking a free Slack alternative with deep and complete integration with both Jira and Confluence, Microsoft Teams stands out as the top recommendation. Its robust, officially supported apps for Jira and Confluence Cloud provide a seamless workflow within a single interface, at no additional cost.1

Microsoft Teams offers a comprehensive free plan that allows for extensive integration with the Atlassian suite.2 With the Jira Cloud app for Teams, users can create, assign, and track Jira issues, receive customized notifications, and even hold meetings where Jira tickets can be viewed and updated in real-time.3 This level of interaction eliminates the need to constantly switch between applications, fostering a more efficient work environment.4

Similarly, the Confluence Cloud integration for Microsoft Teams enables users to receive notifications about page updates, create new Confluence pages directly from within a Teams channel, and easily share and preview pages, keeping the entire team informed and collaborative.5

While other open-source alternatives like Mattermost and Rocket.Chat also offer free plans and integrations with Jira and Confluence, they often come with certain limitations. Their free tiers, particularly the self-hosted "community" editions, may require more technical expertise to set up and maintain. The integrations, while functional, might not be as feature-rich or as seamlessly integrated as what Microsoft Teams provides out-of-the-box. For instance, some functionalities might rely on webhooks or third-party automation tools like Zapier, which can have their own usage limits on free plans.

Zulip, another open-source option, also provides free plans and integrations.6 However, similar to Mattermost and Rocket.Chat, the depth of its native integration on the free tier may be more focused on notifications rather than the rich, interactive experience offered by Microsoft Teams.

In summary, for a small team of 10 looking for a free, user-friendly, and powerful Slack alternative that works in complete harmony with their Jira and Confluence workflows, Microsoft Teams offers the most comprehensive and officially supported solution.

