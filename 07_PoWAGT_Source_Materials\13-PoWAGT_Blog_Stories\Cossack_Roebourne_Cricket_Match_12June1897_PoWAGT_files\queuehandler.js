var jetpackLikesWidgetBatch=[];var jetpackLikesMasterReady=false;var jetpackLikesLookAhead=2e3;var jetpackCommentLikesLoadedWidgets=[];var jetpackLikesDocReadyPromise=new Promise(e=>{if(document.readyState!=="loading"){e()}else{window.addEventListener("DOMContentLoaded",()=>e())}});function JetpackLikesPostMessage(e,t){if(typeof e==="string"){try{e=JSON.parse(e)}catch(e){return}}if(t&&typeof t.postMessage==="function"){try{t.postMessage(JSON.stringify({type:"likesMessage",data:e}),"*")}catch(e){return}}}function JetpackLikesBatchHandler(){const e=[];document.querySelectorAll("div.jetpack-likes-widget-unloaded").forEach(t=>{if(jetpackLikesWidgetBatch.indexOf(t.id)>-1){return}if(!jetpackIsScrolledIntoView(t)){return}jetpackLikesWidgetBatch.push(t.id);var i=/like-(post|comment)-wrapper-(\d+)-(\d+)-(\w+)/,s=i.exec(t.id),o;if(!s||s.length!==5){return}o={blog_id:s[2],width:t.width};if("post"===s[1]){o.post_id=s[3]}else if("comment"===s[1]){o.comment_id=s[3]}o.obj_id=s[4];e.push(o)});if(e.length>0){JetpackLikesPostMessage({event:"initialBatch",requests:e},window.frames["likes-master"])}}function JetpackLikesMessageListener(e){let t=e&&e.data;if(typeof t==="string"){try{t=JSON.parse(t)}catch(e){return}}const i=t&&t.type;const s=t&&t.data;if(i!=="likesMessage"||typeof s.event==="undefined"){return}const o="https://widgets.wp.com";if(o!==e.origin){return}switch(s.event){case"masterReady":jetpackLikesDocReadyPromise.then(()=>{jetpackLikesMasterReady=true;const e={event:"injectStyles"};const t=document.querySelector(".sd-text-color");const i=document.querySelector(".sd-link-color");const s=t&&getComputedStyle(t)||{};const o=i&&getComputedStyle(i)||{};if(document.body.classList.contains("jetpack-reblog-enabled")){JetpackLikesPostMessage({event:"reblogsEnabled"},window.frames["likes-master"])}e.textStyles={color:s["color"],fontFamily:s["font-family"],fontSize:s["font-size"],direction:s["direction"],fontWeight:s["font-weight"],fontStyle:s["font-style"],textDecoration:s["text-decoration"]};e.linkStyles={color:o["color"],fontFamily:o["font-family"],fontSize:o["font-size"],textDecoration:o["text-decoration"],fontWeight:o["font-weight"],fontStyle:o["font-style"]};JetpackLikesPostMessage(e,window.frames["likes-master"]);JetpackLikesBatchHandler()});break;case"showLikeWidget":break;case"showCommentLikeWidget":break;case"killCommentLikes":document.querySelectorAll(".jetpack-comment-likes-widget-wrapper").forEach(e=>e.remove());break;case"clickReblogFlair":if(wpcom_reblog&&typeof wpcom_reblog.toggle_reblog_box_flair==="function"){wpcom_reblog.toggle_reblog_box_flair(s.obj_id,s.post_id)}break;case"hideOtherGravatars":{hideLikersPopover();break}case"showOtherGravatars":{const e=document.querySelector("#likes-other-gravatars");if(!e){break}const t=e.querySelector("ul");e.style.display="none";t.innerHTML="";e.querySelectorAll(".likes-text span").forEach(e=>e.textContent=s.totalLikesLabel);(s.likers||[]).forEach(async(e,i)=>{if(e.profile_URL.substr(0,4)!=="http"){return}const o=document.createElement("li");t.append(o);o.innerHTML=`
				<a href="${encodeURI(e.profile_URL)}" rel="nofollow" target="_parent" class="wpl-liker">
					<img src="${encodeURI(e.avatar_URL)}"
						alt=""
						style="width: 28px; height: 28px;" />
					<span></span>
				</a>
				`;o.classList.add(e.css_class);o.querySelector("img").alt=s.avatarAltTitle.replace("%s",e.name);o.querySelector("span").innerText=e.name;if(i===s.likers.length-1){o.addEventListener("keydown",e=>{if(e.key==="Tab"&&!e.shiftKey){e.preventDefault();hideLikersPopover();JetpackLikesPostMessage({event:"focusLikesCount",parent:s.parent},window.frames["likes-master"])}})}});const i=function(){const t=getComputedStyle(e);const i=t.direction==="rtl";const o=document.querySelector(`*[name='${s.parent}']`);const a=o.getBoundingClientRect();const n=o.ownerDocument.defaultView;const r={top:a.top+n.pageYOffset,left:a.left+n.pageXOffset};e.style.display="none";let c=0;e.style.top=r.top+s.position.top-1+"px";if(i){const t=s&&s.likers?Math.min(s.likers.length,5):0;c=r.left+s.position.left+24*t+4;e.style.transform="translateX(-100%)"}else{c=r.left+s.position.left}e.style.left=c+"px";const l=s.width-20;const d=Math.floor(l/37);let k=Math.ceil(s.likers.length/d)*37+17+22;if(k>204){k=204}const p=n.innerWidth;e.style.left="-9999px";e.style.display="block";const f=e.offsetWidth;const m=c+f;if(m>p&&!i){c=a.left+a.width-f}else if(c-f<0&&i){e.style.transform="none";c=a.left}e.style.left=c+"px";e.setAttribute("aria-hidden","false")};i();e.focus();const o=function(e,t){var i;return function(){var s=this;var o=arguments;clearTimeout(i);i=setTimeout(function(){e.apply(s,o)},t)}};const a=o(i,100);e.__resizeHandler=a;window.addEventListener("resize",a)}}}window.addEventListener("message",JetpackLikesMessageListener);function hideLikersPopover(){const e=document.querySelector("#likes-other-gravatars");if(e){e.style.display="none";e.setAttribute("aria-hidden","true");const t=e.__resizeHandler;if(t){window.removeEventListener("resize",t);delete e.__resizeHandler}}}document.addEventListener("click",hideLikersPopover);function JetpackLikesWidgetQueueHandler(){var e;if(!jetpackLikesMasterReady){setTimeout(JetpackLikesWidgetQueueHandler,500);return}jetpackUnloadScrolledOutWidgets();var t=jetpackGetUnloadedWidgetsInView();if(t.length>0){JetpackLikesBatchHandler()}for(var i=0,s=t.length;i<=s-1;i++){e=t[i].id;if(!e){continue}jetpackLoadLikeWidgetIframe(e)}}function jetpackLoadLikeWidgetIframe(e){if(typeof e==="undefined"){return}const t=document.querySelector("#"+e);t.querySelectorAll("iframe").forEach(e=>e.remove());const i=t.querySelector(".likes-widget-placeholder");if(i&&i.classList.contains("post-likes-widget-placeholder")){const e=document.createElement("iframe");e.classList.add("post-likes-widget","jetpack-likes-widget");e.name=t.dataset.name;e.src=t.dataset.src;e.height="55px";e.width="100%";e.frameBorder="0";e.scrolling="no";e.title=t.dataset.title;i.after(e)}if(i.classList.contains("comment-likes-widget-placeholder")){const e=document.createElement("iframe");e.class="comment-likes-widget-frame jetpack-likes-widget-frame";e.name=t.dataset.name;e.src=t.dataset.src;e.height="18px";e.width="100%";e.frameBorder="0";e.scrolling="no";t.querySelector(".comment-like-feedback").after(e);jetpackCommentLikesLoadedWidgets.push(e)}t.classList.remove("jetpack-likes-widget-unloaded");t.classList.add("jetpack-likes-widget-loading");t.querySelector("iframe").addEventListener("load",e=>{JetpackLikesPostMessage({event:"loadLikeWidget",name:e.target.name,width:e.target.width},window.frames["likes-master"]);t.classList.remove("jetpack-likes-widget-loading");t.classList.add("jetpack-likes-widget-loaded")})}function jetpackGetUnloadedWidgetsInView(){const e=document.querySelectorAll("div.jetpack-likes-widget-unloaded");return[...e].filter(e=>jetpackIsScrolledIntoView(e))}function jetpackIsScrolledIntoView(e){const t=e.getBoundingClientRect().top;const i=e.getBoundingClientRect().bottom;return t+jetpackLikesLookAhead>=0&&i<=window.innerHeight+jetpackLikesLookAhead}function jetpackUnloadScrolledOutWidgets(){for(let e=jetpackCommentLikesLoadedWidgets.length-1;e>=0;e--){const t=jetpackCommentLikesLoadedWidgets[e];if(!jetpackIsScrolledIntoView(t)){const i=t&&t.parentElement&&t.parentElement.parentElement;i.classList.remove("jetpack-likes-widget-loaded");i.classList.remove("jetpack-likes-widget-loading");i.classList.add("jetpack-likes-widget-unloaded");i.querySelectorAll(".comment-likes-widget-placeholder").forEach(e=>e.style.display="block");jetpackCommentLikesLoadedWidgets.splice(e,1);t.remove()}}}var jetpackWidgetsDelayedExec=function(e,t){var i;return function(){clearTimeout(i);i=setTimeout(t,e)}};var jetpackOnScrollStopped=jetpackWidgetsDelayedExec(250,JetpackLikesWidgetQueueHandler);JetpackLikesWidgetQueueHandler();window.addEventListener("scroll",jetpackOnScrollStopped,true);