import*as e from"@wordpress/interactivity";var r={990:(e,r,t)=>{t.d(r,{j:()=>i});const i=(e,r,t,i=null)=>{if(""===r&&t)return"is_required";if(!t&&""===r)return"yes";if("checkbox-multiple"===e)return 0!==r.length?"yes":"is_required";if("date"===e)return((e,r)=>{let t,i,o;if(!e)return!1;switch(r){case"mm/dd/yy":[i,o,t]=e.split("/").map(Number);break;case"dd/mm/yy":[o,i,t]=e.split("/").map(Number);break;case"yy-mm-dd":[t,i,o]=e.split("-").map(Number);break;default:return!1}if(isNaN(t)||isNaN(i)||isNaN(o))return!1;const n=new Date(t,i-1,o);return n.getFullYear()===t&&n.getMonth()===i-1&&n.getDate()===o})(r,i)?"yes":"invalid_date";if("number"===e)return function(e,r){if(!/^-?\d+(\.\d+)?$/.test(e))return"invalid_number";const t=parseFloat(e);return r&&void 0!==r.min&&t<parseFloat(r.min)?"invalid_min_number":r&&void 0!==r.max&&t>parseFloat(r.max)?"invalid_max_number":"yes"}(r,i);if("file"===e)return r.some(e=>e.error)?"invalid_file_has_errors":r.some(e=>!e.isUploaded)?"invalid_file_uploading":"yes";let o=null;switch(e){case"url":o=/(?:(?:[Hh][Tt][Tt][Pp][Ss]?|[Ff][Tt][Pp]):\/\/)?(?:\S+(?::\S*)?@|\d{1,3}(?:\.\d{1,3}){3}|(?:[a-zA-Z\d\u00a1-\uffff](?:[a-zA-Z\d\u00a1-\uffff-]*[a-zA-Z\d\u00a1-\uffff])?)(?:\.[a-zA-Z\d\u00a1-\uffff](?:[a-zA-Z\d\u00a1-\uffff-]*[a-zA-Z\d\u00a1-\uffff])?)*(?:\.[a-zA-Z\u00a1-\uffff]{2,6}))(?::\d+)?(?:[^\s]*)?/;break;case"email":o=/^[^\s@]+@[^\s@]+\.[^\s@]+$/;break;case"telephone":o=/^\+?[0-9\s\-()]+$/;break;case"number":o=/^[0-9]+$/}return o&&!o.test(r)?"invalid_"+e:"yes"}},833:(r,t,i)=>{r.exports=(e=>{var r={};return i.d(r,e),r})({getConfig:()=>e.getConfig,getContext:()=>e.getContext,store:()=>e.store,withSyncEvent:()=>e.withSyncEvent})}},t={};function i(e){var o=t[e];if(void 0!==o)return o.exports;var n=t[e]={exports:{}};return r[e](n,n.exports,i),n.exports}i.d=(e,r)=>{for(var t in r)i.o(r,t)&&!i.o(e,t)&&Object.defineProperty(e,t,{enumerable:!0,get:r[t]})},i.o=(e,r)=>Object.prototype.hasOwnProperty.call(e,r);var o=i(833),n=i(990);const s=o.withSyncEvent||(e=>(...r)=>e(...r)),l="jetpack/form",a=(0,o.getConfig)(l),u=(e,r,t=!1)=>{const i=(0,o.getContext)();let s=i.fields[e];if(!s){const{fieldType:r,fieldLabel:t,fieldValue:o,fieldIsRequired:n,fieldExtra:l}=i;d(e,r,t,o,n,l),s=i.fields[e]}if(s){const{type:e,isRequired:i,extra:o}=s;s.value=r,s.error=(0,n.j)(e,r,i,o),s.showFieldError=t}},d=(e,r,t="",i="",s=!1,l=null)=>{const a=(0,o.getContext)();a.fields[e]||(a.fields[e]={id:e,type:r,label:t,value:i,isRequired:s,extra:l,error:(0,n.j)(r,i,s,l),step:a?.step?a.step:1})},f=e=>{if("number"===e.type){if("invalid_min_number"===e.error)return a.error_types.invalid_min_number.replace("%d",e.extra.min);if("invalid_max_number"===e.error)return a.error_types.invalid_max_number.replace("%d",e.extra.max)}return a.error_types&&a.error_types[e.error]},{state:c}=(0,o.store)(l,{state:{get fieldHasErrors(){const e=(0,o.getContext)(),r=e.fieldId,t=e.fields[r]||{};return!(!e.showErrors&&t.error&&"is_required"===t.error)&&(e.showErrors||t.showFieldError)&&t.error&&"yes"!==t.error},get isFormEmpty(){const e=(0,o.getContext)();return!Object.values(e.fields).some(e=>""!==e.value)},get isFieldEmpty(){const e=(0,o.getContext)(),r=e.fieldId,t=e.fields[r]||{};return!!(""===t.value||Array.isArray(t.value)&&0===t.value.length)},get hasFieldValue(){return!c.isFieldEmpty},get isSubmitting(){return(0,o.getContext)().isSubmitting},get isAriaDisabled(){return(0,o.getContext)().isSubmitting},get errorMessage(){const e=(0,o.getContext)(),r=e.fieldId,t=e.fields[r]||{};return(e.showErrors||t.showFieldError)&&t.error?f(t):""},get isFormValid(){if(c.isFormEmpty)return!1;const e=(0,o.getContext)();return!Object.values(e.fields).some(e=>"yes"!==e.error)},get showFromErrors(){const e=(0,o.getContext)();return!c.isFormValid&&e.showErrors},get getFormErrorMessage(){return c.isFormEmpty?a.error_types.invalid_form_empty:a.error_types.invalid_form},get getErrorList(){const e=[];if(c.isFormEmpty)return e;const r=(0,o.getContext)();return r.showErrors&&Object.values(r.fields).forEach(r=>{r.error&&"yes"!==r.error&&e.push({anchor:"#"+r.id,label:r.label+" : "+f(r),id:r.id})}),e},get getFieldValue(){const e=(0,o.getContext)(),r=e.fieldId;return e.fields[r].value}},actions:{updateFieldValue:(e,r)=>{u(e,r)},handleNumberKeyPress:s(e=>{/^[0-9.]*$/.test(e.key)||e.preventDefault(),"."===e.key&&e.target.value.includes(".")&&e.preventDefault()}),onFieldChange:e=>{let r=e.target.value;const t=(0,o.getContext)(),i=t.fieldId;"checkbox"===t.fieldType&&(r=e.target.checked?"1":""),u(i,r)},onMultipleFieldChange:e=>{const r=(0,o.getContext)(),t=r.fieldId,i=r.fields[t],n=e.target.value;let s=[...i.value||[]];e.target.checked?s.push(n):s=s.filter(e=>e!==n),u(t,s)},onFieldBlur:e=>{const r=(0,o.getContext)();u(r.fieldId,e.target.value,!0)},onFormSubmit:s(e=>{const r=(0,o.getContext)();c.isFormValid?r.isSubmitting=!0:(r.showErrors=!0,e.preventDefault(),e.stopPropagation())}),scrollIntoView:s(e=>{const r=(0,o.getContext)(),t=document.querySelector(r.item.anchor);if(t)return t.focus({preventScroll:!0}),t.scrollIntoView({behavior:"smooth"}),void e.preventDefault();const i=r.item.anchor.substring(1),n=document.querySelector('[name="'+i+'"]');if(n)return n.focus({preventScroll:!0}),n.scrollIntoView({behavior:"smooth"}),void e.preventDefault();const s=document.getElementById(i+"-label");s&&(s.querySelector("input").focus({preventScroll:!0}),s.scrollIntoView({behavior:"smooth"}),e.preventDefault())})},callbacks:{initializeField(){const e=(0,o.getContext)(),{fieldId:r,fieldType:t,fieldLabel:i,fieldValue:n,fieldIsRequired:s,fieldExtra:l}=e;d(r,t,i,n,s,l)}}});