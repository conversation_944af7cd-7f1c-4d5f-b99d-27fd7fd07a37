# Study Phase Quotation
Quote #: QT-2025-002
Date: 08/06/2025
Valid until: 30/07/2025
Western Australian Ghost Towns Website - Study Phase
Client: FamilyHistoryWA <EMAIL>
Service Provider: <PERSON>
202 President <PERSON>, WA 6105 Phone: **********
Email: samu<PERSON><PERSON><PERSON>@outlook.com.au ABN: ***********

# Project Overview
The study phase of the will deliver comprehensive documentation and specifications needed to plan and implement a central repository for genealogical information about people who lived in ghost towns and abandoned communities across Western Australia. This study phase will also aim to ensure the design of the system is compatible for use in future Family History WA projects.

This quotation outlines a contract for <PERSON> to take on a number of technical roles in study phase of the project and be responsible for key deliverables as outlined in the project plan and this quote.Approach & Methodology

The approach will be to use an iterative methodology where each iteration focuses on a new set of deliverables while updating previous ones based on new information and feedback. The study phase will include workshops, user interviews (virtual), and regular stakeholder review meetings. All documentation will be maintained in Atlassian tools (JIRA, Confluence, Bitbucket) for collaboration and transparency.

# Timeframe
No strict timeframe is proposed for the completion of the study phase as time must be allowed to ensure adequate feedback and input from stakeholders and users. The minimum time for completion is estimated at ~7 weeks, however this quote is made with the assumption this time could be spread out over a much longer time as determined by the availability and needs of users and stakeholders. This quote is valid for participation in project up to 6 months from the start date.

# Project Iterations and Deliverables
## Iteration 1: Requirements & Early Concepts

1. Project kickoff & project tool setup
2. Workshop 1: Requirements gathering (Ghost Towns focus)
3. Draft requirements specification including:
    - Feature-driven development approach, breaking requirements into discrete features
    - Grouping of features into "Epics" as required
    - Categorisation of requirements (Functional/Non-Functional)
    - Appropriate tagging (Business/Stakeholder/Solution/Transition Requirements)
    - Prioritisation of requirements with stakeholders
4. Creation of an "initial pitch" with early wireframes & architecture diagrams to elicit early feedback
5. User interviews and follow-up on requirements (virtual/asynchronous)
6.  Iteration review meeting with stakeholders

## Iteration 2: Technical & Architectural Design

1. Update requirements draft based on feedback
2. Workshop 2: Extensibility & future-proofing
3. Develop draft technical specification & architecture:
    - Document constraints and assumptions
    - Research capabilities of the nominated infrastructure provider
    - Technical stack options comparison
    - Technology recommendations with justification
    - Stakeholder validation of recommendations
    - High-level architecture definition
    - Identification of extensibility points for future projects
    - Initial database design
4. Set up a data entry (backend) prototype
5. Generate an API schema based on the prototype
6. Update wireframes based on new insights
7. Iteration review meeting with stakeholders

## Iteration 3: UX & Design Artifacts

1. Update requirements and technical drafts
2. Develop UX flows for different user personas
3. Create a design template (mock-up or prototype front-end)
4. Document style guide (colors, typography, UI components)
5. Create wireframes for key screens not represented in the prototype
6. User validation of design artifacts (virtual/asynchronous)
7. Update all previous deliverables as needed
8. Iteration review meeting with stakeholders

## Iteration 4: Finalisation & Reporting

1. Finalise all deliverables:
    - Incorporate feedback from iteration review meeting
    - Convert deliverables into final format (TBD)
    - Review for consistency and completeness
    - Final stakeholder review
2. Prepare final report and handover documentation
3. Study phase closure and retrospective
4. Presentation of findings and recommendations for implementation phase

## Ongoing Activities

1. User follow-up and interviews (throughout the study phase)
2. Regular project status updates
3. Collaboration with stakeholders via Atlassian tools
4. Quality assurance of all documentation

# Cost
Total Study Phase Cost: $5,000 AUD

# Terms and Conditions

1. Payment Schedule: 25% deposit ($1,250) upon project commencement, 75% ($3,750) upon completion of the study phase. (Negotiable)
2. Timeline: Study phase to be completed in a minimum of 2 months and a maximum of 6 months from commencement, with key deliverables available earlier according to the iteration schedule.
3. Changes: Significant changes to the study scope may incur additional costs and timeline adjustments.
4. Intellectual Property: All documentation, diagrams, and design assets created during this study phase become the property of FamilyHistoryWA upon final payment.
5. Tools: The Atlassian tool stack (JIRA, Confluence, Bitbucket) will be used for project management, documentation, and prototypes and are the responsibility of FamilyHistoryWA.
6. Implementation Quote: Upon completion of the study phase, a separate detailed implementation quote will be provided based on the finalised requirements and technical specifications.
7. Stakeholder Engagement: Regular stakeholder participation is required for workshops, reviews, and feedback sessions to ensure the study phase meets expectations.

Accepted by FamilyHistoryWA:
Signature / Date

Provided by Sam bradley:
Signature / Date
