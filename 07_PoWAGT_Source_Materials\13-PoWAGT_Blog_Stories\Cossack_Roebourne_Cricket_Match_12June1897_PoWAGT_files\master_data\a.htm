<!DOCTYPE html>
<html><head>
<meta http-equiv="content-type" content="text/html; charset=UTF-8">
<script>
	var _wpcomProxyRequestHasCookie = false;
	var _wpcomProxyToken = null;
	var _wpcomProxyTokenPinghub = null;
	var _wpcomProxyRequestDC = "bur";
</script>
<script type="text/javascript" src="rlt-proxy.js"></script>
<script type="text/javascript">
	// compute origin from location hash
	var parentOrigin;
	if (window.location.hash) {
		parentOrigin = window.location.hash.substr(1);
	}

	rltInitialize({ parentOrigin: parentOrigin });
</script>
<script>!function(){var e=["Allow","Date","Content-Type","Content-Length","Link","X-WP-Total","X-WP-TotalPages"],o=["Accept","X-Fingerprint"],t={},n=["https://wordpress.com","https://cloud.jetpack.com","https://agencies.automattic.com","https://hosts.automattic.com","http://wpcalypso.wordpress.com","https://forums.pocketcasts.com","https://forums.dayoneapp.com","http://widgets.wp.com","https://widgets.wp.com","https://dev-mc.a8c.com","https://mc.a8c.com","https://vrc.a8c.com","https://dserve.a8c.com","http://calypso.localhost:3000","https://calypso.localhost:3000","http://jetpack.cloud.localhost:3000","https://jetpack.cloud.localhost:3000","http://agencies.localhost:3000","https://agencies.localhost:3000","http://hosts.localhost:3000","https://hosts.localhost:3000","http://calypso.localhost:3001","https://calypso.localhost:3001","https://calypso.live","http://127.0.0.1:41050","http://send.linguine.localhost:3000"],a=window.location.hash.replace("#","").split("/",3).join("/"),r=!1;["https://mc.a8c.com","https://dev-mc.a8c.com","https://dev-mc.wordpress.com"].indexOf(a)>-1&&(r=!0);var s={};function c(){var e,o=document.cookie.split(/;\s*/);for(e=0;e<o.length;e++)if(o[e].match(/^wp_api=/))return(o=o[e].split("="))[1]}function p(e){var o=e.split(".");return 3!==o.length?null:function(e){var o=e.replace("-","+").replace("_","/"),t=atob(o);try{return JSON.parse(t)}catch{return null}}(o[1])}function i(e){if(!e)return!1;var o=p(e);return!(!o||!o.exp)&&Date.now()<1e3*(o.exp-60)}var d,l=!0,u=[];function h(e){var o,t=[];function n(e,o){var a,r,s;if(Array.isArray(o))o.forEach((function(o,t){n(e+"["+("object"==typeof o&&null!==o?t:"")+"]",o)}));else if("object"==typeof o)for(a in o)n(e+"["+a+"]",o[a]);else r=e,s=o,t.push(encodeURIComponent(r)+"="+encodeURIComponent(null==s?"":s))}if(!e)return"";for(o in e)n(o,e[o]);return t.join("&")}function f(e){return e.response?"json"===e.responseType?e.response:"blob"===e.responseType?"application/json"===e.response.type?{body:null}:e.response:JSON.parse(e.response):{body:null}}function m(o,t,n){return function(a,r){var s;s=function(o,t){var n=[],a={},r={};return o&&o.headers&&(n=o.headers[0]&&o.headers[0].name?o.headers:Object.keys(o.headers).map((function(e){return{name:e,value:o.headers[e]}}))),n.forEach((function(e){a[e.name.toUpperCase()]=e.value})),e.forEach((function(e){var o=e.toUpperCase();a[o]?r[e]=a[o]:t&&(r[e]=t.getResponseHeader(e))})),r}(a,r);var c=a instanceof Blob?a:a.body;y(n?[c,a.code,s,o]:[c,a.code,o],t)}}function y(e,o){o&&(e=JSON.stringify(e)),window.parent.postMessage(e,a)}function b(e,n){function p(e,o){return"string"==typeof e[o]&&e[o].length>0}if(!p(e,"path")){const o={apiNamespace:e.apiNamespace,apiVersion:e.apiVersion,method:e.method},t=`data.path is not a valid string. Debug Info: ${Object.entries(o).map((([e,o])=>`${e}:${o}`)).join(", ")}`;return void y(e.supports_args?[t,400,{},e.callback]:[t,400,e.callback],n)}function d(e,o){return e+(-1===e.indexOf("?")?"?":"&")+o}p(e,"apiVersion")||(e.apiVersion=p(e,"version")?e.version.replace(/[^0-9\\.]/,""):"1");var l=window.location.protocol+"//public-api.wordpress.com/";(p(e,"apiNamespace")?(l+=e.apiNamespace+e.path,"HEAD"!==e.method&&(l=d(l,"_envelope=1"))):(l+="rest/v"+e.apiVersion+e.path,"HEAD"!==e.method&&(l=d(l,"http_envelope=1"))),e.query)&&(l=d(l,p(e,"query")?e.query.replace(/^&/,""):h(e.query)));var u=new XMLHttpRequest,b=rltGetToken();u.open(e.method||"GET",l),u.responseType="blob"===(e.responseType||"").toLowerCase()?"blob":"json";var w,v=c();e.token?u.setRequestHeader("Authorization","Bearer "+e.token):i(_wpcomProxyToken)?u.setRequestHeader("Authorization","X-WPTOKEN "+_wpcomProxyToken+":"+(r?"1":"0")+":"+a):v?u.setRequestHeader("Authorization","X-WPCOOKIE "+v+":"+(r?"1":"0")+":"+a):b&&u.setRequestHeader("Authorization","X_WPCOM_RLT "+b),e.formData&&e.formData.length>0?w=function(e){var o,t,n,a,r=new FormData;for(o in e.body)e.body.hasOwnProperty(o)&&r.append(o,e.body[o]);for(a=0;a<e.formData.length;a++)"object"==typeof e.formData[a][1]&&void 0!==e.formData[a][1].fileContents?(o=e.formData[a][0],t=e.formData[a][1].fileName,n=new Blob([e.formData[a][1].fileContents],{type:e.formData[a][1].mimeType}),r.append(o,n,t)):r.append(e.formData[a][0],e.formData[a][1]);return r}(e):e.body&&(w=JSON.stringify(e.body),u.setRequestHeader("Content-Type","application/json")),e.headers&&o.forEach((o=>{e.headers[o]&&u.setRequestHeader(o,e.headers[o])})),u.onload=function(){var o,a=D(u);delete t[e.callback],a?(o=JSON.parse(s[e.callback]),delete s[e.callback]):o=f(u),o.code=o.code||o.status||u.status,400===parseInt(o.code)&&b&&(g("c3po.provider.response.400","1","c"),rltInvalidateToken(b)),403===parseInt(o.code)&&b&&g("c3po.provider.response.403","1","c"),m(e.callback,n,e.supports_args)(o,this)},u.onerror=function(){delete t[e.callback],function(e,o,t,n){return function(a){var r,s;r=n?"error":{message:a.statusText},s=n?0:a.status,y(t?[r,s,{},e]:[r,s,e],o)}}(e.callback,n,e.supports_args,!e.supports_error_obj)(this)},u.onreadystatechange=function(){u.readyState===XMLHttpRequest.HEADERS_RECEIVED&&D(u)&&function(e,o){o.responseType="text";let t=0;s[e]=null,o.addEventListener("progress",(({target:o})=>{for(;;){const n=o.response.indexOf("\n",t);if(n<0)break;s[e]=o.response.slice(t,n);const a=JSON.parse(s[e]);if(a.status<200){const o={"Content-Type":"application/x-ndjson;",status:a.status};y([a.body,207,o,e])}t=n+1}}))}(e.callback,u)};const k=!(!e.body||void 0===e.body.stream)&&e.body.stream;e.supports_progress&&!k&&(u.upload.onprogress=function(o){var t={upload:!0,lengthComputable:o.lengthComputable,loaded:o.loaded,total:o.total,callbackId:e.callback};window.parent.postMessage(t,a)},u.onprogress=function(o){var t={download:!0,lengthComputable:o.lengthComputable,loaded:o.loaded,total:o.total,callbackId:e.callback};window.parent.postMessage(t,a)}),t[e.callback]=u,u.send(w)}function g(e,o,t){return(new Image).src='https://pixel.wp.com/boom.gif?v=0.9&u=https://public-api.wordpress.com/pinghub&json={"beacons":["'+e+"."+function(){if(d)return d;var e=!!window.opr&&!!opr.addons||!!window.opera||navigator.userAgent.indexOf(" OPR/")>=0,o="undefined"!=typeof InstallTrigger,t=/^((?!chrome|android).)*safari/i.test(navigator.userAgent),n=!!document.documentMode,a=!n&&!!window.StyleMedia,r=window.chrome&&void 0!==window.chrome&&"Google Inc."===window.navigator.vendor;return d=e?"opera":o?"firefox":t?"safari":n?"msie":a?"edge":r?"chrome":"unknown"}()+":"+o+"|"+t+'"]}',!0}var w={};function v(e){delete w[e]}function k(e,o){if(e in w)o({body:{type:"error",text:"already subscribed"},code:444});else{var t=(new Date).getTime(),n=function(e){let o="wss://public-api.wordpress.com"+e;if(_wpcomProxyTokenPinghub){const e="wordpress_api_pinghub_jwt_"+Math.floor(1e7*(1+9*Math.random())),t=new Date;t.setTime(t.getTime()+3e4),document.cookie=e+"="+_wpcomProxyTokenPinghub+";Expires="+t.toUTCString()+";Secure;SameSite=None;Path=/pinghub;Partitioned",o+="?jwtc="+e}return w[e]=new WebSocket(o)}(e);n.onopen=function(e){g("pinghub.conn_open_dc."+_wpcomProxyRequestDC,(new Date).getTime()-t,"ms"),o({body:{type:"open"},code:207})},n.onclose=function(n){g("pinghub.conn_close_code."+n.code,(new Date).getTime()-t,"ms"),v(e),o({body:{type:"close"},code:200})},n.onerror=function(n){g("pinghub.conn_err",(new Date).getTime()-t,"ms"),v(e),o({body:{type:"error"},code:500})},n.onmessage=function(e){o({body:{type:"message",data:e.data},code:207})}}}var T=!1,_=!1,x=!!_wpcomProxyRequestHasCookie;function P(e,o){var t=m(e.callback,o,e.supports_args);if("undefined"==typeof WebSocket)return T=T||g("pinghub.restproxy.ws_unsupported","1","c"),void t({body:{type:"error",data:"unsupported"},code:500});T=T||g("pinghub.restproxy.ws_supported","1","c"),_=_wpcomProxyTokenPinghub?_||g("pinghub.restproxy.jwt","1","c"):x?_||g("pinghub.restproxy.cookie","1","c"):_||g("pinghub.restproxy.nocookie","1","c"),"connect"===e.action&&k(e.path,t),"disconnect"===e.action&&function(e,o){e in w?(w[e].close(),v(e),o({body:{type:"disconnect"},code:200})):o({body:{type:"error",data:"not connected"},code:200})}(e.path,t),"send"===e.action&&function(e,o,t){if(e in w)try{w[e].send(o),t({body:{type:"sent"},code:201})}catch(e){t({body:{type:"error"},code:600})}else t({body:{type:"error"},code:404})}(e.path,e.message,t)}function j(e){var o,n=!1;if(a===e.origin)if(l)u.push({data:e.data,origin:e.origin});else{if("string"==typeof e.data){try{o=JSON.parse(e.data)}catch(e){return}n=!0}else o=e.data;if("object"==typeof o&&"loginMessage"!==o.type&&"rltMessage"!==o.type){if(o.metaAPI)return void 0!==o.metaAPI.accessAllUsersBlogs&&(r=!!o.metaAPI.accessAllUsersBlogs),void m(o.callback,n)({body:"metaAPIupdated",code:200});var s="/pinghub/";if(o.path&&o.path.slice(0,9)===s)P(o,n);else if(o.abort){if(t[o.callback])t[o.callback].abort()}else b(o,n)}}}function D(e){return/^application[/]x-ndjson($|;)/.test(e.getResponseHeader("Content-Type"))}window.addEventListener("message",j),function(e,o){if(n.indexOf(e)>-1||e.match(/^https:\/\/[a-z0-9-]+\.calypso\.live$/)||e.match(/^https:\/\/([a-z0-9-]+\.)+wordpress\.com$/))o(!0);else{var t=window.location.protocol+"//public-api.wordpress.com/rest/v1/sites/"+encodeURIComponent(e)+"?http_envelope=1&check_wpcom=1",a=new XMLHttpRequest;a.open("GET",t),a.responseType="json";var r=c();i(_wpcomProxyToken)?a.setRequestHeader("Authorization","X-WPTOKEN "+_wpcomProxyToken+":0:"+e):r&&a.setRequestHeader("Authorization","X-WPCOOKIE "+r+":0:"+e),a.onload=function(){var e=!1;if(200===this.status){var t=f(this);t&&t.body&&!0===t.body.isWPcom&&(e=!0)}o(e)},a.send()}}(a,(function(e){if(!e)return window.removeEventListener("message",j),void(u=[]);if(l=!1,u.forEach(j),u=[],_wpcomProxyToken){const e=setInterval((()=>{window.fetch("?refresh",{headers:{authorization:"X-WPTOKEN "+_wpcomProxyToken}}).then((o=>{if(o.ok)for(const[e,t]of o.headers)"x-jwt"===e&&t?_wpcomProxyToken=t:"x-jwt-pinghub"===e&&t&&(_wpcomProxyTokenPinghub=t);else{if(304===o.status)return;401===o.status&&(console.error("REST proxy JWT refresh failed: no valid logged-in session"),_wpcomProxyToken=null,_wpcomProxyTokenPinghub=null,clearInterval(e))}})).catch((e=>{console.error("REST proxy JWT refresh failed:",e)}))}),18e5)}})),y("ready"),y(_wpcomProxyToken||c()?"cookie-auth-ok":"cookie-auth-missing")}();</script>
</head>
<body>
</body></html>