



.jp-related-posts-i2 {
	margin-top: 1.5rem;
}

.jp-related-posts-i2__list {
	--hgap: 1rem;

	display: flex;
	flex-wrap: wrap;
	column-gap: var(--hgap);
	row-gap: 2rem;

	margin: 0;
	padding: 0;

	list-style-type: none;
}

.jp-related-posts-i2__post {
	display: flex;
	flex-direction: column;

	
	flex-basis: calc( ( 100% - var(--hgap) ) / 2 );
}


.jp-related-posts-i2__post:nth-last-child(n+3):first-child,
.jp-related-posts-i2__post:nth-last-child(n+3):first-child ~ * {

	
	flex-basis: calc( ( 100% - var(--hgap) * 2 ) / 3 );
}

.jp-related-posts-i2__post:nth-last-child(4):first-child,
.jp-related-posts-i2__post:nth-last-child(4):first-child ~ * {

	
	flex-basis: calc( ( 100% - var(--hgap) ) / 2 );
}

.jp-related-posts-i2__post-link {
	display: flex;
	flex-direction: column;
	row-gap: 0.5rem;

	width: 100%;
	margin-bottom: 1rem;
	
	line-height: 1.2;
}

.jp-related-posts-i2__post-link:focus-visible {
	outline-offset: 2px;
}

.jp-related-posts-i2__post-img {
	order: -1;

	max-width: 100%;
}

.jp-related-posts-i2__post-defs {
	margin: 0;

	list-style-type: unset;
}


.jp-related-posts-i2__post-defs dt {
	position: absolute;
	width: 1px;
	height: 1px;
	overflow: hidden;
	clip: rect(1px, 1px, 1px, 1px); white-space: nowrap;
}

.jp-related-posts-i2__post-defs dd {
	margin: 0;
}



.jp-relatedposts-i2[data-layout="list"] .jp-related-posts-i2__list {
	display: block;
}

.jp-relatedposts-i2[data-layout="list"] .jp-related-posts-i2__post {
	margin-bottom: 2rem;
}



@media only screen and (max-width: 640px) {

	.jp-related-posts-i2__list {
		display: block;
	}

	.jp-related-posts-i2__post {
		margin-bottom: 2rem;
	}
}



#jp-relatedposts {
	display: none;
	padding-top: 1em;
	margin: 1em 0;
	position: relative;
	clear: both;
}

.jp-relatedposts::after {
	content: '';
	display: block;
	clear: both;
}



#jp-relatedposts h3.jp-relatedposts-headline {
	margin: 0 0 1em 0;
	display: inline-block;
	float: left;
	font-size: 9pt;
	font-weight: 700;
	font-family: inherit;
}

#jp-relatedposts h3.jp-relatedposts-headline em::before {
	content: "";
	display: block;
	width: 100%;
	min-width: 30px;
	border-top: 1px solid rgba(0,0,0,.2);
	margin-bottom: 1em;
}

#jp-relatedposts h3.jp-relatedposts-headline em {
	font-style: normal;
	font-weight: 700;
}



#jp-relatedposts .jp-relatedposts-items {
	clear: left;
}

#jp-relatedposts .jp-relatedposts-items-visual {
	margin-right: -20px;
}



#jp-relatedposts .jp-relatedposts-items .jp-relatedposts-post {
	float: left;
	width: 33%;
	margin: 0 0 1em; 
	box-sizing: border-box;
}

#jp-relatedposts .jp-relatedposts-items-visual .jp-relatedposts-post {
	padding-right: 20px;
	filter: alpha(opacity=80);
	-moz-opacity: .8;
	opacity: .8;
}

#jp-relatedposts .jp-relatedposts-items .jp-relatedposts-post:nth-child(3n+4),
#jp-relatedposts .jp-relatedposts-items-visual .jp-relatedposts-post:nth-child(3n+4) {
	clear: both;
}

#jp-relatedposts .jp-relatedposts-items .jp-relatedposts-post:hover .jp-relatedposts-post-title a {
	text-decoration: underline;
}

#jp-relatedposts .jp-relatedposts-items .jp-relatedposts-post:hover {
	filter: alpha(opacity=100);
	-moz-opacity: 1;
	opacity: 1;
}



#jp-relatedposts .jp-relatedposts-items-visual h4.jp-relatedposts-post-title,
#jp-relatedposts .jp-relatedposts-items p,
#jp-relatedposts .jp-relatedposts-items time {
	font-size: 14px;
	line-height: 20px;
	margin: 0;
}

#jp-relatedposts .jp-relatedposts-items-visual .jp-relatedposts-post-nothumbs {
	position:relative;
}

#jp-relatedposts .jp-relatedposts-items-visual .jp-relatedposts-post-nothumbs a.jp-relatedposts-post-aoverlay {
	position:absolute;
	top:0;
	bottom:0;
	left:0;
	right:0;
	display:block;
	border-bottom: 0;
}

#jp-relatedposts .jp-relatedposts-items p,
#jp-relatedposts .jp-relatedposts-items time {
	margin-bottom: 0;
}

#jp-relatedposts .jp-relatedposts-items-visual h4.jp-relatedposts-post-title {
	text-transform: none;
	margin: 0;
	font-family: inherit;
	display: block;
	max-width: 100%;
}

#jp-relatedposts .jp-relatedposts-items .jp-relatedposts-post .jp-relatedposts-post-title a {
	font-size: inherit;
	font-weight: 400;
	text-decoration: none;
	filter: alpha(opacity=100);
	-moz-opacity: 1;
	opacity: 1;
}

#jp-relatedposts .jp-relatedposts-items .jp-relatedposts-post .jp-relatedposts-post-title a:hover {
	text-decoration: underline;
}

#jp-relatedposts .jp-relatedposts-items .jp-relatedposts-post img.jp-relatedposts-post-img,
#jp-relatedposts .jp-relatedposts-items .jp-relatedposts-post span {
	display: block;
	max-width: 90%;
	overflow: hidden;
	text-overflow: ellipsis;
}

#jp-relatedposts .jp-relatedposts-items-visual .jp-relatedposts-post img.jp-relatedposts-post-img,
#jp-relatedposts .jp-relatedposts-items-visual .jp-relatedposts-post span {
	height: auto;
	max-width: 100%;
}

#jp-relatedposts .jp-relatedposts-items .jp-relatedposts-post .jp-relatedposts-post-date,
#jp-relatedposts .jp-relatedposts-items .jp-relatedposts-post .jp-relatedposts-post-context {
	opacity: .6;
}


.jp-relatedposts-items .jp-relatedposts-post .jp-relatedposts-post-date {
	display: none;
}


#jp-relatedposts .jp-relatedposts-items-visual div.jp-relatedposts-post-thumbs p.jp-relatedposts-post-excerpt {
	display: none;
}


#jp-relatedposts .jp-relatedposts-items-visual .jp-relatedposts-post-nothumbs p.jp-relatedposts-post-excerpt {
	overflow: hidden;
}

#jp-relatedposts .jp-relatedposts-items-visual .jp-relatedposts-post-nothumbs span {
	margin-bottom: 1em;
}


#jp-relatedposts .jp-relatedposts-list .jp-relatedposts-post {
	clear: both;
	width: 100%;
}

#jp-relatedposts .jp-relatedposts-list .jp-relatedposts-post img.jp-relatedposts-post-img {
	float: left;
	overflow: hidden;
	max-width: 33%;
	margin-right: 3%;
}

#jp-relatedposts .jp-relatedposts-list h4.jp-relatedposts-post-title {
	display: inline-block;
	max-width: 63%;
}



@media only screen and (max-width: 640px) {

	#jp-relatedposts .jp-relatedposts-items .jp-relatedposts-post {
		width: 50%;
	}

	#jp-relatedposts .jp-relatedposts-items .jp-relatedposts-post:nth-child(3n) {
		clear: left;
	}

	#jp-relatedposts .jp-relatedposts-items-visual {
		margin-right: 20px;
	}
}

@media only screen and (max-width: 320px) {

	#jp-relatedposts .jp-relatedposts-items .jp-relatedposts-post {
		width: 100%;
		clear: both;
		margin: 0 0 1em;
	}

	#jp-relatedposts .jp-relatedposts-list .jp-relatedposts-post img.jp-relatedposts-post-img,
	#jp-relatedposts .jp-relatedposts-list h4.jp-relatedposts-post-title {
		float: none;
		max-width: 100%;
		margin-right: 0;
	}
}



@media print {

	.jp-relatedposts {
		display:none !important;
	}
}


#jp-post-flair {
	padding-top: .5em;
}


div.sharedaddy,
#content div.sharedaddy,
#main div.sharedaddy {
	clear: both;
}

div.sharedaddy h3.sd-title {
	margin: 0 0 1em 0;
	display: inline-block;
	line-height: 1.2;
	font-size: 9pt;
	font-weight: bold;
}

div.sharedaddy h3.sd-title:before {
	content: "";
	display: block;
	width: 100%;
	min-width: 30px;
	border-top: 1px solid #dcdcde;
	margin-bottom: 1em;
}



div.jetpack-likes-widget-wrapper {
	width: 100%;
	min-height: 50px;	
	position: relative; 
}

div.jetpack-likes-widget-wrapper .sd-link-color {
	font-size: 12px;
}

div.jetpack-comment-likes-widget-wrapper {
	width: 100%;
	position: relative;
	min-height: 31px;
}

div.jetpack-comment-likes-widget-wrapper iframe {
	margin-bottom: 0;
}

#likes-other-gravatars {
	display: none;
	position: absolute;
	padding: 9px 12px 10px 12px;
	background-color: #fff;
	border: solid 1px #dcdcde;
	border-radius: 4px;
	min-width: 220px;
	max-height: 240px;
	height: auto;
	overflow: auto;
	z-index: 1000;
}

#likes-other-gravatars * {
	line-height: normal;
}

#likes-other-gravatars .likes-text {
	color: #101517;
	font-size: 12px;
	font-weight: 500;
	padding-bottom: 8px;
}

#likes-other-gravatars ul,
#likes-other-gravatars li {
	margin: 0;
	padding: 0;
	text-indent: 0;
	list-style-type: none;
}

#likes-other-gravatars li::before {
	content: "";
}

#likes-other-gravatars ul.wpl-avatars {
	overflow: hidden;
	display: block;
}

#likes-other-gravatars ul.wpl-avatars li {
	width: 196px;
	height: 28px;
	float: none;
	margin: 0 0 4px 0;
}

#likes-other-gravatars ul.wpl-avatars li:last-child {
	margin: 0;
}

#likes-other-gravatars ul.wpl-avatars li a {
	margin: 0 2px 0 0;
	border-bottom: none !important;
	display: flex;
	align-items: center;
	gap: 8px;
	text-decoration: none;
}

#likes-other-gravatars ul.wpl-avatars li a span {
	font-size: 12px;
	color: #2C3338;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

#likes-other-gravatars ul.wpl-avatars li a img {
	background: none;
	border: none;
	border-radius: 50%;
	margin: 0 !important;
	padding: 1px !important;
	position: static;
	box-sizing: border-box;
}

div.sd-box {
	border-top: 1px solid #dcdcde;
	border-top: 1px solid rgba(0,0,0,.13);
}

.entry-content .post-likes-widget, .post-likes-widget,
.comment-likes-widget {
	margin: 0;
	border-width: 0;
}


.post-likes-widget-placeholder,
.comment-likes-widget-placeholder {
	margin: 0;
	border-width: 0;
	position: relative;
}

.jetpack-likes-widget-unloaded .likes-widget-placeholder,
.jetpack-likes-widget-loading .likes-widget-placeholder,
.jetpack-likes-widget-loaded iframe {
	display: block;
}

.jetpack-likes-widget-loaded .likes-widget-placeholder,
.jetpack-likes-widget-unloaded iframe,
.jetpack-likes-widget-loading iframe {
	display: none;
}

.comment-likes-widget-placeholder {
	height: 18px;
	position: absolute;
	display: flex;
	font-family: -apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif;
}

.comment-likes-widget-placeholder::before {
	color: #2EA2CC;
	width: 16px;
	height: 16px;
	content: '';
	display: inline-block;
	position: relative;
	top: 3px;
	padding-right: 5px;
	background-repeat: no-repeat;
	background-size: 16px 16px;
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Crect x='0' fill='none' width='24' height='24'/%3E%3Cg%3E%3Cpath fill='%232EA2CC' d='M12 2l2.582 6.953L22 9.257l-5.822 4.602L18.18 21 12 16.89 5.82 21l2.002-7.14L2 9.256l7.418-.304'/%3E%3C/g%3E%3C/svg%3E");
}

.post-likes-widget-placeholder .button {
	display: none;	
}

.post-likes-widget-placeholder .loading,
.comment-likes-widget-placeholder .loading {
	color: #999;
	font-size: 12px;
}

.comment-likes-widget-placeholder .loading {
	padding-left: 5px;
	margin-top: 4px;
	align-self: center;
	color: #4E4E4E;
}


div.sharedaddy.sd-like-enabled .sd-like h3 {
	display: none;
}

div.sharedaddy.sd-like-enabled .sd-like .post-likes-widget {
	width: 100%;
	float: none;
	position: absolute; 
	top: 0;
}

.comment-likes-widget {
	width: 100%;
}



.pd-rating,
.cs-rating {
	display: block !important;
}



.sd-gplus .sd-title {
	display: none;
}

@media print {
	.jetpack-likes-widget-wrapper {
		display: none;
	}
}
