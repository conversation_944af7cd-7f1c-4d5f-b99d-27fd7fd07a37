# 11_PoWAGT_Individual_DB_text_files

c:\c\projects\family_history_wa\21_People_of_Western_Australias_Ghost_Towns\03_PoWAGT_Resources\40-PoWAGT_ICTAV\51_PoWAGT_Code\11_PoWAGT_Individual_DB_text_files\


## Create Individual Files
### Prompt 2025-06-20 15:03:07
@catalog_export_all_entries_20250620t141253_I2QO.json write me a python program which reads the json file mentioned (catalog_export_all_entries_20250620t141253_I2QO.json) and reads each item in the "objects.rows" key - it is a list of items and each item is a list of fields. read each item (getting back the list of fields) and then create a text file in the subfolder called "PoWAGT_DB_Entries_as_Individual_Files".  the text file is called nnnnnn.txt where the nnnnnn is the number which is the first item in the list of fields in the item you just read (ie it is field 1). Pad the number with leading 0's so it might read as 000002, 000038, 004285 etc etc.  Just save the list of fields in the text file, with CRLF as per the list which means that there will be >50 lines in each text file.  Include all the empty fields as "" on a line by themselves.

