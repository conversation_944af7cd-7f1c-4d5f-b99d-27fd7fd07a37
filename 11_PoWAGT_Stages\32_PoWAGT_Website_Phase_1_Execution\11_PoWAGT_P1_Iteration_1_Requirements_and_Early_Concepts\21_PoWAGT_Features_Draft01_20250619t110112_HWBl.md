# PoWAGT Features



The central concept is that the site consists of a set of stories.
These stories may have been told by somebody, or they may be a remembered story (as recounted by somebody), or the story may be a creation by somebody based on some research or historical information that a person has uncovered and created the story about.

Each story can be considered as a set of statements which have some particular meaning associated with the story. These individual statements could be characterised as being an assertion of some facts or a fact.

The representation of the assertion of fact is that each statement is related to one or more reference values from different entities which describe or represent elements of that statement.

For the purposes of this system these entities are: People; Places; and Time; sources; artefacts.
Thus, a statement may be talking about one or more people that are associated with a particular place at a particular time. this statement is attested to by some source material. There may be a number of artefacts comments such as supporting documents, or photos, etc which relate to the particular statement. The statement may be about the document or the photo for instance close parent.

A story will typically have one or more statements embedded in it. each of those statements will have associations with one or more people, place, time, source and artefact records.


It should be always remembered that statements or assertions are not necessarily 100% veracity, and indeed one could always consider a statement as having a percentage of veracity associated with it. The more evidence there is for the statement of the assertion from different sources and possibly different people or concurring may increase the veracity of the statement. alternatively different statements which conflict in some way may reduce the veracity of the statement.



Access the System
    Present Ghosts landing page
        Provide menus and links to other pages
    Allow login to enter and edit information
        Present Ghosts Maintenance page, allowing access to maintain various information, with Story entry/updating as central
Gather and Maintain Information
    ?? Maintain different types of information: e.g. genealogical, historical, demographic and geographical
    ** Maintain Stories
        (this is likely the Catalog table in the current prototype system)
        Story properties: Text; Notes;
        Story relationships: to Statement (1-m a story can have 1 or more statements about the story)
        ** The original requirements specific a Duplicate Check as:
                Before adding a new record, or updating a record ensure the record is unique. A record is considered to be duplicate if a record already exists with the identical data elements in columns:
                    •     Community Tag
                    •     Family Name
                    •     Given Names
                    •     Known As
                    •     Fact
                    •     Fact Date
                    •     SN
                    •     Citation Page
            Uncertain whether this is still valid, especially as a precise check, and especially as multiple people may reference the same "story" but from different perspectives. Possibly implement a system to retrieve similar records during the proof-reading/verification process, allowing the verifier to determine whether to mark the new "story" as duplicate and archive it, and/or to place some details from the new "story" into the previous story/stories.
        When a story has been created anew, or edited, invoke the workflow associated with new stories, or edited stories (which enforces proof-reading/verification etc)
    ** Maintain Statements/Assertions (name as Statement)
        Statement properties: Text; Notes;
        Statement relationships:
            to Place/Community (m-m) - typed, various types
            to People/Person (m-m) - typed, various types
            to Time (m-m) - typed, various types - most usually 1-m
            to Source (m-m) - typed, various types - typically 1-m
                It is the set of typed relationships to a statement that constitute the "specification" of the fact being asserted.
        Create Statement by highlighting text in a story (double click to highlight a sentence) and creating new statement.
            Parse the Statement for entities (people, places) and times to attempt to auto-create relationships
        Add relationships to the statement (People, Place, Time, Source)
            Find target records in related table (People, Place, Time, Source) by specifying values or wildcards for combinations of the properties: ObjectID; Type; Name; Title; Date
                Display list of matching records in the target table and allow selection of one or more (and allow to return without a selection)
                Allow navigation to enter new record in the target table if not found/available, then return with the new record selected
            In many circumstances, the same relationships to People, Place, Time, Source will be re-used for additional statements. Thus, recently used relationship records (say last 5 - set as a parameter) should be displayed in the lookup list.
    Maintain Community details  (??? is Community really just Place in a broader sense)
        Community properties: Name;
        Community relationships: 
            Place as LGA;
            Place as Administrative Region;
            ** Type of Relationship to Place (e.g. Residence)
    Maintain People/Persons details
        People properties: Family Name; Given Names; Also Known As; Phonetic Name; Gender; DoB; DoD; Significant Date; Nationality; Occupations; Biography; Notes;
        Relationships: Community (m-m); Source (m-m); Place (m-m hierarchy)
        Allow Places to relate to each other in an hierarchy (e.g. a Town as a place is located within an LGA which is located within an Administrative Region)
        Link Persons to a Community at a particular time or time period according to a source citation
        Link Story to Person to Community at a particular time or time period
    Maintain Place details
        Place properties: Community Name; Traditional Name; Historic Names; Phonetic Name; Place Type; Notes;
            Maintain demographic and historical profiles of places (see properties above)
            Maintain different types of places: e.g. ghost towns, abandoned towns/settlements (see Place Type property above)
        Place relationships:
            ** Relate Places to Locations (m-m) - typed
            ** Relate Places to Addresses (m-m) - typed. ?? use Location ??
            ** Relate Places to Time (m-m) - typed - type to represent Place Existed From/Started; type to represent Place Existed To/Ended; 
            ** Relate Places to Statements (and hence Story) (m-m) - typed
                    Was Communities in the original requirements.
        Maintain Locations (geographic point or geospatial polygon to locate a Place)
            Location properties: Name; Geographic Point or Geospatial Polygon; What3Words;
                A Geospatial Polygon is a set of 3 or more Geographic Points.
                A Geographic Point is a Latitude/Longitude pair.
        ** Maintain addresses
            Address properties: Line 1; Line 2; Suburb; City; State; Postcode; Country;
            Address relationships: to Location (1-m) - typed
                Typing of address relationships can indicate Home Address; Business Address; Postal Address; etc.
            ** Associate an address with a geographic Location
    Maintain Time
        ** Store time records as independent records, to be related to other entities, since statements etc can refer to multiple times.
        Time properties: Time From; Time To (can be null); Epoch (can be null); 
            Time properties above can be specified as a precise ISO datetime, or as YYYYMMDD or as YYYYMM or as YYYY
            Epoch property is a string, referencing a named period, such as "Pre White Settlement", "Post Federation" etc.
    Maintain Sources (in relation to Information stored)
        Source properties: Source Type; Source SN; Call Number; Barcode; Source Title; Source Notes; Volume; Issue; Pages; URL; Access Date
        Source Relationships:
            to People/Person as Author of Source (typed)
            to Time for Publication Date/Year (typed);
            to Place for Publication City/Location (typed);
        Provide citations for sources (e.g. enter Source record details)
    Maintain Registers
        Maintain Register of Administrative Regions
            ** These are just Places - identified as Type of Place
        Maintain Register of Local Government Authorities (LGAs)
            ** These are just Places - identified as Type of Place
        Maintain Register of Repositories (holding Sources)
            ** These are just Places - identified as Type of Place
        Maintain Register of Industries
            Preload with Australian standard industry codes and names but allow additions and changes to be made
    Create and Maintain Blog entries
    Maintain work and action queues
        Maintain workflow processes for different types of work/action management
        Accept a work/action entry into a queue
        Allocate work/action to user and instantiate selected workflow process
        Manage the workflow, including forwarding of actions, notifications, bring-ups, completions and reporting.
    Maintain Verifications
        Maintain details of verification, including personnel who have verified/proofread entries, notes/comments, etc, associated with each record (?? story).
        Link verification activity to action in workflow
        Allow multiple verifications.
        Do not allow personnel who entered the data to immediately perform (subsequent) verification on that data, but allow them to verify/proof-read if there has been an intervening verification. Do not allow personnel who have verified data to immediately verify again, but can verify after an intervening verification.

Publish Information
    Publish Information to website
        Website to be available to the general public
    Display Lists
        Allow sorting of displayed list by any of the fields displayed, ascending or descending
        Allow filtering of rows in the displayed list for any field displayed - allowing a range of values or wildcards
        Display Alphabetical List of Communities
        Display List of Communities by Administrative Region
        Display List of Communities by Local Government Authority (LGA)
    Display Community page(s)
        Link Community pages together (in a navigation workflow)
        Display Information for Community based on selected DisplayTemplate
    Define Templates for display of information
        Information structures include historical information, demographic information
    Search for Information
        Provide structured search against multiple fields (properties) across tables (entities), allowing for wildcard searches within fields
            Provide different search screen templates, specifying different sets of fields to search on
        Provide full text search across the whole database, with text from all fields/properties searchable, including Text and Note properties.
        Provide vector-based similarity search across all records/fields
            Automatically produce a textual representation of the entered information for vector and LLM purposes.
    Display information/pages according to standard templates (layout, colours, etc)
        Templates to conform to accessibility guidelines
        Templates should have a high visibility option
        Provide templates which show different summaries of the information found, to be displayed.  Allow selection of different template to display against the same record (do not need to search again etc).
    Allow Suggestions and Comments to be lodged
        Community pages can have comments recorded by members of the public
        Comments facilities should be able to ignore/manage spam and other "bad actor" attempts
        Comments should be queued to site system administrators for review prior to display
        System administrators can block IP addresses from making comments
        Comments with suggestions for changes or improvements to be placed into a queue for action

Monetise the System
    ??? ???

Manage the System
    Control Access to the Information Gathering and Maintenance facility
        Register Users
        Import User details from external system - Membership system
        Integrate with external SSO provider
        Maintain user roles and access control details
        Implement Two Factor Authentication
    Screen management
        Pages should be responsive and SPA
        When displaying rows of data for display/edit purposes, provide a menu (kebab menu/vertical ellipsis) or icons (or other means) to manage further actions which can be taken on the data/row. Actions may include: View details of record; Proof-read/verify record; Update record; Delete record; View metadata on record, including all verification activities.
        When displaying a screen as a list of records or details of a record, provide icons/buttons for: Create new record; Refresh the display (list or details); Finish and return to previous screen or home page.
    Data management
        Maintain metadata (a la Dublin Core) for every record/row in the database
            Metadata is automatically added to records when added or edited.
        Do not actually delete records, but rather, archive them and do not participate in searches or display unless specifically request to see archived records.
        Do not modify records in place when updating, but rather archive the original then modify the record.
    Database management
        Retrieve 'n' records per query and allow paging thereafter
            'n' records to be set as a system/project parameter
        All records are uniquely identified by an UUID as the ObjectID primary key for a record.
        ObjectIDs are used as foreign keys in relationships.
    Maintain Projects
        Maintain details about projects run by FHWA
        Maintain parameters to control system operation and inform display etc, on a per project basis
            Maintain system defaults to load into projects (inheritance)
    Ensure performance of the system
        Searches for a specific community page according to 3 defined criteria to be within 1 second

