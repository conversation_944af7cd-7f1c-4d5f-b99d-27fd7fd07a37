!function(){"use strict";var n=function(n,t,e,i){var o,r;return function(u){t.value>=0&&(u||i)&&((r=t.value-(null!=o?o:0))||void 0===o)&&(o=t.value,t.delta=r,t.rating=function(n,t){return n>t[1]?"poor":n>t[0]?"needs-improvement":"good"}(t.value,e),n(t))}},t=-1,e=function(n){addEventListener("pageshow",(function(e){e.persisted&&(t=e.timeStamp,n(e))}),!0)},i=function(){var n=performance.getEntriesByType("navigation")[0];if(n&&n.responseStart>0&&n.responseStart<performance.now())return n},o=function(){var n,t=i();return null!==(n=null==t?void 0:t.activationStart)&&void 0!==n?n:0},r=function(n,e){void 0===e&&(e=-1);var r=i(),u="navigate";t>=0?u="back-forward-cache":r&&(document.prerendering||o()>0?u="prerender":document.wasDiscarded?u="restore":r.type&&(u=r.type.replace(/_/g,"-")));return{name:n,value:e,rating:"good",delta:0,entries:[],id:"v5-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:u}},u=function(n){document.prerendering?addEventListener("prerenderingchange",(function(){return n()}),!0):n()},c=[800,1800],a=function(n){document.prerendering?u((function(){return a(n)})):"complete"!==document.readyState?addEventListener("load",(function(){return a(n)}),!0):setTimeout(n)};function f(n,t){for(var e=n||{},i=0;i<t.length;i++)e=void 0===e?e:e[t[i]];return e}var d={};function v(n,t){return f(d.t,["dataset",n])||t}function s(n){var t=v(n,"")||[];return"string"==typeof t?t.split(","):t}function h(){return function(n){var t;if(n)try{var e={},i=JSON.parse(n);for(var o in i||{})"string"==typeof i[o]&&(e[o]=i[o]);Object.keys(e).length&&(t=JSON.stringify(e))}catch(n){}return t}(f(d.t,["dataset","customProps"])||f(d.t,["dataset","customproperties"]))}var m,l,p,g,b={},y=36e5,w=!1;function M(n){g="function"==typeof fetchLater?"fetchLater":"function"==typeof navigator.sendBeacon?"sendBeacon":"image";var t,e,i=v("forceReporter");i&&(g=i),b.reporter=g,"fetchLater"!==g&&(t=C,e=function(){"hidden"===document.visibilityState&&(document.removeEventListener("visibilitychange",e),t())},document.addEventListener("visibilitychange",e),setTimeout(C,y)),p=n}function L(){"fetchLater"===g&&function(){if(w)return;if(m&&m.activated)return w=!0,void(p&&p());l&&l.abort();!function(){var n=E();if(!n)return;l=new AbortController,m=fetchLater(n,{method:"GET",signal:l.signal,activateAfter:y})}()}()}function E(){var n="";if(void 0===b.ttfb)return n;for(var t in b)void 0!==b[t]&&(n+="&"+t+"="+encodeURIComponent(b[t]));return n?"https://pixel.wp.com/boom.gif?bilmur=1"+n:null}function C(){if(!w){w=!0;var n=E();if(n){if("image"===g)(new Image).src=n;"sendBeacon"===g&&navigator.sendBeacon(n)}p&&p()}}var S=!0;function T(){!function(t,u){void 0===u&&(u={});var f=r("TTFB"),d=n(t,f,c,u.reportAllChanges);a((function(){var a=i();a&&(f.value=Math.max(a.responseStart-o(),0),f.entries=[a],d(!0),e((function(){f=r("TTFB",0),(d=n(t,f,c,u.reportAllChanges))(!0)})))}))}((function(n){S&&(b.ttfb=Math.round(n.value),b.nav_type=n.navigationType,L())}))}var O=function(n){requestAnimationFrame((function(){return requestAnimationFrame((function(){return n()}))}))},P=-1,k=function(){return"hidden"!==document.visibilityState||document.prerendering?1/0:0},I=function(n){"hidden"===document.visibilityState&&P>-1&&(P="visibilitychange"===n.type?n.timeStamp:0,F())},A=function(){addEventListener("visibilitychange",I,!0),addEventListener("prerenderingchange",I,!0)},F=function(){removeEventListener("visibilitychange",I,!0),removeEventListener("prerenderingchange",I,!0)},_=function(){var n;if(P<0){var t=o(),i=document.prerendering||null===(n=window.performance.getEntriesByType("visibility-state").filter((function(n){return"hidden"===n.name&&n.startTime>t}))[0])||void 0===n?void 0:n.startTime;P=null!=i?i:k(),A(),e((function(){setTimeout((function(){P=k(),A()}))}))}return{get firstHiddenTime(){return P}}},N=function(n,t,e){void 0===e&&(e={});try{if(PerformanceObserver.supportedEntryTypes.includes(n)){var i=new PerformanceObserver((function(n){Promise.resolve().then((function(){t(n.getEntries())}))}));return i.observe(Object.assign({type:n,buffered:!0},e||{})),i}}catch(n){}},j=[1800,3e3],B=function(t,i){void 0===i&&(i={}),u((function(){var u,c=_(),a=r("FCP"),f=N("paint",(function(n){n.forEach((function(n){"first-contentful-paint"===n.name&&(f.disconnect(),n.startTime<c.firstHiddenTime&&(a.value=Math.max(n.startTime-o(),0),a.entries.push(n),u(!0)))}))}));f&&(u=n(t,a,j,i.reportAllChanges),e((function(e){a=r("FCP"),u=n(t,a,j,i.reportAllChanges),O((function(){a.value=performance.now()-e.timeStamp,u(!0)}))})))}))},D=!0;function J(){B((function(n){if(D){b.fcp=Math.round(n.value);var t=n.entries[n.entries.length-1],e=t&&t.startTime;void 0!==e&&(b.fcp_raw=Math.round(e)),(performance.getEntriesByType("paint")||[]).forEach((function(n){"first-paint"===n.name&&(b.fp_raw=Math.round(n.startTime))})),L()}}))}var R=new WeakMap;function q(n,t){return R.get(n)||R.set(n,new t),R.get(n)}var x=function(){function n(){this.i=0,this.o=[]}return n.prototype.u=function(n){var t;if(!n.hadRecentInput){var e=this.o[0],i=this.o.at(-1);this.i&&e&&i&&n.startTime-i.startTime<1e3&&n.startTime-e.startTime<5e3?(this.i+=n.value,this.o.push(n)):(this.i=n.value,this.o=[n]),null===(t=this.v)||void 0===t||t.call(this,n)}},n}(),H=function(n){var t=!1;return function(){t||(n(),t=!0)}},W=[.1,.25],z=!0;function G(){var t,i;t=function(n){z&&(b.cls=Math.round(1e3*n.value)/1e3,L())},void 0===(i={reportAllChanges:!0})&&(i={}),B(H((function(){var o,u=r("CLS",0),c=q(i,x),a=function(n){n.forEach((function(n){c.u(n)})),c.i>u.value&&(u.value=c.i,u.entries=c.o,o())},f=N("layout-shift",a);f&&(o=n(t,u,W,i.reportAllChanges),document.addEventListener("visibilitychange",(function(){"hidden"===document.visibilityState&&(a(f.takeRecords()),o(!0))})),e((function(){c.i=0,u=r("CLS",0),o=n(t,u,W,i.reportAllChanges),O((function(){return o()}))})),setTimeout(o))})))}var U,K=0,Q=1/0,V=0,X=function(n){n.forEach((function(n){n.interactionId&&(Q=Math.min(Q,n.interactionId),V=Math.max(V,n.interactionId),K=V?(V-Q)/7+1:0)}))},Y=function(){var n;return U?K:null!==(n=performance.interactionCount)&&void 0!==n?n:0},Z=function(){"interactionCount"in performance||U||(U=N("event",X,{type:"event",buffered:!0,durationThreshold:0}))},$=0,nn=function(){function n(){this.h=[],this.m=new Map}return n.prototype.l=function(){$=Y(),this.h.length=0,this.m.clear()},n.prototype.p=function(){var n=Math.min(this.h.length-1,Math.floor((Y()-$)/50));return this.h[n]},n.prototype.u=function(n){var t,e,i=this;if(null===(t=this.M)||void 0===t||t.call(this,n),n.interactionId||"first-input"===n.entryType){var o=this.h.at(-1),r=this.m.get(n.interactionId);if(r||this.h.length<10||n.duration>o.L){if(r?n.duration>r.L?(r.entries=[n],r.L=n.duration):n.duration===r.L&&n.startTime===r.entries[0].startTime&&r.entries.push(n):(r={id:n.interactionId,entries:[n],L:n.duration},this.m.set(r.id,r),this.h.push(r)),this.h.sort((function(n,t){return t.L-n.L})),this.h.length>10)this.h.splice(10).forEach((function(n){i.m.delete(n.id)}));null===(e=this.C)||void 0===e||e.call(this,r)}}},n}(),tn=function(n){var t=window.requestIdleCallback||setTimeout;"hidden"===document.visibilityState?n():(n=H(n),document.addEventListener("visibilitychange",n,{once:!0}),t((function(){n(),document.removeEventListener("visibilitychange",n)})))},en=[200,500],on=!0;function rn(){var t,i;t=function(n){on&&(b.inp=Math.round(n.value),L())},void 0===(i={reportAllChanges:!0})&&(i={}),window.PerformanceEventTiming&&"interactionId"in PerformanceEventTiming.prototype&&u((function(){var o;Z();var u,c=r("INP"),a=q(i,nn),f=function(n){tn((function(){n.forEach((function(n){a.u(n)}));var t=a.p();t&&t.L!==c.value&&(c.value=t.L,c.entries=t.entries,u())}))},d=N("event",f,{durationThreshold:null!==(o=i.durationThreshold)&&void 0!==o?o:40});u=n(t,c,en,i.reportAllChanges),d&&(d.observe({type:"first-input",buffered:!0}),document.addEventListener("visibilitychange",(function(){"hidden"===document.visibilityState&&(f(d.takeRecords()),u(!0))})),e((function(){a.l(),c=r("INP"),u=n(t,c,en,i.reportAllChanges)})))}))}var un=function(){function n(){}return n.prototype.u=function(n){var t;null===(t=this.M)||void 0===t||t.call(this,n)},n}(),cn=[2500,4e3],an=!0;function fn(){var t,i;t=function(n){if(an){b.lcp=Math.round(n.value);var t=n.entries[n.entries.length-1],e=t&&t.startTime;void 0!==e&&(b.lcp_raw=Math.round(e)),L()}},void 0===(i={reportAllChanges:!0})&&(i={}),u((function(){var u,c=_(),a=r("LCP"),f=q(i,un),d=function(n){i.reportAllChanges||(n=n.slice(-1)),n.forEach((function(n){f.u(n),n.startTime<c.firstHiddenTime&&(a.value=Math.max(n.startTime-o(),0),a.entries=[n],u())}))},v=N("largest-contentful-paint",d);if(v){u=n(t,a,cn,i.reportAllChanges);var s=H((function(){d(v.takeRecords()),v.disconnect(),u(!0)}));["keydown","click","visibilitychange"].forEach((function(n){addEventListener(n,(function(){return tn(s)}),{capture:!0,once:!0})})),e((function(e){a=r("LCP"),u=n(t,a,cn,i.reportAllChanges),O((function(){a.value=performance.now()-e.timeStamp,u(!0)}))}))}}))}var dn,vn,sn,hn,mn,ln=["unloadEventStart","unloadEventEnd","redirectStart","redirectEnd","fetchStart","domainLookupStart","domainLookupEnd","connectStart","connectEnd","secureConnectionStart","requestStart","responseStart","responseEnd","domLoading","domInteractive","domContentLoadedEventStart","domContentLoadedEventEnd","domComplete","loadEventStart","loadEventEnd"],pn=["activationStart","redirectCount","firstInterimResponseStart","finalResponseHeadersStart"],gn="--";function bn(){sn=f(performance,["timing"]),hn=sn&&performance.getEntriesByType("navigation"),mn=hn&&hn[0]?hn[0]:{}}function yn(){bn();var n={};if(!(hn&&mn.responseStart<0||mn.responseStart>performance.now())){var t=0===mn.startTime?2:1,e=b.nt;b.nt=t+gn,ln.forEach((function(t){n[t]=function(n){if("number"==typeof sn[n]&&"number"==typeof sn.navigationStart&&!(sn[n]<=0||sn.navigationStart<=0)){var t=sn[n]-sn.navigationStart;return t>=0?t:void 0}}(t)})),t>1&&"number"==typeof mn.secureConnectionStart&&mn.secureConnectionStart>0&&(n.secureConnectionStart=Math.floor(mn.secureConnectionStart)),b.nt+=i(ln),b.nt+=gn,t>1&&(pn.forEach((function(t){var e=mn[t];n[t]=e?Math.floor(e):e})),b.nt+=i(pn)),b.nt+=gn,t>1&&(b.nt+=void 0===mn.nextHopProtocol?"":mn.nextHopProtocol),b.nt!==e&&L()}function i(t){var e="";return t.forEach((function(t,i){i>0&&(e+="_");var o=n[t];e+=void 0===o?"":o.toString(36)})),e}}function wn(){bn(),sn&&sn.navigationStart&&(!function(){var n=mn.serverTiming;if(n){var t=n.findIndex((function(n){return"a8c-cdn"===n.name}));if(!(t<0||n.length<t+2)&&("dc"===n[t+1].name&&(b.edge_dc=n[t+1].description),"cache"===n[t+2].name)){b.edge_status=n[t+2].description;var e=parseFloat(n[t+2].duration);e>=0&&(b.edge_dur=e)}}}(),yn(),dn=document.addEventListener("DOMContentLoaded",(function(){return setTimeout(yn)})),vn=addEventListener("load",(function(){return setTimeout(yn)})))}function Mn(n,t){var e=new PerformanceObserver((function(n){for(var e=n.getEntries(),i=0;i<e.length;i++)t(e[i])}));return e.observe({type:n,buffered:!0}),function(){e&&(e.disconnect(),e=null)}}var Ln,En,Cn,Sn,Tn={},On={};function Pn(n,t,e){return e.some((function(e){return 0===n.indexOf(e)||0===t.indexOf(e)}))}function kn(n){var t=!1,e=function(n){return n.replace(/^\d/,"_").replace(/\W/g,"_")}(n.name);"mark"===n.entryType?Pn(e,n.name,Cn)&&(Tn[e]=Math.round(n.startTime)||0,t=!0):Pn(e,n.name,Sn)&&(On[e]=Math.round(n.duration)||0,t=!0),t&&(Object.keys(Tn).length&&(b.marks=JSON.stringify(Tn)),Object.keys(On).length&&(b.measures=JSON.stringify(On)),L())}function In(){Ln&&Ln(),En&&En()}function An(){T(),J(),G(),rn(),fn(),wn(),function(){if(window.PerformanceMeasure&&window.PerformanceMark){Sn=s("customMeasuresPrefixes"),Cn=s("customMarksPrefixes"),Sn.push("bilmur_"),Cn.push("bilmur_");var n=kn;try{Ln=Mn("mark",n),En=Mn("measure",n)}catch(n){In()}}}()}function Fn(){S=!1,D=!1,z=!1,on=!1,an=!1,document.removeEventListener("DOMContentLoaded",dn),removeEventListener("load",vn),In()}function _n(n){return n>0||0===n}function Nn(){var n;!function(){try{var n=0;document.createNodeIterator(document,128,(function(n){return(n.nodeValue||"").indexOf("served from batcache in")>-1?1:2})).nextNode()&&(n=1),b.batcache_hit=n}catch(n){}}(),b.provider=v("provider"),b.service=v("service"),b.site_tz=v("siteTz"),b.custom_props=h(),_n((n=f(navigator,["connection"])||{}).rtt)&&(b.rtt=n.rtt),_n(n.downlink)&&(b.downlink=Math.round(1e3*n.downlink)),b.site_host=f(location,["hostname"]),b.site_path=f(location,["pathname"]),b.version="4.2.1",L()}function jn(){return"true"===v("allowIframe")}function Bn(){Nn(),An(),M(Fn)}function Dn(){d.t=document.getElementById("bilmur")||{};var n=jn();try{if(window.self!==window.top&&!n)return}catch(t){if(!n)return}document.prerendering?document.addEventListener("prerenderingchange",Bn,{once:!0}):Bn()}window.performance&&window.performance.getEntriesByType&&("hidden"!==document.visibilityState||document.prerendering)&&("loading"===document.readyState?document.addEventListener("DOMContentLoaded",Dn):Dn())}();
