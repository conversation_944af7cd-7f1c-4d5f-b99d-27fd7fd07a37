<h1 id="people-of-western-australias-ghost-towns">People of Western
Australias Ghost Towns</h1>
<h2 id="comments-on-various-quotes-20250319">Comments on Various Quotes
20250319</h2>
<ul>
<li>Summed the costs for development as presented in the quotes, for
both the complete development as well as for the Data Entry Initial
Stage (as an initial provision of a system).</li>
<li>Incorporated the cost of hosting for 12 months, based on the minimum
hosting environment suggested by the people quoting.</li>
<li>Did not include the cost of providing a monthly maintenance service.
Codevelopment was the only organisation offering this service (and
costing it - but with 2 different costs between the data entry stage and
the complete development). The total annual cost for Codevelopment could
be $8,448 p.a. incGST.</li>
<li>All costs had GST applied, to make the comparisons valid (since the
hosting costs included GST).</li>
<li>The final costs are likely to be somewhat different from the costs
outlined below (since the latter are for comparison purposes).</li>
<li>All of the vendors apart from PWD basically stated they would make
the data entry stage the first part of the complete development. PWD
could be asked to revise their data entry quote to make it the first
part of the complete development.</li>
<li>As a side note, the Codevelopment contract states that the IP
remains with Codevelopment. This is not acceptable. <PERSON>
specifically said that Family History would own the IP. Not mentioned by
the others.</li>
<li>From an initial technical appraisal, all of Wordpress (PWD), PHP
Laravel (Six Character) and Drupal (Codevelopment) are well known
technologies and one could find support for these elsewhere. Directus
(Sam Bradley) is not as well used in WA but is used world-wide quite a
bit. It is open-source and it is likely one could find people to work
with it if needed.
<ul>
<li>Without more detailed analysis, it is difficult to ascertain which
technology would be more likely to be able to be further developed. Each
of the technology “stacks” has its own strengths and weaknesses.
<ul>
<li>For instance, Drupal has a module which allows for AI integration,
Wordpress has some plugins for AI, and PHP can be programmed to
integrate with AI systems (PHP is a general purpose language). Directus
is basically a system to connect a database with webpages, and thus
requires programming with another language, which could be used to
integrate with some AI systems.</li>
<li>Mostly, one way or another, each of the technologies would be able
to be extended for further development purposes.</li>
</ul></li>
</ul></li>
<li>The costs comparison which I worked up is as follows:</li>
</ul>
<p>Costs Comparisons (Data Entry Stage vs Complete Development):</p>
<table>
<thead>
<tr class="header">
<th style="text-align: left;">Vendor (Technologies)</th>
<th style="text-align: right;">Data Entry</th>
<th style="text-align: right;">Complete Development</th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td style="text-align: left;">Sam Bradley (Directus):</td>
<td style="text-align: right;">$9,428.88</td>
<td style="text-align: right;">$14,378.88</td>
</tr>
<tr class="even">
<td style="text-align: left;">Six Character (PHP Laravel):</td>
<td style="text-align: right;">$12,151.38</td>
<td style="text-align: right;">$16,424.88</td>
</tr>
<tr class="odd">
<td style="text-align: left;">PWD (Wordpress Gravity):</td>
<td style="text-align: right;">$12,675.30</td>
<td style="text-align: right;">$132,391.00</td>
</tr>
<tr class="even">
<td style="text-align: left;">Codevelopment (Drupal):</td>
<td style="text-align: right;">$22,353.88</td>
<td style="text-align: right;">$65,283.76</td>
</tr>
</tbody>
</table>
<ul>
<li>I think the IP clause by Codevelopment discounts using them, but
their costs are also quite high, including a high hourly rate.</li>
<li>The complete development costs for PWD are very high.</li>
<li>Did you meet with Sam Bradley and Morgan Leek of Six Character? What
was the impression of their capabilities?</li>
<li>Since both Sam Bradley and Six Character have reasonably low total
prices, it would be worthwhile having some further discussions with
them.</li>
</ul>
<p>There is a possible alternative whereby I can generate the code for
the system using the technologies proposed by them, and see whether they
will accept taking over the code and providing ongoing support. In this
manner, the initial cost can be much lower but there is still support
provided.</p>
<p>For further discussion.</p>
<h2 id="website-development">Website Development</h2>
<p>Costs for Data Entry Stage plus 12 months hosting:</p>
<div class="line-block">Sam Bradley (Directus): | $9,428.88 |<br />
Six Character (PHP Laravel): | $12,151.38 |<br />
PWD (Wordpress Gravity): | $12,675.30 |<br />
Codevelopment (Drupal): | $22,353.88 |</div>
<p>Complete Development of Solution 12 months total costs:</p>
<div class="line-block">Sam Bradley (Directus) | $14,378.88 |<br />
Six Characters (PHP Laravel) | $16,424.88 |<br />
Codevelopment (Drupal) | $65,283.76 |<br />
PWD (Wordpress) | $132,391.00 |</div>
<p>Costs Comparisons (Data Entry Stage vs Complete Development):</p>
<div class="line-block">Sam Bradley (Directus): | $9,428.88 | $14,378.88
|<br />
Six Character (PHP Laravel): | $12,151.38 | $16,424.88 |<br />
PWD (Wordpress Gravity): | $12,675.30 | $132,391.00 |<br />
Codevelopment (Drupal): | $22,353.88 | $65,283.76 |</div>
<h3 id="various-organisations-stage-1-quote-comments">Various
Organisations Stage 1 Quote Comments</h3>
<p>20250313_email_Project_quote_for_online_data_collection_tool_20250313t175421_b8xs.pdf</p>
<h4 id="pwd">PWD</h4>
<p>20250307_PWD_Chris_Merton_People_of_Western_Australia_s_Ghost_Towns_Stage_1_20250316t130712_Ztda.pdf</p>
<p>See comments below. Wordpress, Gravity Forms, WPEngine.</p>
<pre><code>99.00 x1 = 99.00 per month (for 6 months)

Subtotal   GST 10%
99.00      9.90
Total AUD including GST: $108.90 per month (for 6 months)
Total over 6 months AUD including GST: $653.40
Total over 12 months AUD including GST: $1306.80

Subtotal   GST 10%
10,335.00  1,033.50
Total AUD including GST: $11,368.50

Total for 6 months hosting and development AUD including GST: $12,021.90
Total for 12 months hosting and development AUD including GST: $12,675.30
</code></pre>
<h4 id="codevelopment">Codevelopment</h4>
<p>20250228_Codevelopment_David_Malan_20250316t130712_RzRv.pdf</p>
<p>Uses Drupal as used by WA Museum and various others. Media Cloud WA
hosting https://www.mediacloud.net.au/ (possibly could manage with
Shared Hosting @$21/mth = $252 p.a. May need VPS Hosting @$98.24/mth =
1178.88 p.a. ) CONSULTATION &amp; PLANNING $2,800 TECHNICAL REQUIREMENTS
(Development) $16,450 TOTAL $19,250 exGST $21,175 incGST</p>
<p>Stage 1 Development plus 12 months hosting: 21,175 + 1178.88 =
$22,353.88</p>
<p>Website managed services (database, administration tools, security
and compatibility updates): $470/month = $5,640 p.a. Additional
services: $260ph, or per estimate.</p>
<p>IP remains with Codevelopment - really should be with Family History.
The agreement is unclear about what happens to the software upon
termination. A cursory read suggests that it has to return to
Codevelopment (or remain with it).</p>
<h4 id="six-character">Six Character</h4>
<p>20250312_Six_Character_Media_6cm_Morgan_Leek_20250316t130712_d32C.pdf</p>
<p>Uses PHP, Laravel (Laravel Scout and Typesense, Laravel Sanctum),
MySQL, RESTful API, NextJS and React, The cost for this build would be
$9,975 +GST (9975*1.1 = $10,972.50) Requires a LinuxVPS from MediaCloud
(VPS Hosting @$98.24/mth = 1178.88 p.a.)</p>
<p>Stage 1 Development: $9,975 exGST ($10,972.50 incGST)</p>
<p>Stage 1 Development plus 12 months hosting: 10,972.50 + 1178.88 =
$12,151.38</p>
<p>Adhoc Hourly cost: $105/hour exGST ($115.50 incGST)</p>
<h4 id="sam-bradley">Sam Bradley</h4>
<p>20250313_SB_Sam_Bradley_Project_Quotation_WA_Ghost_Towns_Website_20250316t130712_DmAK.pdf</p>
<p>Directus headless CMS product
(https://directus.io/solutions/headless-cms) ?? not sure what the SQL
database is?</p>
<p>Milestone 1: Project Planning and Setup Milestone 2: Directus Setup
and Database Integration Milestone 3: Finialise Directus for Data Entry
Milestone 4: Website Data Views Milestone 5: Static Content and Blog
Module Milestone 5 (sic - 6): Map functionality and User Submissions
Milestone 7: System Optimisation and Compliance Milestone 8: Final
Deployment and Training</p>
<p>Family History owns IP.</p>
<p>Website hosting will be provided by Media Cloud as specified in the
requirements. Use the VPS Hosting for comparison @$98.24/mth = 1178.88
p.a.</p>
<p>$15,000 in total. 25% deposit ($3,750) upon project commencement, 25%
($3,750) after delivery of milestone 3, 50% ($7,500) upon project
completion</p>
<p>Data Entry only is Milestone 3 = $3,750 + $3,750 = $7,500 ex GST
($8,250 inc GST) Data Entry Stage plus 12 months hosting = $8,250 +
1178.88 = $9,428.88</p>
<p>===========================================================================</p>
<h4 id="pwd-stage-1-quote-comments">PWD Stage 1 Quote Comments</h4>
<p>Comments made on 20250316
20250313_PWD_quote_People_of_Western_Australias_Ghost_Towns_Comments_01_20250314t101229.docx
20250313_PWD_quote_People_of_Western_Australias_Ghost_Towns_Stage_1_20250313t175404_6Cm8.pdf
20250313_PWD_quote_People_of_Western_Australias_Ghost_Towns_Stage_1_20250313t175404_6Cm8.txt</p>
<p>There are a range of considerations posed by the proposal. Give me a
call to discuss further. If you want me to discuss with PWD as well,
then happy to do so.</p>
<h5 id="executive-summary">Executive Summary</h5>
<ol type="1">
<li>Stage 1 also needs editing capability.</li>
<li>Duplication of user registration - issue or not?</li>
<li>What does Stage 2 actually do?</li>
<li>Stage 1 - A Component or a Throwaway? Should Stage 1 actually be the
initial build of the final solution? If Staged 1 is effectively a
throwaway, then alternative solutions could be considered which would
save the $12,000 cost of the throwaway solution, which is then applied
to the final solution.</li>
</ol>
<h5 id="recommendation">Recommendation</h5>
<ol type="a">
<li>Ask PWD to consider whether Stage 1 should be the initial build of
the final solution, rather than a throwaway solution.</li>
<li>Ask PWD to provide the cost for each stage (based on Stage 1 being
the initial build), and provide a little more detail on what is being
proposed in each stage.</li>
<li>If PWD still considers that Stage 1 should effectively be a
throwaway (as per their current proposal), then consider some low-cost
no-code alternatives for Stage 1 and confirm the full cost of a PWD
final solution build importing data from the Stage 1 alternative.</li>
</ol>
<p>More detail follows, which elucidates on the points above … …</p>
<h5 id="pwd-staging">PWD Staging</h5>
<p>It is usually a good idea to break the project down into stages or
phases, but I am not sure that I understand whether the stages proposed
are actually that useful or workable in practice.</p>
<p>They propose the following stages, which will theoretically deliver
the whole project:</p>
<ul>
<li>Stage 1 - Data Collection Module (and exporting existing
database)</li>
<li>Stage 2 - Centralise data for better search functionality.</li>
<li>Stage 3 - Enhance the design and functionalities based on the
collected data to create a full custom public facing website</li>
</ul>
<p>Some comments below in re each stage.</p>
<h5
id="stage-1---data-collection-module-and-exporting-existing-database">Stage
1 - Data Collection Module (and exporting existing database)</h5>
<p>PWD proposes using Gravity Forms running within/under Wordpress using
WPEngine to host the Wordpress site.</p>
<h6 id="gravity-forms">Gravity Forms</h6>
<p>Gravity Forms allows for entry of data in a form. The form can be
saved and then edited prior to submission, but when submitted, the form
data is no longer available for editing (this is the standard form
behaviour - verify with PWD whether this is incorrect).</p>
<h6 id="users">Users</h6>
<p>The Wordpress site containing the Gravity Form will have to be
controlled to only allow registered user access - otherwise anyone on
the internet could enter bogus data (and cause all sorts of
problems).</p>
<p>How many users are you proposing to allow entry of data in the
initial stage? Are these the same users as already registered in main
FHWA site? Will the new data.fhwa.org.au site share the registered user
base, or will it require a separate registration process?</p>
<p>Based on what PWD state (“Volunteers will be able to create
accounts”), it is likely to be a separate registration process.</p>
<p>Not much of a problem if only a few users, but potentially
problematical if wanting to allow everyone in FHWA to be able to enter
records, and if there are many registered users.</p>
<p>The presumption is that the final ghostswa.org.au site will share the
registered user base with fhwa.org.au? Is this correct? If this is the
case, having too many people having to multiply register will cause some
confusion etc, although this may be a relatively minor concern and could
be managed.</p>
<h6 id="data-storage">Data Storage</h6>
<p>When the form is submitted, where is the data stored? In a relational
database (like PostgreSQL) or as CSV files etc or some other mechanism?
It appears that it will be stored using Wordpress facilities (“Backend
(Wordpress) database setup to store and manage submitted data”).</p>
<h6 id="attachments">Attachments</h6>
<p>If photos/documents etc are a part of the data entry, where and how
are these stored? How many can be attached per each form?</p>
<p>Large attachments like photos/documents etc can not be exported in
CSV (unless one used various advanced techniques like Base64 conversion,
and then usually the line lengths are far too long to handle in
CSV).</p>
<p>How will attachments be handled in the conversion process to the new
platform?</p>
<h6 id="data-editing">Data Editing</h6>
<p>Once the Gravity Form is submitted, it is unclear that any of the
data entered will be able to be edited, e.g. to fix a typo, to add some
additional information, to upload another photo or document etc. The
presumption is that the data will NOT be able to edited, because this is
not what normal Gravity Forms does. I did find an add-on product called
GravityView which is part of GravityKit (https://www.gravitykit.com/).
It costs (https://www.gravitykit.com/intro-pricing/) either USD$86 or
USD$132 for the first year and then increasing thereafter.</p>
<p>There is also a technical administrative backend inside Wordpress
which would allow editing, but I would suggest that that would not be
for ordinary users.</p>
<h6 id="stage-1---a-component-or-a-throwaway">Stage 1 - A Component or a
Throwaway</h6>
<p>The big question that I have is whether this initial Stage 1 solution
is going to be (a) a component of the final solution, or (b) will the
final solution replace the data entry with another product/solution?</p>
<p>If the former (a), then why would there not be an editing
capability?</p>
<p>If the latter (b) - which is more likely, given their statement
“Export existing database into new platfrom (from CSV)” (sic) - then
will the Gravity Forms interface be completely abandoned (most
likely)?</p>
<p>As mentioned above, migrating attachments into the new
platform/database may pose some additional coding work, all of which is
“wasted” from a future perspective.</p>
<h6 id="overall-costings-and-proposed-solution-savings">Overall Costings
and Proposed Solution Savings</h6>
<p>How does the proposed $12,000 cost for Stage 1 fit into the overall
project costings? Does it save money overall, or add to the final total
cost? (Incidentally, what was the proposed cost for the final
solution?)</p>
<h6 id="stage-1-should-be-initial-build-of-final-solution">Stage 1
should be initial build of final solution</h6>
<p>Given all the above, why would PWD not use stage 1 to build the
initial aspect of the final solution?</p>
<p>These steps would presumably be:</p>
<ol type="1">
<li>Implement basic platform facility (e.g. if the final solution is all
Wordpress based and uses a relational database backend, then create the
Wordpress configuration and installation, install and configure the
database, provide basic user management functionality). As an adjunct,
maybe implement the connection between registered users on FHWA and the
new site, to avoid the overlapping of registrations;</li>
<li>Design and create the data structures for the final solution, and
implement in the database;</li>
<li>Design the basic data entry and editing screen(s) for the main data
entry (as per what was proposed for Stage 1 by PWD but including editing
capability);</li>
<li>Build and test the data entry and editing screen(s) for the main
data entry, implementing basic CRUD functionality (Create Read Update
Delete), allowing for attachments to be processed.</li>
</ol>
<p>The initial implementation would have limited search capabilities,
and possibly just provide a list of records entered, which one could
scroll down to find the record to view and edit if required.</p>
<p>This would then neatly lead into Stage 2 and follow-on to Stage
3.</p>
<p>This solution obviates the need for GravityForms and other
add-ons.</p>
<h5
id="stage-2---centralise-data-for-better-search-functionality.">Stage 2
- Centralise data for better search functionality.</h5>
<h6 id="what-does-stage-2-actually-do">What does Stage 2 actually
do</h6>
<p>In the PWD proposal, I did not understand what Stage 2 was supposed
to do at all. What does “Centralise data” mean? What exactly is being
proposed in terms of development etc?</p>
<p>Following on from the outline and queries in re Stage 1, at some
point, it appears that PWD are proposing to have to develop a new
platform, with all data entry, editing, search and other functionality
built into it. The GravityForms solution would then be abandoned after
the data migration to the new platform.</p>
<p>Is this what Stage 2 is supposed to be?</p>
<p>If not, then what is Stage 2 actually implementing in what
environment?</p>
<p>And if this new development is what Stage 2 is, then why not commence
it in Stage 1 (as outlined above) and then extend it in Stage 2 for
further search and view functionality?</p>
<h5
id="stage-3---enhance-the-design-and-functionalities-based-on-the-collected-data-to-create-a-full-custom-public-facing-website">Stage
3 - Enhance the design and functionalities based on the collected data
to create a full custom public facing website</h5>
<p>I then presume that Stage 3 is taking the new platform that was
implemented in Stage 2 (and if it was not implemented in Stage 2, it
would have to be implemented in Stage 3 - further exacerbating the
issues of wasted development in earlier stages) and adding all the rest
of the functionality required, such as different views of the data,
advanced search capabilities, etc.</p>
<p>Once again, the issue is whether Stage 1 and Stage 2 are just the
early stages of the complete development on the new platform, or whether
the work in any of these stages is thrown away?</p>
<h5 id="alternatives-for-a-stage-1-data-entry-throwaway">Alternatives
for a Stage 1 Data Entry Throwaway</h5>
<h6 id="number-of-data-entry-registered-users-required">Number of Data
Entry Registered Users Required</h6>
<p>How many people might have to be registered to do data entry in Stage
1? Or alternatively, do you think it would work to have just 2 or 3
registered users (i.e. FH1, FH2, FH3) and people share the login when
doing some data entry (saves on costs)?</p>
<h6 id="data-entry-nocode-products">Data Entry NoCode Products</h6>
<p>There are quite a few products which allow you to define really nice
data entry forms and store the entries in a database, for later display,
editing, etc. They allow uploading of multiple photos/documents etc.
They also allow for exporting the data in various formats, and have
interfaces to download all the photos/documents as well.</p>
<p>These products are very easy to setup in the first instance and
pretty easy to use.</p>
<p>All the products offer a free tier which limits the number of users
and the number of records that can be stored. This can be used to test
out the design for the data entry app and pick which solution you like
the best.</p>
<p>For instance Baserow (https://baserow.io) does all the above and much
more, for either USD$12/user/month or USD$22/user/month. See
https://baserow.io/pricing.</p>
<p>Other options (which are of a similar pricing nature to baserow.io)
include:</p>
<ul>
<li>https://www.clappia.com/</li>
<li>https://www.getgrist.com/</li>
<li>https://www.airtable.com/</li>
<li>https://www.notion.com/</li>
<li>https://clickup.com/</li>
</ul>
<h6 id="data-entry-development">Data Entry Development</h6>
<p>If you want a simple data entry solution and save money for the final
build, then one could use one of the above NoCode products to do the
data entry, and then export the data to CSV and import into the final
solution (in the same manner that PWD were proposing for their Stage 1
to subsequent stages proposal).</p>
<p>Given the fields which the Ghosts form wants to populate (you would
provide that), I could easily generate a solution using a free tier of a
good product and that prototype could be iterated to make sure it works
really well.</p>
<p>It would not take long to get something going to see what it looks
like - I could do that within a day if you provide me with all the
fields.</p>
<p>A simple manual could be written and people could get going with the
data entry.</p>
<p>I would have to implement the data export facility, which requires a
small amount of work to export all the attachments as well as the data
itself (the data export is built-in to the product and requires no work
- it is just getting all the attachments which requires a little bit of
work. PWD would have to do the same work in their GravityForms
solution.)</p>
<p>The data export could be run regularly to provide backups.</p>
<p>When PWD are ready with the basics of their final solution (enough to
enter and edit data and read in date from an export), then the latest
export could be provided to PWD.</p>
<hr />
<p>On a side note, some of the costing PWD provided seemed a little
strange …</p>
<h6 id="wp-engine-hosting">WP Engine hosting</h6>
<p>PWD suggest that the data entry needs to be hosted on WPEngine
(https://wpengine.com/au/plans/), at a cost of $99 per month. But the
actual WPEngine site states that the cost is AUD$83 per month if one
pays monthly and only AUD$69 per month if pay annually (i.e. $826/year).
Why the pricing discrepancy?</p>
<p>More than that, why is such a high-end hosting solution needed for
data entry only. If it goes down and takes some time to recover (which
is highly unlikely even for the most basic web hosting solution), it
simply means that some data entry is delayed for a little while. As long
as backups and data copies are made regularly, no data should be
lost.</p>
<p>When the full system is in place, obviously a robust infrastructure
solution is necessary (365/24/7 operation, auto-failover, recovery,
etc). But it is very unclear from the quote whether a Wordpress site is
the end-point infrastructure for the final solution.</p>
<p>The https://ghostswa.au/ website is obviously Wordpress, using
WordPress.com (because of the promotional header which still appears). I
am presuming that the main site (https://www.fhwa.org.au/) is not
Wordpress (based on the PWD statement: “Although the main FHWA site is
not built on WordPress”) - presumably it is MemberJungle.</p>
<h4 id="pwd-quote">PWD Quote</h4>
<p>20250313_PWD_quote_People_of_Western_Australias_Ghost_Towns_Stage_1_20250313t175404_6Cm8.pdf</p>
<p>Full Project Objectives:</p>
<ul>
<li>Develop a comprehensive genealogical index of people who resided in
Western Australian ghost towns</li>
<li>Include historical, demographic, and geographical information on all
known ghost towns, abandoned towns/settlements, and similar places in
Western Australia</li>
<li>Make the information accessible through a dedicated website with
advanced search / filtering capabilities</li>
<li>Aim for the project to be self-funding through public and private
grants, public donations, and monetisation of the collected
information</li>
<li>Proposed Stages:
<ul>
<li>Stage 1 - Data Collection Module (and exporting existing
database)</li>
<li>Stage 2 - Centralise data for better search functionality.</li>
<li>Stage 3 - Enhance the design and functionalities based on the
collected data to create a full custom public facing website</li>
</ul></li>
</ul>
<p>53 Hours Allocated: 195.00 x 53 = 10,335.00</p>
<h5 id="data-collection-module---stage-1">Data Collection Module - Stage
1</h5>
<p>We will need to create a subdomain on WordPress for this project
(e.g., data.fhwa.org.au) to host the data collection module. Although
the main FHWA site is not built on WordPress, the subdomain will be
configured to run on a separate WordPress instance, ensuring smooth
integration with the main site while maintaining flexibility for future
expansion. This approach allows the data collection tool to function
independently, with its own database and access controls, while still
aligning with the overall FHWA infrastructure.</p>
<p>Stage 1 of the project focuses on developing a secure, role-based
data collection module for the “People of Western Australian Ghost
Towns” initiative. Volunteers will be able to create accounts and submit
data via a password-protected subdomain, using Gravity Forms integrated
with a backend database. The module will allow for efficient data entry
and future scalability, with an emphasis on secure access and ease of
use. This phase will be completed with a design, development, testing,
and volunteer account setup, ensuring smooth integration into Phase
2.</p>
<h5 id="design---estimated-at-8-hours">Design: - Estimated at 8
hours</h5>
<ul>
<li>User-friendly, intuitive interface for easy data submission</li>
<li>Clean and accessible layout</li>
<li>High visibility design for an older demographic</li>
<li>Fully responsive, compatible across devices</li>
<li>Simple, easy-to-navigate structure</li>
<li>Supports role-based access for volunteers and admins</li>
<li>Scalable design to accommodate future integration with the data
collection module</li>
</ul>
<h5 id="development---estimated-at-39-hours">Development: - Estimated at
39 Hours</h5>
<ul>
<li>Development of a secure, password-protected subdomain for data
collection</li>
<li>Integration of Gravity Forms for data entry and submission</li>
<li>Backend (Wordpress) database setup to store and manage submitted
data</li>
<li>Role-based access system to manage volunteer and admin accounts</li>
<li>Implementation of quality assurance and testing to ensure
functionality and security</li>
<li>Export existing database into new platfrom (from CSV)</li>
<li>Full QA and testing prior to launch</li>
<li>Scalable architecture to support future phases and database
centralisation</li>
</ul>
<h5 id="project-management---estimated-at-6-hours">Project Management: -
Estimated at 6 hours</h5>
<ul>
<li>Dedicated project manager</li>
<li>Development progress meetings</li>
<li>Pre-launch review meeting</li>
<li>Training for volunteers</li>
</ul>
<h5 id="wp-engine-premium-hosting---recommended-for-security">WP Engine
Premium Hosting - Recommended for Security</h5>
<p>WP Engine is a premium managed WordPress hosting provider designed to
deliver high performance, secure, and scalable solutions for WordPress
websites including features such as automated backups, managed security,
a content delivery network (CDN), and expert WordPress support, ensuring
your website operates at peak performance.</p>
<h5 id="costs">Costs</h5>
<pre><code>99.00 x1 = 99.00 per month (for 6 months)

Subtotal   GST 10%
99.00      9.90
Total AUD including GST: $108.90 per month (for 6 months)
Total over 6 months AUD including GST: $653.40

Subtotal   GST 10%
10,335.00  1,033.50
Total AUD including GST: $11,368.50

Total for hosting and development AUD including GST: $12,021.90
</code></pre>
<h1
id="section">===============================================================</h1>
<h3
id="various-organisations-complete-development-quotes-comments">Various
Organisations Complete Development Quotes Comments</h3>
<p>Complete Development of Solution 12 months total costs:</p>
<div class="line-block">Codevelopment | Drupal | $58,170 + $1178.88 =
$59,348.88 exGST $65,283.76 incGST |<br />
PWD | Wordpress | $109,440.00 + $9,608.64 + $1306.80 = $120,355.44 exGST
$132,391.00 incGST |<br />
Six Characters | PHP Laravel | $15,246 + $1,178.88 = $16,424.88 incGST
|<br />
Sam Bradley | Directus | $13,200 + $1,178.88 = $14,378.88 incGST |</div>
<h4 id="codevelopment-1">Codevelopment</h4>
<p>20240725_Codevelopment_Quote_2024_07_25_Family_History_WA_WEBSITE_DEVELOPMENT_20250316t130734_TCoV.pdf</p>
<p>25th July 2024 Uses Drupal (as used by WA Museum and various others),
Apache SOLR Media Cloud WA hosting https://www.mediacloud.net.au/</p>
<p>Key Requirements, Setup, Search: $18,550 UX REQUIREMENTS: $15,890
Backend: $18,130 Consultation, planning and communication: $5,600</p>
<p>Complete development total: $58,170 exGST $63,987 incGST</p>
<p>WEBSITE MAINTENANCE monthly fee: $640 per month exGST $704 per month
incGST (12 months is $7680 exGST $8448 incGST) Additional services: $260
per hour, or per estimate</p>
<p>Media Cloud WA hosting https://www.mediacloud.net.au/ (possibly could
manage with Shared Hosting @$21/mth = $252 p.a. May need VPS Hosting
@$98.24/mth = 1178.88 p.a. )</p>
<p>12 months total cost with no support, for comparison purposes:
$58,170 + $1178.88 = $59,348.88 exGST $65,283.76 incGST 12 months total
cost including monthly support: $58,170 + $8,448 + $1,178.88 =
$67,896.88 exGST $74,686.56 incGST</p>
<h4 id="pwd-1">PWD</h4>
<p>20240729_PWD_Quote_People_of_Western_Australia_s_Ghost_Towns_Website_Project_20250316t130734_Hasx.pdf</p>
<p>Built using Wordpress CMS</p>
<p>Website Development-Stage1 Stage 1 will be ensuring that the required
functionality is achieved. $180.00/hr x 640 hours with a 5% discount =
$109,440.00 exGST $115,368.00 incGST</p>
<p>Priority Support and Maintenance Retainer: $180.00/hr x 5 hours with
a 5% discount = $853.55 exGST $900.72 incGST per month, which is
$9,608.64 exGST $10,088.64 incGST for 12 months.</p>
<p>Adhoc hourly rate $180 exGST $198 incGST</p>
<p>12 months total cost: $109,440.00 + $9,608.64 + $1306.80 =
$120,355.44 exGST $132,391.00 incGST</p>
<h4 id="six-character-1">Six Character</h4>
<p>20240801_6CM_Morgan_Leek_Quote_People_of_WA_s_Ghost_Towns_20250316t130734_J8Aq.pdf</p>
<p>Uses PHP, Laravel (Laravel Scout and Typesense, Laravel Sanctum),
MySQL, RESTful API, NextJS and React, The cost for this build would be
$9,975 +GST (9975*1.1 = $10,972.50) Requires a LinuxVPS from MediaCloud
(VPS Hosting @$98.24/mth = 1178.88 p.a.)</p>
<p>Adhoc Hourly cost: $105/hour exGST ($115.50 incGST)</p>
<p>Complete Build cost: $13,860 exGST $15,246 incGST</p>
<p>Complete Development plus 12 months hosting: $15,246 + $1,178.88 =
$16,424.88 incGST</p>
<h4 id="sam-bradley-1">Sam Bradley</h4>
<p>20240807_Samuel_Bradley_Website_Quote_20250316t130734_MfLt.pdf
20240807_SB_Samuel_Bradley_wa_ghost_towns_architecture_20250316t130712_3l6c.png</p>
<p>Directus headless CMS product
(https://directus.io/solutions/headless-cms) ?? not sure what the SQL
database is?</p>
<p>Deploy and configure Directus CMS: $3,000 exGST $3,300 incGST
Directus CMS Initial support and training: $1,500 exGST $1,650 incGST
Build a Website to Display content from Directus: $6,000 exGST $6,600
incGST Website testing, fixes and visual enhancements: $1,500 exGST
$1,650 incGST</p>
<p>Complete development cost: $12,000 exGST $13,200 incGST</p>
<p>Website hosting will be provided by Media Cloud as specified in the
requirements. Use the VPS Hosting for comparison @$98.24/mth = 1178.88
p.a.</p>
<p>Complete Development plus 12 months hosting: $13,200 + $1,178.88 =
$14,378.88 incGST</p>
