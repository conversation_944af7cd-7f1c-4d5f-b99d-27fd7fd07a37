
# People of Western Australias Ghost Towns - Notes, Instructions

## Website/Database Development


### Website/Database Phase 1 - Requirements, Design specification and agreement

People of Western Australias Ghost Towns Web Application Phase 1 Solution Definition

#### Prompt
please write me a detailed project plan for doing a study into an ict application/system such that we verify the requirements which have been initially set out, we produce an architecture and design for the system which covers functionality, data, uX, infrastructure, deployment etc, and we do the UX design including screen mockups - for a web based application that is database backed and modern front end using a headless CMS. the design must be such that it accommodates producing new systems in the future based on the same design just with changes to configurations, certain fields etc.  the plan must include user sessions to discuss requirements, design, UX etc as well as to review what is produced/written up and make mods and then to accept it all.

in the uploaded Word document, make the following changes and recreate the document with the changes for me to download as a Word document:
1. ensure that all level 2 and level 3 and level 4 and level 5 Headings match in their formatting, with at least some space before each heading.
2. format all the tables so that they are nicely formatted with different colours and thin dotted lines between cells
3. ensure that you insert at least ten pictures or diagrams but not more than twenty into the document at various locations evenly spread through the document. the picture or diagram should relate to the text if possible (e.g. the Waterfall SDLC can have a diagram depicting a waterfall approach) or be visually similar to the text
4. Create an Executive Summary of the whole document which is no more than 3 pages in length but preferably two pages maximum - which goes at the front of the main text
5. Create a cover page for the report which includes the title, who it is for (Family History WA), the date
6. Create a table of contents which goes after the cover page and before the Executive Summary
7. Create a list of figures which goes after the table of contents and before the Executive Summary
8. Create a list of tables which goes after the list of figures and before the Executive Summary
9. Create a list of acronyms which goes after the list of tables and before the Executive Summary
10. Create a list of abbreviations which goes after the list of acronyms and before the Executive Summary
11. Insert page headers and footers, with the heading having the report title and the footer having the report filename and the page number - all nicely formatted
12. Ensure the document is sized for A4
