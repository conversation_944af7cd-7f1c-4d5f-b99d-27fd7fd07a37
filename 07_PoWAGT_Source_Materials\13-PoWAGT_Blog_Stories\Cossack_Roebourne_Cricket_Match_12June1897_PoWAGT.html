<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="profile" href="http://gmpg.org/xfn/11">

<title>Cossack &#8211; 12 June 1897 &#8211; People of Western Australia&#039;s <PERSON></title>
<script type="text/javascript">
  WebFontConfig = {"google":{"families":["Josefin+Sans:b:latin,latin-ext","Alegreya+Sans:r,i,b,bi:latin,latin-ext"]},"api_url":"https:\/\/fonts-api.wp.com\/css"};
  (function() {
    var wf = document.createElement('script');
    wf.src = 'https://s0.wp.com/wp-content/plugins/custom-fonts/js/webfont.js';
    wf.type = 'text/javascript';
    wf.async = 'true';
    var s = document.getElementsByTagName('script')[0];
    s.parentNode.insertBefore(wf, s);
	})();
</script><style id="jetpack-custom-fonts-css">.wf-active code, .wf-active kbd, .wf-active pre, .wf-active samp{font-family:"Alegreya Sans",sans-serif}.wf-active body{font-family:"Alegreya Sans",sans-serif}.wf-active button, .wf-active input, .wf-active select{font-family:"Alegreya Sans",sans-serif}.wf-active textarea{font-family:"Alegreya Sans",sans-serif}.wf-active blockquote{font-family:"Alegreya Sans",sans-serif}.wf-active code, .wf-active kbd, .wf-active pre, .wf-active tt, .wf-active var{font-family:"Alegreya Sans",sans-serif}.wf-active .button, .wf-active .more-link, .wf-active button:not(.menu-toggle), .wf-active input[type="button"], .wf-active input[type="reset"], .wf-active input[type="submit"], .wf-active .posts-navigation .nav-links a, .wf-active #content #infinite-handle span button{font-family:"Alegreya Sans",sans-serif}.wf-active input[type="email"], .wf-active input[type="password"], .wf-active input[type="search"], .wf-active input[type="text"], .wf-active input[type="url"], .wf-active textarea{font-family:"Alegreya Sans",sans-serif}.wf-active .post-navigation{font-family:"Alegreya Sans",sans-serif}.wf-active .main-navigation{font-family:"Alegreya Sans",sans-serif}.wf-active .entry-content, .wf-active .entry-summary, .wf-active .page-content{font-family:"Alegreya Sans",sans-serif}.wf-active .entry-content .subtitle{font-family:"Alegreya Sans",sans-serif}.wf-active #comments{font-family:"Alegreya Sans",sans-serif}.wf-active .comment-form label{font-family:"Alegreya Sans",sans-serif}.wf-active .comment-form span.required{font-family:"Alegreya Sans",sans-serif}.wf-active .widget_recent_entries span.post-date{font-family:"Alegreya Sans",sans-serif}.wf-active .site-description{font-family:"Alegreya Sans",sans-serif}.wf-active .site-posted-on time{font-family:"Alegreya Sans",sans-serif}.wf-active .page-header:not(.page-header-light) .taxonomy-description{font-family:"Alegreya Sans",sans-serif}.wf-active .light-text{font-family:"Alegreya Sans",sans-serif}.wf-active .site-info{font-family:"Alegreya Sans",sans-serif}.wf-active .sticky-label{font-family:"Alegreya Sans",sans-serif}.wf-active .post-details, .wf-active .post-details a{font-family:"Alegreya Sans",sans-serif}.wf-active .page-links{font-family:"Alegreya Sans",sans-serif}.wf-active .post-edit-link{font-family:"Alegreya Sans",sans-serif}.wf-active .post-author-card .author-description{font-family:"Alegreya Sans",sans-serif}.wf-active #tinymce h1, .wf-active #tinymce h2, .wf-active #tinymce h3, .wf-active #tinymce h4, .wf-active #tinymce h5, .wf-active #tinymce h6, .wf-active .comment-content h1, .wf-active .comment-content h2, .wf-active .comment-content h3, .wf-active .comment-content h4, .wf-active .comment-content h5, .wf-active .comment-content h6, .wf-active .entry-content h1, .wf-active .entry-content h2, .wf-active .entry-content h3, .wf-active .entry-content h4, .wf-active .entry-content h5, .wf-active .entry-content h6, .wf-active .entry-summary h1, .wf-active .entry-summary h2, .wf-active .entry-summary h3, .wf-active .entry-summary h4, .wf-active .entry-summary h5, .wf-active .entry-summary h6, .wf-active .widget_text h1, .wf-active .widget_text h2, .wf-active .widget_text h3, .wf-active .widget_text h4, .wf-active .widget_text h5, .wf-active .widget_text h6{font-family:"Josefin Sans",sans-serif;font-style:normal;font-weight:700}.wf-active h1{font-size:75.2px;font-style:normal;font-weight:700}.wf-active h2{font-size:49.6px;font-style:normal;font-weight:700}.wf-active h3{font-size:38.4px;font-style:normal;font-weight:700}.wf-active h4{font-size:32px;font-style:normal;font-weight:700}.wf-active h5{font-size:24px;font-style:normal;font-weight:700}.wf-active h6{font-size:22.4px;font-style:normal;font-weight:700}.wf-active blockquote h1, .wf-active blockquote h2, .wf-active blockquote h3, .wf-active blockquote h4{font-family:"Josefin Sans",sans-serif;font-weight:700;font-style:normal}.wf-active div#jp-relatedposts h3.jp-relatedposts-headline em{font-family:"Josefin Sans",sans-serif;font-style:normal;font-weight:700}.wf-active .comment-reply-title, .wf-active .comments-title{font-family:"Josefin Sans",sans-serif;font-size:41.6px;font-weight:700;font-style:normal}.wf-active .image-post-title{font-family:"Josefin Sans",sans-serif;font-size:25.6px;font-weight:700;font-style:normal}.wf-active .page-header:not(.page-header-light) h1{font-size:65.6px;font-style:normal;font-weight:700}.wf-active .entry-title{font-family:"Josefin Sans",sans-serif;font-size:3.52em;font-style:normal;font-weight:700}.wf-active #post-cover-image .cover-meta .single-post-title{font-family:"Josefin Sans",sans-serif;font-size:67.2px;font-style:normal;font-weight:700}.wf-active #hero-header .site-title{font-family:"Josefin Sans",sans-serif;font-size:65.6px;font-style:normal;font-weight:700}.wf-active .site-header .site-title{font-size:30.4px;font-style:normal;font-weight:700}.wf-active .site-header .site-description{font-size:20.8px;font-style:normal;font-weight:700}</style>
<meta name='robots' content='max-image-preview:large' />

<!-- Async WordPress.com Remote Login -->
<script id="wpcom_remote_login_js">
var wpcom_remote_login_extra_auth = '';
function wpcom_remote_login_remove_dom_node_id( element_id ) {
	var dom_node = document.getElementById( element_id );
	if ( dom_node ) { dom_node.parentNode.removeChild( dom_node ); }
}
function wpcom_remote_login_remove_dom_node_classes( class_name ) {
	var dom_nodes = document.querySelectorAll( '.' + class_name );
	for ( var i = 0; i < dom_nodes.length; i++ ) {
		dom_nodes[ i ].parentNode.removeChild( dom_nodes[ i ] );
	}
}
function wpcom_remote_login_final_cleanup() {
	wpcom_remote_login_remove_dom_node_classes( "wpcom_remote_login_msg" );
	wpcom_remote_login_remove_dom_node_id( "wpcom_remote_login_key" );
	wpcom_remote_login_remove_dom_node_id( "wpcom_remote_login_validate" );
	wpcom_remote_login_remove_dom_node_id( "wpcom_remote_login_js" );
	wpcom_remote_login_remove_dom_node_id( "wpcom_request_access_iframe" );
	wpcom_remote_login_remove_dom_node_id( "wpcom_request_access_styles" );
}

// Watch for messages back from the remote login
window.addEventListener( "message", function( e ) {
	if ( e.origin === "https://r-login.wordpress.com" ) {
		var data = {};
		try {
			data = JSON.parse( e.data );
		} catch( e ) {
			wpcom_remote_login_final_cleanup();
			return;
		}

		if ( data.msg === 'LOGIN' ) {
			// Clean up the login check iframe
			wpcom_remote_login_remove_dom_node_id( "wpcom_remote_login_key" );

			var id_regex = new RegExp( /^[0-9]+$/ );
			var token_regex = new RegExp( /^.*|.*|.*$/ );
			if (
				token_regex.test( data.token )
				&& id_regex.test( data.wpcomid )
			) {
				// We have everything we need to ask for a login
				var script = document.createElement( "script" );
				script.setAttribute( "id", "wpcom_remote_login_validate" );
				script.src = '/remote-login.php?wpcom_remote_login=validate'
					+ '&wpcomid=' + data.wpcomid
					+ '&token=' + encodeURIComponent( data.token )
					+ '&host=' + window.location.protocol
					+ '//' + window.location.hostname
					+ '&postid=3719'
					+ '&is_singular=1';
				document.body.appendChild( script );
			}

			return;
		}

		// Safari ITP, not logged in, so redirect
		if ( data.msg === 'LOGIN-REDIRECT' ) {
			window.location = 'https://wordpress.com/log-in?redirect_to=' + window.location.href;
			return;
		}

		// Safari ITP, storage access failed, remove the request
		if ( data.msg === 'LOGIN-REMOVE' ) {
			var css_zap = 'html { -webkit-transition: margin-top 1s; transition: margin-top 1s; } /* 9001 */ html { margin-top: 0 !important; } * html body { margin-top: 0 !important; } @media screen and ( max-width: 782px ) { html { margin-top: 0 !important; } * html body { margin-top: 0 !important; } }';
			var style_zap = document.createElement( 'style' );
			style_zap.type = 'text/css';
			style_zap.appendChild( document.createTextNode( css_zap ) );
			document.body.appendChild( style_zap );

			var e = document.getElementById( 'wpcom_request_access_iframe' );
			e.parentNode.removeChild( e );

			document.cookie = 'wordpress_com_login_access=denied; path=/; max-age=31536000';

			return;
		}

		// Safari ITP
		if ( data.msg === 'REQUEST_ACCESS' ) {
			console.log( 'request access: safari' );

			// Check ITP iframe enable/disable knob
			if ( wpcom_remote_login_extra_auth !== 'safari_itp_iframe' ) {
				return;
			}

			// If we are in a "private window" there is no ITP.
			var private_window = false;
			try {
				var opendb = window.openDatabase( null, null, null, null );
			} catch( e ) {
				private_window = true;
			}

			if ( private_window ) {
				console.log( 'private window' );
				return;
			}

			var iframe = document.createElement( 'iframe' );
			iframe.id = 'wpcom_request_access_iframe';
			iframe.setAttribute( 'scrolling', 'no' );
			iframe.setAttribute( 'sandbox', 'allow-storage-access-by-user-activation allow-scripts allow-same-origin allow-top-navigation-by-user-activation' );
			iframe.src = 'https://r-login.wordpress.com/remote-login.php?wpcom_remote_login=request_access&origin=' + encodeURIComponent( data.origin ) + '&wpcomid=' + encodeURIComponent( data.wpcomid );

			var css = 'html { -webkit-transition: margin-top 1s; transition: margin-top 1s; } /* 9001 */ html { margin-top: 46px !important; } * html body { margin-top: 46px !important; } @media screen and ( max-width: 660px ) { html { margin-top: 71px !important; } * html body { margin-top: 71px !important; } #wpcom_request_access_iframe { display: block; height: 71px !important; } } #wpcom_request_access_iframe { border: 0px; height: 46px; position: fixed; top: 0; left: 0; width: 100%; min-width: 100%; z-index: 99999; background: #23282d; } ';

			var style = document.createElement( 'style' );
			style.type = 'text/css';
			style.id = 'wpcom_request_access_styles';
			style.appendChild( document.createTextNode( css ) );
			document.body.appendChild( style );

			document.body.appendChild( iframe );
		}

		if ( data.msg === 'DONE' ) {
			wpcom_remote_login_final_cleanup();
		}
	}
}, false );

// Inject the remote login iframe after the page has had a chance to load
// more critical resources
window.addEventListener( "DOMContentLoaded", function( e ) {
	var iframe = document.createElement( "iframe" );
	iframe.style.display = "none";
	iframe.setAttribute( "scrolling", "no" );
	iframe.setAttribute( "id", "wpcom_remote_login_key" );
	iframe.src = "https://r-login.wordpress.com/remote-login.php"
		+ "?wpcom_remote_login=key"
		+ "&origin=aHR0cHM6Ly9naG9zdHN3YS5hdQ%3D%3D"
		+ "&wpcomid=*********"
		+ "&time=1750387368";
	document.body.appendChild( iframe );
}, false );
</script>
<link rel='dns-prefetch' href='//s0.wp.com' />
<link rel='dns-prefetch' href='//widgets.wp.com' />
<link rel='dns-prefetch' href='//wordpress.com' />
<link rel="alternate" type="application/rss+xml" title="People of Western Australia&#039;s Ghost Towns &raquo; Feed" href="https://ghostswa.au/feed/" />
<link rel="alternate" type="application/rss+xml" title="People of Western Australia&#039;s Ghost Towns &raquo; Comments Feed" href="https://ghostswa.au/comments/feed/" />
<link rel="alternate" type="application/rss+xml" title="People of Western Australia&#039;s Ghost Towns &raquo; Cossack &#8211; 12 June&nbsp;1897 Comments Feed" href="https://ghostswa.au/2025/06/12/otd18970612/feed/" />
	<script type="text/javascript">
		/* <![CDATA[ */
		function addLoadEvent(func) {
			var oldonload = window.onload;
			if (typeof window.onload != 'function') {
				window.onload = func;
			} else {
				window.onload = function () {
					oldonload();
					func();
				}
			}
		}
		/* ]]> */
	</script>
	<script type="text/javascript">
/* <![CDATA[ */
window._wpemojiSettings = {"baseUrl":"https:\/\/s0.wp.com\/wp-content\/mu-plugins\/wpcom-smileys\/twemoji\/2\/72x72\/","ext":".png","svgUrl":"https:\/\/s0.wp.com\/wp-content\/mu-plugins\/wpcom-smileys\/twemoji\/2\/svg\/","svgExt":".svg","source":{"concatemoji":"https:\/\/s0.wp.com\/wp-includes\/js\/wp-emoji-release.min.js?m=1743601627i&ver=6.8.1-alpha-60199"}};
/*! This file is auto-generated */
!function(i,n){var o,s,e;function c(e){try{var t={supportTests:e,timestamp:(new Date).valueOf()};sessionStorage.setItem(o,JSON.stringify(t))}catch(e){}}function p(e,t,n){e.clearRect(0,0,e.canvas.width,e.canvas.height),e.fillText(t,0,0);var t=new Uint32Array(e.getImageData(0,0,e.canvas.width,e.canvas.height).data),r=(e.clearRect(0,0,e.canvas.width,e.canvas.height),e.fillText(n,0,0),new Uint32Array(e.getImageData(0,0,e.canvas.width,e.canvas.height).data));return t.every(function(e,t){return e===r[t]})}function u(e,t,n){switch(t){case"flag":return n(e,"\ud83c\udff3\ufe0f\u200d\u26a7\ufe0f","\ud83c\udff3\ufe0f\u200b\u26a7\ufe0f")?!1:!n(e,"\ud83c\uddfa\ud83c\uddf3","\ud83c\uddfa\u200b\ud83c\uddf3")&&!n(e,"\ud83c\udff4\udb40\udc67\udb40\udc62\udb40\udc65\udb40\udc6e\udb40\udc67\udb40\udc7f","\ud83c\udff4\u200b\udb40\udc67\u200b\udb40\udc62\u200b\udb40\udc65\u200b\udb40\udc6e\u200b\udb40\udc67\u200b\udb40\udc7f");case"emoji":return!n(e,"\ud83d\udc26\u200d\ud83d\udd25","\ud83d\udc26\u200b\ud83d\udd25")}return!1}function f(e,t,n){var r="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope?new OffscreenCanvas(300,150):i.createElement("canvas"),a=r.getContext("2d",{willReadFrequently:!0}),o=(a.textBaseline="top",a.font="600 32px Arial",{});return e.forEach(function(e){o[e]=t(a,e,n)}),o}function t(e){var t=i.createElement("script");t.src=e,t.defer=!0,i.head.appendChild(t)}"undefined"!=typeof Promise&&(o="wpEmojiSettingsSupports",s=["flag","emoji"],n.supports={everything:!0,everythingExceptFlag:!0},e=new Promise(function(e){i.addEventListener("DOMContentLoaded",e,{once:!0})}),new Promise(function(t){var n=function(){try{var e=JSON.parse(sessionStorage.getItem(o));if("object"==typeof e&&"number"==typeof e.timestamp&&(new Date).valueOf()<e.timestamp+604800&&"object"==typeof e.supportTests)return e.supportTests}catch(e){}return null}();if(!n){if("undefined"!=typeof Worker&&"undefined"!=typeof OffscreenCanvas&&"undefined"!=typeof URL&&URL.createObjectURL&&"undefined"!=typeof Blob)try{var e="postMessage("+f.toString()+"("+[JSON.stringify(s),u.toString(),p.toString()].join(",")+"));",r=new Blob([e],{type:"text/javascript"}),a=new Worker(URL.createObjectURL(r),{name:"wpTestEmojiSupports"});return void(a.onmessage=function(e){c(n=e.data),a.terminate(),t(n)})}catch(e){}c(n=f(s,u,p))}t(n)}).then(function(e){for(var t in e)n.supports[t]=e[t],n.supports.everything=n.supports.everything&&n.supports[t],"flag"!==t&&(n.supports.everythingExceptFlag=n.supports.everythingExceptFlag&&n.supports[t]);n.supports.everythingExceptFlag=n.supports.everythingExceptFlag&&!n.supports.flag,n.DOMReady=!1,n.readyCallback=function(){n.DOMReady=!0}}).then(function(){return e}).then(function(){var e;n.supports.everything||(n.readyCallback(),(e=n.source||{}).concatemoji?t(e.concatemoji):e.wpemoji&&e.twemoji&&(t(e.twemoji),t(e.wpemoji)))}))}((window,document),window._wpemojiSettings);
/* ]]> */
</script>
<link crossorigin='anonymous' rel='stylesheet' id='all-css-0-1' href='https://s0.wp.com/_static/??-eJxtzFsKgDAMRNENWaNg0X6IayltEPvGpLh9UQRB/Bk4MFw4ijA5MSaGWEUJdd0SgUMu2vjHEHO+xtaABDsGzWhFycQftYaogf9k2Dy+4VvXfYlzPw5SdkrJyZ1gtTTl&cssminify=yes' type='text/css' media='all' />
<style id='wp-emoji-styles-inline-css'>

	img.wp-smiley, img.emoji {
		display: inline !important;
		border: none !important;
		box-shadow: none !important;
		height: 1em !important;
		width: 1em !important;
		margin: 0 0.07em !important;
		vertical-align: -0.1em !important;
		background: none !important;
		padding: 0 !important;
	}
</style>
<link crossorigin='anonymous' rel='stylesheet' id='all-css-2-1' href='https://s0.wp.com/_static/??-eJylzFsKgCAQQNENlWNhr49oLWmDWZOKj6LdF22hz8uFA5cvlbMJbQJPWRsbQec3JQb9noBw1py1jIPMhhaQ5NRekpFhDjfEdBMyFWMB/6C04vFB0zFWnRBDM/C+2h4i4jhW&cssminify=yes' type='text/css' media='all' />
<style id='wp-block-library-inline-css'>
.has-text-align-justify {
	text-align:justify;
}
.has-text-align-justify{text-align:justify;}
</style>
<style id='classic-theme-styles-inline-css'>
/*! This file is auto-generated */
.wp-block-button__link{color:#fff;background-color:#32373c;border-radius:9999px;box-shadow:none;text-decoration:none;padding:calc(.667em + 2px) calc(1.333em + 2px);font-size:1.125em}.wp-block-file__button{background:#32373c;color:#fff;text-decoration:none}
</style>
<link crossorigin='anonymous' rel='stylesheet' id='all-css-4-1' href='https://s0.wp.com/_static/??-eJyVzDEOgCAMQNELiQU1Jg7Gs2AlDYpAaInx9ro5O/7hfbiywhTFRYEcKvnIQPXN1RVSa0h4MOxOssVDBXunKoqK34DlDq5F5gb+LIoVH4k/vpyzGYe+03oyw/4AcmU1KQ==&cssminify=yes' type='text/css' media='all' />
<link crossorigin='anonymous' rel='stylesheet' id='all-css-6-1' href='https://s0.wp.com/_static/??-eJzTLy/QzcxLzilNSS3WzyrWz01NyUxMzUnNTc0rQeEU5CRWphbp5qSmJyZX6uVm5uklFxfr6OPTDpRD5sM02efaGpoZmFkYGRuZGmQBAHPvL0Y=&cssminify=yes' type='text/css' media='all' />
<style id='jetpack-sharing-buttons-style-inline-css'>
.jetpack-sharing-buttons__services-list{display:flex;flex-direction:row;flex-wrap:wrap;gap:0;list-style-type:none;margin:5px;padding:0}.jetpack-sharing-buttons__services-list.has-small-icon-size{font-size:12px}.jetpack-sharing-buttons__services-list.has-normal-icon-size{font-size:16px}.jetpack-sharing-buttons__services-list.has-large-icon-size{font-size:24px}.jetpack-sharing-buttons__services-list.has-huge-icon-size{font-size:36px}@media print{.jetpack-sharing-buttons__services-list{display:none!important}}.editor-styles-wrapper .wp-block-jetpack-sharing-buttons{gap:0;padding-inline-start:0}ul.jetpack-sharing-buttons__services-list.has-background{padding:1.25em 2.375em}
</style>
<link crossorigin='anonymous' rel='stylesheet' id='all-css-8-1' href='https://s0.wp.com/_static/??/wp-content/mu-plugins/core-compat/wp-mediaelement.css,/wp-content/mu-plugins/wpcom-bbpress-premium-themes.css?m=1432920480j&cssminify=yes' type='text/css' media='all' />
<style id='global-styles-inline-css'>
:root{--wp--preset--aspect-ratio--square: 1;--wp--preset--aspect-ratio--4-3: 4/3;--wp--preset--aspect-ratio--3-4: 3/4;--wp--preset--aspect-ratio--3-2: 3/2;--wp--preset--aspect-ratio--2-3: 2/3;--wp--preset--aspect-ratio--16-9: 16/9;--wp--preset--aspect-ratio--9-16: 9/16;--wp--preset--color--black: #000000;--wp--preset--color--cyan-bluish-gray: #abb8c3;--wp--preset--color--white: #fff;--wp--preset--color--pale-pink: #f78da7;--wp--preset--color--vivid-red: #cf2e2e;--wp--preset--color--luminous-vivid-orange: #ff6900;--wp--preset--color--luminous-vivid-amber: #fcb900;--wp--preset--color--light-green-cyan: #7bdcb5;--wp--preset--color--vivid-green-cyan: #00d084;--wp--preset--color--pale-cyan-blue: #8ed1fc;--wp--preset--color--vivid-cyan-blue: #0693e3;--wp--preset--color--vivid-purple: #9b51e0;--wp--preset--color--medium-blue: #0087be;--wp--preset--color--bright-blue: #00aadc;--wp--preset--color--dark-gray: #4d4d4b;--wp--preset--color--light-gray: #b3b3b1;--wp--preset--gradient--vivid-cyan-blue-to-vivid-purple: linear-gradient(135deg,rgba(6,147,227,1) 0%,rgb(155,81,224) 100%);--wp--preset--gradient--light-green-cyan-to-vivid-green-cyan: linear-gradient(135deg,rgb(122,220,180) 0%,rgb(0,208,130) 100%);--wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange: linear-gradient(135deg,rgba(252,185,0,1) 0%,rgba(255,105,0,1) 100%);--wp--preset--gradient--luminous-vivid-orange-to-vivid-red: linear-gradient(135deg,rgba(255,105,0,1) 0%,rgb(207,46,46) 100%);--wp--preset--gradient--very-light-gray-to-cyan-bluish-gray: linear-gradient(135deg,rgb(238,238,238) 0%,rgb(169,184,195) 100%);--wp--preset--gradient--cool-to-warm-spectrum: linear-gradient(135deg,rgb(74,234,220) 0%,rgb(151,120,209) 20%,rgb(207,42,186) 40%,rgb(238,44,130) 60%,rgb(251,105,98) 80%,rgb(254,248,76) 100%);--wp--preset--gradient--blush-light-purple: linear-gradient(135deg,rgb(255,206,236) 0%,rgb(152,150,240) 100%);--wp--preset--gradient--blush-bordeaux: linear-gradient(135deg,rgb(254,205,165) 0%,rgb(254,45,45) 50%,rgb(107,0,62) 100%);--wp--preset--gradient--luminous-dusk: linear-gradient(135deg,rgb(255,203,112) 0%,rgb(199,81,192) 50%,rgb(65,88,208) 100%);--wp--preset--gradient--pale-ocean: linear-gradient(135deg,rgb(255,245,203) 0%,rgb(182,227,212) 50%,rgb(51,167,181) 100%);--wp--preset--gradient--electric-grass: linear-gradient(135deg,rgb(202,248,128) 0%,rgb(113,206,126) 100%);--wp--preset--gradient--midnight: linear-gradient(135deg,rgb(2,3,129) 0%,rgb(40,116,252) 100%);--wp--preset--font-size--small: 13px;--wp--preset--font-size--medium: 20px;--wp--preset--font-size--large: 36px;--wp--preset--font-size--x-large: 42px;--wp--preset--font-family--albert-sans: 'Albert Sans', sans-serif;--wp--preset--font-family--alegreya: Alegreya, serif;--wp--preset--font-family--arvo: Arvo, serif;--wp--preset--font-family--bodoni-moda: 'Bodoni Moda', serif;--wp--preset--font-family--bricolage-grotesque: 'Bricolage Grotesque', sans-serif;--wp--preset--font-family--cabin: Cabin, sans-serif;--wp--preset--font-family--chivo: Chivo, sans-serif;--wp--preset--font-family--commissioner: Commissioner, sans-serif;--wp--preset--font-family--cormorant: Cormorant, serif;--wp--preset--font-family--courier-prime: 'Courier Prime', monospace;--wp--preset--font-family--crimson-pro: 'Crimson Pro', serif;--wp--preset--font-family--dm-mono: 'DM Mono', monospace;--wp--preset--font-family--dm-sans: 'DM Sans', sans-serif;--wp--preset--font-family--dm-serif-display: 'DM Serif Display', serif;--wp--preset--font-family--domine: Domine, serif;--wp--preset--font-family--eb-garamond: 'EB Garamond', serif;--wp--preset--font-family--epilogue: Epilogue, sans-serif;--wp--preset--font-family--fahkwang: Fahkwang, sans-serif;--wp--preset--font-family--figtree: Figtree, sans-serif;--wp--preset--font-family--fira-sans: 'Fira Sans', sans-serif;--wp--preset--font-family--fjalla-one: 'Fjalla One', sans-serif;--wp--preset--font-family--fraunces: Fraunces, serif;--wp--preset--font-family--gabarito: Gabarito, system-ui;--wp--preset--font-family--ibm-plex-mono: 'IBM Plex Mono', monospace;--wp--preset--font-family--ibm-plex-sans: 'IBM Plex Sans', sans-serif;--wp--preset--font-family--ibarra-real-nova: 'Ibarra Real Nova', serif;--wp--preset--font-family--instrument-serif: 'Instrument Serif', serif;--wp--preset--font-family--inter: Inter, sans-serif;--wp--preset--font-family--josefin-sans: 'Josefin Sans', sans-serif;--wp--preset--font-family--jost: Jost, sans-serif;--wp--preset--font-family--libre-baskerville: 'Libre Baskerville', serif;--wp--preset--font-family--libre-franklin: 'Libre Franklin', sans-serif;--wp--preset--font-family--literata: Literata, serif;--wp--preset--font-family--lora: Lora, serif;--wp--preset--font-family--merriweather: Merriweather, serif;--wp--preset--font-family--montserrat: Montserrat, sans-serif;--wp--preset--font-family--newsreader: Newsreader, serif;--wp--preset--font-family--noto-sans-mono: 'Noto Sans Mono', sans-serif;--wp--preset--font-family--nunito: Nunito, sans-serif;--wp--preset--font-family--open-sans: 'Open Sans', sans-serif;--wp--preset--font-family--overpass: Overpass, sans-serif;--wp--preset--font-family--pt-serif: 'PT Serif', serif;--wp--preset--font-family--petrona: Petrona, serif;--wp--preset--font-family--piazzolla: Piazzolla, serif;--wp--preset--font-family--playfair-display: 'Playfair Display', serif;--wp--preset--font-family--plus-jakarta-sans: 'Plus Jakarta Sans', sans-serif;--wp--preset--font-family--poppins: Poppins, sans-serif;--wp--preset--font-family--raleway: Raleway, sans-serif;--wp--preset--font-family--roboto: Roboto, sans-serif;--wp--preset--font-family--roboto-slab: 'Roboto Slab', serif;--wp--preset--font-family--rubik: Rubik, sans-serif;--wp--preset--font-family--rufina: Rufina, serif;--wp--preset--font-family--sora: Sora, sans-serif;--wp--preset--font-family--source-sans-3: 'Source Sans 3', sans-serif;--wp--preset--font-family--source-serif-4: 'Source Serif 4', serif;--wp--preset--font-family--space-mono: 'Space Mono', monospace;--wp--preset--font-family--syne: Syne, sans-serif;--wp--preset--font-family--texturina: Texturina, serif;--wp--preset--font-family--urbanist: Urbanist, sans-serif;--wp--preset--font-family--work-sans: 'Work Sans', sans-serif;--wp--preset--spacing--20: 0.44rem;--wp--preset--spacing--30: 0.67rem;--wp--preset--spacing--40: 1rem;--wp--preset--spacing--50: 1.5rem;--wp--preset--spacing--60: 2.25rem;--wp--preset--spacing--70: 3.38rem;--wp--preset--spacing--80: 5.06rem;--wp--preset--shadow--natural: 6px 6px 9px rgba(0, 0, 0, 0.2);--wp--preset--shadow--deep: 12px 12px 50px rgba(0, 0, 0, 0.4);--wp--preset--shadow--sharp: 6px 6px 0px rgba(0, 0, 0, 0.2);--wp--preset--shadow--outlined: 6px 6px 0px -3px rgba(255, 255, 255, 1), 6px 6px rgba(0, 0, 0, 1);--wp--preset--shadow--crisp: 6px 6px 0px rgba(0, 0, 0, 1);}:where(.is-layout-flex){gap: 0.5em;}:where(.is-layout-grid){gap: 0.5em;}body .is-layout-flex{display: flex;}.is-layout-flex{flex-wrap: wrap;align-items: center;}.is-layout-flex > :is(*, div){margin: 0;}body .is-layout-grid{display: grid;}.is-layout-grid > :is(*, div){margin: 0;}:where(.wp-block-columns.is-layout-flex){gap: 2em;}:where(.wp-block-columns.is-layout-grid){gap: 2em;}:where(.wp-block-post-template.is-layout-flex){gap: 1.25em;}:where(.wp-block-post-template.is-layout-grid){gap: 1.25em;}.has-black-color{color: var(--wp--preset--color--black) !important;}.has-cyan-bluish-gray-color{color: var(--wp--preset--color--cyan-bluish-gray) !important;}.has-white-color{color: var(--wp--preset--color--white) !important;}.has-pale-pink-color{color: var(--wp--preset--color--pale-pink) !important;}.has-vivid-red-color{color: var(--wp--preset--color--vivid-red) !important;}.has-luminous-vivid-orange-color{color: var(--wp--preset--color--luminous-vivid-orange) !important;}.has-luminous-vivid-amber-color{color: var(--wp--preset--color--luminous-vivid-amber) !important;}.has-light-green-cyan-color{color: var(--wp--preset--color--light-green-cyan) !important;}.has-vivid-green-cyan-color{color: var(--wp--preset--color--vivid-green-cyan) !important;}.has-pale-cyan-blue-color{color: var(--wp--preset--color--pale-cyan-blue) !important;}.has-vivid-cyan-blue-color{color: var(--wp--preset--color--vivid-cyan-blue) !important;}.has-vivid-purple-color{color: var(--wp--preset--color--vivid-purple) !important;}.has-black-background-color{background-color: var(--wp--preset--color--black) !important;}.has-cyan-bluish-gray-background-color{background-color: var(--wp--preset--color--cyan-bluish-gray) !important;}.has-white-background-color{background-color: var(--wp--preset--color--white) !important;}.has-pale-pink-background-color{background-color: var(--wp--preset--color--pale-pink) !important;}.has-vivid-red-background-color{background-color: var(--wp--preset--color--vivid-red) !important;}.has-luminous-vivid-orange-background-color{background-color: var(--wp--preset--color--luminous-vivid-orange) !important;}.has-luminous-vivid-amber-background-color{background-color: var(--wp--preset--color--luminous-vivid-amber) !important;}.has-light-green-cyan-background-color{background-color: var(--wp--preset--color--light-green-cyan) !important;}.has-vivid-green-cyan-background-color{background-color: var(--wp--preset--color--vivid-green-cyan) !important;}.has-pale-cyan-blue-background-color{background-color: var(--wp--preset--color--pale-cyan-blue) !important;}.has-vivid-cyan-blue-background-color{background-color: var(--wp--preset--color--vivid-cyan-blue) !important;}.has-vivid-purple-background-color{background-color: var(--wp--preset--color--vivid-purple) !important;}.has-black-border-color{border-color: var(--wp--preset--color--black) !important;}.has-cyan-bluish-gray-border-color{border-color: var(--wp--preset--color--cyan-bluish-gray) !important;}.has-white-border-color{border-color: var(--wp--preset--color--white) !important;}.has-pale-pink-border-color{border-color: var(--wp--preset--color--pale-pink) !important;}.has-vivid-red-border-color{border-color: var(--wp--preset--color--vivid-red) !important;}.has-luminous-vivid-orange-border-color{border-color: var(--wp--preset--color--luminous-vivid-orange) !important;}.has-luminous-vivid-amber-border-color{border-color: var(--wp--preset--color--luminous-vivid-amber) !important;}.has-light-green-cyan-border-color{border-color: var(--wp--preset--color--light-green-cyan) !important;}.has-vivid-green-cyan-border-color{border-color: var(--wp--preset--color--vivid-green-cyan) !important;}.has-pale-cyan-blue-border-color{border-color: var(--wp--preset--color--pale-cyan-blue) !important;}.has-vivid-cyan-blue-border-color{border-color: var(--wp--preset--color--vivid-cyan-blue) !important;}.has-vivid-purple-border-color{border-color: var(--wp--preset--color--vivid-purple) !important;}.has-vivid-cyan-blue-to-vivid-purple-gradient-background{background: var(--wp--preset--gradient--vivid-cyan-blue-to-vivid-purple) !important;}.has-light-green-cyan-to-vivid-green-cyan-gradient-background{background: var(--wp--preset--gradient--light-green-cyan-to-vivid-green-cyan) !important;}.has-luminous-vivid-amber-to-luminous-vivid-orange-gradient-background{background: var(--wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange) !important;}.has-luminous-vivid-orange-to-vivid-red-gradient-background{background: var(--wp--preset--gradient--luminous-vivid-orange-to-vivid-red) !important;}.has-very-light-gray-to-cyan-bluish-gray-gradient-background{background: var(--wp--preset--gradient--very-light-gray-to-cyan-bluish-gray) !important;}.has-cool-to-warm-spectrum-gradient-background{background: var(--wp--preset--gradient--cool-to-warm-spectrum) !important;}.has-blush-light-purple-gradient-background{background: var(--wp--preset--gradient--blush-light-purple) !important;}.has-blush-bordeaux-gradient-background{background: var(--wp--preset--gradient--blush-bordeaux) !important;}.has-luminous-dusk-gradient-background{background: var(--wp--preset--gradient--luminous-dusk) !important;}.has-pale-ocean-gradient-background{background: var(--wp--preset--gradient--pale-ocean) !important;}.has-electric-grass-gradient-background{background: var(--wp--preset--gradient--electric-grass) !important;}.has-midnight-gradient-background{background: var(--wp--preset--gradient--midnight) !important;}.has-small-font-size{font-size: var(--wp--preset--font-size--small) !important;}.has-medium-font-size{font-size: var(--wp--preset--font-size--medium) !important;}.has-large-font-size{font-size: var(--wp--preset--font-size--large) !important;}.has-x-large-font-size{font-size: var(--wp--preset--font-size--x-large) !important;}.has-albert-sans-font-family{font-family: var(--wp--preset--font-family--albert-sans) !important;}.has-alegreya-font-family{font-family: var(--wp--preset--font-family--alegreya) !important;}.has-arvo-font-family{font-family: var(--wp--preset--font-family--arvo) !important;}.has-bodoni-moda-font-family{font-family: var(--wp--preset--font-family--bodoni-moda) !important;}.has-bricolage-grotesque-font-family{font-family: var(--wp--preset--font-family--bricolage-grotesque) !important;}.has-cabin-font-family{font-family: var(--wp--preset--font-family--cabin) !important;}.has-chivo-font-family{font-family: var(--wp--preset--font-family--chivo) !important;}.has-commissioner-font-family{font-family: var(--wp--preset--font-family--commissioner) !important;}.has-cormorant-font-family{font-family: var(--wp--preset--font-family--cormorant) !important;}.has-courier-prime-font-family{font-family: var(--wp--preset--font-family--courier-prime) !important;}.has-crimson-pro-font-family{font-family: var(--wp--preset--font-family--crimson-pro) !important;}.has-dm-mono-font-family{font-family: var(--wp--preset--font-family--dm-mono) !important;}.has-dm-sans-font-family{font-family: var(--wp--preset--font-family--dm-sans) !important;}.has-dm-serif-display-font-family{font-family: var(--wp--preset--font-family--dm-serif-display) !important;}.has-domine-font-family{font-family: var(--wp--preset--font-family--domine) !important;}.has-eb-garamond-font-family{font-family: var(--wp--preset--font-family--eb-garamond) !important;}.has-epilogue-font-family{font-family: var(--wp--preset--font-family--epilogue) !important;}.has-fahkwang-font-family{font-family: var(--wp--preset--font-family--fahkwang) !important;}.has-figtree-font-family{font-family: var(--wp--preset--font-family--figtree) !important;}.has-fira-sans-font-family{font-family: var(--wp--preset--font-family--fira-sans) !important;}.has-fjalla-one-font-family{font-family: var(--wp--preset--font-family--fjalla-one) !important;}.has-fraunces-font-family{font-family: var(--wp--preset--font-family--fraunces) !important;}.has-gabarito-font-family{font-family: var(--wp--preset--font-family--gabarito) !important;}.has-ibm-plex-mono-font-family{font-family: var(--wp--preset--font-family--ibm-plex-mono) !important;}.has-ibm-plex-sans-font-family{font-family: var(--wp--preset--font-family--ibm-plex-sans) !important;}.has-ibarra-real-nova-font-family{font-family: var(--wp--preset--font-family--ibarra-real-nova) !important;}.has-instrument-serif-font-family{font-family: var(--wp--preset--font-family--instrument-serif) !important;}.has-inter-font-family{font-family: var(--wp--preset--font-family--inter) !important;}.has-josefin-sans-font-family{font-family: var(--wp--preset--font-family--josefin-sans) !important;}.has-jost-font-family{font-family: var(--wp--preset--font-family--jost) !important;}.has-libre-baskerville-font-family{font-family: var(--wp--preset--font-family--libre-baskerville) !important;}.has-libre-franklin-font-family{font-family: var(--wp--preset--font-family--libre-franklin) !important;}.has-literata-font-family{font-family: var(--wp--preset--font-family--literata) !important;}.has-lora-font-family{font-family: var(--wp--preset--font-family--lora) !important;}.has-merriweather-font-family{font-family: var(--wp--preset--font-family--merriweather) !important;}.has-montserrat-font-family{font-family: var(--wp--preset--font-family--montserrat) !important;}.has-newsreader-font-family{font-family: var(--wp--preset--font-family--newsreader) !important;}.has-noto-sans-mono-font-family{font-family: var(--wp--preset--font-family--noto-sans-mono) !important;}.has-nunito-font-family{font-family: var(--wp--preset--font-family--nunito) !important;}.has-open-sans-font-family{font-family: var(--wp--preset--font-family--open-sans) !important;}.has-overpass-font-family{font-family: var(--wp--preset--font-family--overpass) !important;}.has-pt-serif-font-family{font-family: var(--wp--preset--font-family--pt-serif) !important;}.has-petrona-font-family{font-family: var(--wp--preset--font-family--petrona) !important;}.has-piazzolla-font-family{font-family: var(--wp--preset--font-family--piazzolla) !important;}.has-playfair-display-font-family{font-family: var(--wp--preset--font-family--playfair-display) !important;}.has-plus-jakarta-sans-font-family{font-family: var(--wp--preset--font-family--plus-jakarta-sans) !important;}.has-poppins-font-family{font-family: var(--wp--preset--font-family--poppins) !important;}.has-raleway-font-family{font-family: var(--wp--preset--font-family--raleway) !important;}.has-roboto-font-family{font-family: var(--wp--preset--font-family--roboto) !important;}.has-roboto-slab-font-family{font-family: var(--wp--preset--font-family--roboto-slab) !important;}.has-rubik-font-family{font-family: var(--wp--preset--font-family--rubik) !important;}.has-rufina-font-family{font-family: var(--wp--preset--font-family--rufina) !important;}.has-sora-font-family{font-family: var(--wp--preset--font-family--sora) !important;}.has-source-sans-3-font-family{font-family: var(--wp--preset--font-family--source-sans-3) !important;}.has-source-serif-4-font-family{font-family: var(--wp--preset--font-family--source-serif-4) !important;}.has-space-mono-font-family{font-family: var(--wp--preset--font-family--space-mono) !important;}.has-syne-font-family{font-family: var(--wp--preset--font-family--syne) !important;}.has-texturina-font-family{font-family: var(--wp--preset--font-family--texturina) !important;}.has-urbanist-font-family{font-family: var(--wp--preset--font-family--urbanist) !important;}.has-work-sans-font-family{font-family: var(--wp--preset--font-family--work-sans) !important;}
:where(.wp-block-columns.is-layout-flex){gap: 2em;}:where(.wp-block-columns.is-layout-grid){gap: 2em;}
:root :where(.wp-block-pullquote){font-size: 1.5em;line-height: 1.6;}
:where(.wp-block-post-template.is-layout-flex){gap: 1.25em;}:where(.wp-block-post-template.is-layout-grid){gap: 1.25em;}
</style>
<link crossorigin='anonymous' rel='stylesheet' id='all-css-10-1' href='https://s0.wp.com/wp-content/mu-plugins/jetpack-mu-wpcom-plugin/moon/jetpack_vendor/automattic/jetpack-mu-wpcom/src/build/verbum-comments/verbum-comments.css?m=1746611094i&cssminify=yes' type='text/css' media='all' />
<link rel='stylesheet' id='verbum-gutenberg-css-css' href='https://widgets.wp.com/verbum-block-editor/block-editor.css?ver=1738686361' media='all' />
<link crossorigin='anonymous' rel='stylesheet' id='all-css-12-1' href='https://s0.wp.com/_static/??-eJydUMsOgjAQ/CHrBmIQD8ZPMVDXstBX2hLi37uAUaOEA5dmZprZ2R0YvJDOJrQJTC+87hXZCNIZw5LQ1CGz+KPsWdkBgXWJ2B3fYP5Yntli8pXsXhyMcxauZCUotBjmOYvwb2hq0PBavq+B7A098sOrMdcUGwwih5geGrcYx1tr7WS3KXY8Z4oWg+fG1uoIyDGKoZoyP3TNpNAJXq5KxOV9E3HXFYXRejHn7HgoyvxUZkX7BFJLtEU=&cssminify=yes' type='text/css' media='all' />
<style id='independent-publisher-2-style-inline-css'>
#hero-header { background: url("https://ghostswa.au/wp-content/uploads/2023/09/cropped-cossack_wa_ghost_town.jpg") no-repeat center; background-size: cover; background-attachment: scroll; }
.byline { clip: rect(1px, 1px, 1px, 1px); height: 1px; position: absolute; overflow: hidden; width: 1px; }
</style>
<link crossorigin='anonymous' rel='stylesheet' id='print-css-13-1' href='https://s0.wp.com/wp-content/mu-plugins/global-print/global-print.css?m=1465851035i&cssminify=yes' type='text/css' media='print' />
<style id='jetpack-global-styles-frontend-style-inline-css'>
:root { --font-headings: unset; --font-base: unset; --font-headings-default: -apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif; --font-base-default: -apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif;}
</style>
<link crossorigin='anonymous' rel='stylesheet' id='all-css-16-1' href='https://s0.wp.com/_static/??-eJyNjcEKAjEMRH/IGlaWdT2InyI1DW3XNCmmRfx7XfEiXrwM82B4A/fqUKWRNCjdVe4xi8FCrXq8fhiK6hqhMxlY8jcKPoTHu2aJWzTbwP+mcxYEU8yeHWtU+4IfW0tUXr9phMh68bwOTuU47Mdp3h3mYVqeUXNJMg==&cssminify=yes' type='text/css' media='all' />
<script type="text/javascript" id="jetpack_related-posts-js-extra">
/* <![CDATA[ */
var related_posts_js_options = {"post_heading":"h4"};
/* ]]> */
</script>
<script type="text/javascript" id="jetpack-mu-wpcom-settings-js-before">
/* <![CDATA[ */
var JETPACK_MU_WPCOM_SETTINGS = {"assetsUrl":"https:\/\/s0.wp.com\/wp-content\/mu-plugins\/jetpack-mu-wpcom-plugin\/moon\/jetpack_vendor\/automattic\/jetpack-mu-wpcom\/src\/build\/"};
/* ]]> */
</script>
<script crossorigin='anonymous' type='text/javascript'  src='https://s0.wp.com/_static/??/wp-content/mu-plugins/jetpack-plugin/moon/_inc/build/related-posts/related-posts.min.js,/wp-content/js/rlt-proxy.js?m=1733762913j'></script>
<script type="text/javascript" id="rlt-proxy-js-after">
/* <![CDATA[ */
	rltInitialize( {"token":null,"iframeOrigins":["https:\/\/widgets.wp.com"]} );
/* ]]> */
</script>
<script type="text/javascript" crossorigin='anonymous' src="https://s0.wp.com/wp-content/plugins/gutenberg-core/v20.6.0/build/hooks/index.min.js?m=1744959081i&amp;ver=84e753e2b66eb7028d38" id="wp-hooks-js"></script>
<script type="text/javascript" crossorigin='anonymous' src="https://s0.wp.com/wp-content/plugins/gutenberg-core/v20.6.0/build/i18n/index.min.js?m=1744959081i&amp;ver=bd5a2533e717a1043151" id="wp-i18n-js"></script>
<script type="text/javascript" id="wp-i18n-js-after">
/* <![CDATA[ */
wp.i18n.setLocaleData( { 'text direction\u0004ltr': [ 'ltr' ] } );
/* ]]> */
</script>
<script type="text/javascript" id="wpcom-actionbar-placeholder-js-extra">
/* <![CDATA[ */
var actionbardata = {"siteID":"*********","postID":"3719","siteURL":"https:\/\/ghostswa.au","xhrURL":"https:\/\/ghostswa.au\/wp-admin\/admin-ajax.php","nonce":"bedc8b9787","isLoggedIn":"","statusMessage":"","subsEmailDefault":"instantly","proxyScriptUrl":"https:\/\/s0.wp.com\/wp-content\/js\/wpcom-proxy-request.js?ver=20211021","shortlink":"https:\/\/wp.me\/pf5S7V-XZ","i18n":{"followedText":"New posts from this site will now appear in your <a href=\"https:\/\/wordpress.com\/reader\">Reader<\/a>","foldBar":"Collapse this bar","unfoldBar":"Expand this bar","shortLinkCopied":"Shortlink copied to clipboard."}};
/* ]]> */
</script>
<link rel="EditURI" type="application/rsd+xml" title="RSD" href="https://ghostswa.wordpress.com/xmlrpc.php?rsd" />
<meta name="generator" content="WordPress.com" />
<link rel="canonical" href="https://ghostswa.au/2025/06/12/otd18970612/" />
<link rel='shortlink' href='https://wp.me/pf5S7V-XZ' />
<link rel="alternate" type="application/json+oembed" href="https://public-api.wordpress.com/oembed/?format=json&amp;url=https%3A%2F%2Fghostswa.au%2F2025%2F06%2F12%2Fotd18970612%2F&amp;for=wpcom-auto-discovery" /><link rel="alternate" type="application/xml+oembed" href="https://public-api.wordpress.com/oembed/?format=xml&amp;url=https%3A%2F%2Fghostswa.au%2F2025%2F06%2F12%2Fotd18970612%2F&amp;for=wpcom-auto-discovery" />
<!-- Jetpack Open Graph Tags -->
<meta property="og:type" content="article" />
<meta property="og:title" content="Cossack &#8211; 12 June 1897" />
<meta property="og:url" content="https://ghostswa.au/2025/06/12/otd18970612/" />
<meta property="og:description" content="On this day in 1897, Roebourne defeated Cossack in a close match." />
<meta property="article:published_time" content="2025-06-11T23:40:00+00:00" />
<meta property="article:modified_time" content="2025-06-11T12:02:32+00:00" />
<meta property="og:site_name" content="People of Western Australia&#039;s Ghost Towns" />
<meta property="og:image" content="https://ghostswa.au/wp-content/uploads/2025/06/slwa_b4721770_2.jpg" />
<meta property="og:image:width" content="760" />
<meta property="og:image:height" content="522" />
<meta property="og:image:alt" content="" />
<meta property="og:locale" content="en_US" />
<meta property="article:publisher" content="https://www.facebook.com/WordPresscom" />
<meta name="twitter:creator" content="@ghostswa" />
<meta name="twitter:site" content="@ghostswa" />
<meta name="twitter:text:title" content="Cossack &#8211; 12 June&nbsp;1897" />
<meta name="twitter:image" content="https://ghostswa.au/wp-content/uploads/2025/06/slwa_b4721770_2.jpg?w=640" />
<meta name="twitter:card" content="summary_large_image" />

<!-- End Jetpack Open Graph Tags -->
<link rel="shortcut icon" type="image/x-icon" href="https://s0.wp.com/i/favicon.ico" sizes="16x16 24x24 32x32 48x48" />
<link rel="icon" type="image/x-icon" href="https://s0.wp.com/i/favicon.ico" sizes="16x16 24x24 32x32 48x48" />
<link rel="apple-touch-icon" href="https://s0.wp.com/i/webclip.png" />
<link rel="search" type="application/opensearchdescription+xml" href="https://ghostswa.au/osd.xml" title="People of Western Australia&#039;s Ghost Towns" />
<link rel="search" type="application/opensearchdescription+xml" href="https://s1.wp.com/opensearch.xml" title="WordPress.com" />
<meta name="theme-color" content="#f9f9f9" />
<link rel="pingback" href="https://ghostswa.au/xmlrpc.php"><meta name="description" content="On this day in 1897, Roebourne defeated Cossack in a close match." />
<style type="text/css" id="custom-background-css">
body.custom-background { background-color: #f9f9f9; }
</style>
	<style type="text/css" id="custom-colors-css">    .has-header-image .site-title a,
    .has-header-image .site-title a:visited {
        color: #fff;
    }

    @media screen and ( max-width: 32.374em ) {
        .main-navigation ul ul {
            background: transparent !important;
        }
        .main-navigation ul ul a {
            color: inherit !important;
        }
    }
  .widget_recent_comments a,
            .widget_recent_entries a,
            body,
            input,
            select,
            textarea,
            .menu-toggle { color: #383838;}
#infinite-footer .blog-info a:hover,
            #infinite-footer .blog-credits a:hover { color: #383838;}
.posts-navigation .nav-links a,
            .main-navigation ul ul a,
            .main-navigation > div > ul > li.current-menu-item > ul > li a,
            .main-navigation > div > ul > li.current_page_item > ul > li a { color: #FFFFFF;}
input[type="button"],
            input[type="button"]:hover,
            input[type="reset"],
            input[type="reset"]:hover,
            input[type="submit"],
            input[type="submit"]:hover,
            button,
            .button,
            .button:hover,
            #content #infinite-handle span button,
            #content #infinite-handle span button:hover,
            .more-link,
            .more-link:hover,
            .more-link:visited { color: #232323;}
.site-main > .hentry:nth-child(n+2), .site .infinite-wrap > .hentry:nth-child(n+2),
            .entry-author-wrapper,
            .post-navigation,
            .comment,
            .page-links a:hover,
            .main-navigation li { border-color: #dddddd;}
.site-main > .hentry:nth-child(n+2), .site .infinite-wrap > .hentry:nth-child(n+2),
            .entry-author-wrapper,
            .post-navigation,
            .comment,
            .page-links a:hover,
            .main-navigation li { border-color: rgba( 221, 221, 221, 0.25 );}
#infinite-footer .blog-info a,
            #infinite-footer .blog-credits,
            #infinite-footer .blog-credits a { color: #6B6B6B;}
.post-details,
            .post-details a,
            .post-details a:visited,
            .post-edit-link a,
            .post-edit-link a:visited { color: #696967;}
.post-tags li:first-child,
            .jetpack-social-navigation li a:hover,
            .widget_wpcom_social_media_icons_widget li a:hover,
            .jetpack-social-navigation li a:focus,
            .widget_wpcom_social_media_icons_widget li a:focus,
            .jetpack-social-navigation li a:active,
            .widget_wpcom_social_media_icons_widget li a:active { color: #515151;}
.jetpack-social-navigation li a,
            .widget_wpcom_social_media_icons_widget li a { color: #6B6B6B;}
.post-navigation .nav-links a:hover,
            .post-navigation .nav-links a:focus,
            .post-navigation .nav-links a:active,
            .entry-author .author-bio,
            .site-posted-on time,
            .site-description { color: #6B6B6B;}
.comment .comment-meta,
            .comment-form label,
            .light-text,
            .light-text a,
            .light-text a:visited,
            .widget_rss .rss-date,
            .widget_rss li > cite { color: #696967;}
.light-text a:hover { color: #696967;}
body { background-color: #f9f9f9;}
#infinite-footer .container { background-color: #f9f9f9;}
#infinite-footer .container { background-color: rgba( 249, 249, 249, 0.7 );}
.post-edit-link a { background-color: #F4F4F4;}
.entry-author .author-title,
            .entry-title,
            .entry-title a,
            .entry-title a:visited,
            .site-posted-on strong,
            .site-title,
            .site-title a,
            .site-title a:visited,
            .entry-title a:hover,
            .site-title a:hover,
            h1,
            h2,
            h3,
            h4,
            h5,
            h6,
            .page-header:not(.page-header-light) h1,
            .comment .comment-meta .comment-author .fn { color: #11457D;}
.comment-form input[type="email"]:active,
            .comment-form input[type="email"]:focus,
            .comment-form input[type="password"]:active,
            .comment-form input[type="password"]:focus,
            .comment-form input[type="search"]:active,
            .comment-form input[type="search"]:focus,
            .comment-form input[type="text"]:active,
            .comment-form input[type="text"]:focus,
            .comment-form input[type="url"]:active,
            .comment-form input[type="url"]:focus,
            .comment-form textarea:active,
            .comment-form textarea:focus,
            blockquote,
            input[type="email"]:focus,
            input[type="password"]:focus,
            input[type="search"]:focus,
            input[type="text"]:focus,
            input[type="url"]:focus,
            textarea:focus { border-color: #0006c7;}
.comment .comment-meta .comment-metadata a:hover,
            .comment-form span.required,
            .pingback:before,
            .post-details a:hover,
            .post-edit-link a:active,
            .post-edit-link a:focus,
            .post-edit-link a:hover,
            .site-info a:hover,
            .trackback:before,
            a,
            a:visited { color: #0006C7;}
.main-navigation > div > ul > li.current-menu-item > a,
            .main-navigation > div > ul > li.current_page_item > a,
            a:active,
            a:focus,
            a:hover,
            .page-links a:hover { color: #0006C7;}
.posts-navigation .nav-links a,
            .main-navigation ul ul { background-color: #0006c7;}
button,
            input[type="button"],
            input[type="reset"],
            input[type="submit"],
            .button,
            #content #infinite-handle span button,
            .more-link { background-color: #3ca2a2;}
button:not(".components-button"):hover,
            input[type="button"]:hover,
            input[type="reset"]:hover,
            input[type="submit"]:hover,
            .button:hover,
            #content #infinite-handle span button:hover,
            .more-link:hover { background-color: #2E7E7E;}
</style>
</head>

<body class="wp-singular post-template-default single single-post postid-3719 single-format-standard custom-background wp-embed-responsive wp-theme-pubindependent-publisher-2 customizer-styles-applied group-blog has-sidebar has-header-image jetpack-reblog-enabled author-hidden custom-colors">

<div id="page" class="hfeed site">
	<a class="skip-link screen-reader-text" href="#content">Skip to content</a>

	<div id="hero-header" class="site-hero-section">
		<header id="masthead" class="site-header" role="banner">
			<div class="inner">
				<div class="site-branding">
					
												<p class="site-title"><a href="https://ghostswa.au/" rel="home">People of Western Australia&#039;s Ghost Towns</a></p>
										</div><!-- .site-branding -->

				
									<button class="menu-toggle" aria-controls="primary-menu" aria-expanded="false" id="primary-menu-button">
						Menu					</button><!-- .menu-toggle -->
				
			</div><!-- .inner -->
		</header><!-- #masthead -->
	</div>

				<nav id="site-navigation" class="main-navigation" role="navigation">
			<div class="menu-menu-container"><ul id="primary-menu" class="menu"><li id="menu-item-3749" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-3749"><a href="https://ghostswa.au/goals/">Our Goals</a></li>
<li id="menu-item-933" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home menu-item-933"><a href="https://ghostswa.au/">Our Blog</a></li>
<li id="menu-item-198" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-has-children menu-item-198"><a href="https://ghostswa.au/town-list/">Our Towns List</a>
<ul class="sub-menu">
	<li id="menu-item-3510" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-3510"><a href="https://ghostswa.au/alphalist/">Alphabetical Listing</a></li>
	<li id="menu-item-3509" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-3509"><a href="https://ghostswa.au/regionlist/">By Location Listing</a></li>
</ul>
</li>
<li id="menu-item-2679" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-2679"><a href="https://ghostswa.au/our-partners/">Our Partners</a></li>
<li id="menu-item-3443" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-3443"><a href="https://ghostswa.au/contact/events/">Our Events</a></li>
<li id="menu-item-197" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-197"><a href="https://ghostswa.au/contact/">About Us</a></li>
<li id="menu-item-53" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-53"><a href="https://fhwa.org.au">FamilyHistoryWA</a></li>
</ul></div>		</nav><!-- .main-navigation -->
	
	
	
	<div id="content-wrapper" class="content-wrapper">
		<div id="content" class="site-content">

	<div id="primary" class="content-area">
		<main id="main" class="site-main" role="main">

		
			
<article id="post-3719" class="post-3719 post type-post status-publish format-standard hentry category-cossack tag-cossack tag-cricket tag-ghost-towns tag-history tag-on-this-day tag-roebourne tag-western-australia-2">
			<header class="entry-header">
			<h1 class="entry-title">Cossack &#8211; 12 June&nbsp;1897</h1>		</header><!-- .entry-header -->		<div class="entry-meta">
			<span class="byline">
				<a href="https://ghostswa.au/author/christinenharris/" title="Posts by Christine Harris" rel="author">Christine Harris</a>			</span>
							<span class="cat-links">
					<a href="https://ghostswa.au/category/ghost-towns/cossack/" rel="category tag">Cossack</a>				</span><!-- .cat-links -->
			
			
			<span class="published-on">
				<time class="entry-date published" datetime="2025-06-12T07:40:00+08:00">12 June, 2025</time><time class="updated" datetime="2025-06-11T20:02:32+08:00">11 June, 2025</time>			</span>

			<span class="word-count">7 Minutes</span>		</div><!-- .entry-meta -->
	
	<div class="entry-content">
		
<p>The following is a detailed report of a cricket match that took place on this day in 1897 at Cossack between the Cossack and Roebourne teams.  It was published in  Northern Public Opinion and Mining and Pastoral News on Saturday, 19 June 1897.<sup data-fn="f8180ae8-6aac-43ab-ad4a-845b79f02d44" class="fn"><a href="#f8180ae8-6aac-43ab-ad4a-845b79f02d44" id="f8180ae8-6aac-43ab-ad4a-845b79f02d44-link">1</a></sup></p>



<p><strong>CATCHES.</strong><br><em>By &#8220;SHORT SLIP.&#8221;</em></p>



<p>The Boebourne Rovers &#8220;trammed&#8221; to Cossack on Saturday. The first association match played at the port this season took place.</p>



<p>The game was well contested, and good feeling made for a close and exciting finish. A good number watched the game, but the fair sex had only one representative present. &#8216;Tis true for the poet when he describes the petticoat sporting fraternity as a &#8220;sit-me-on-the-bicycle sort of push.&#8221;</p>



<p>The Rovers proved the victors by eleven runs.</p>



<figure class="wp-block-image size-large"><img data-attachment-id="3723" data-permalink="https://ghostswa.au/2025/06/12/otd18970612/slwa_b4721770_2/" data-orig-file="https://ghostswa.au/wp-content/uploads/2025/06/slwa_b4721770_2.jpg" data-orig-size="760,522" data-comments-opened="1" data-image-meta="{&quot;aperture&quot;:&quot;0&quot;,&quot;credit&quot;:&quot;&quot;,&quot;camera&quot;:&quot;&quot;,&quot;caption&quot;:&quot;&quot;,&quot;created_timestamp&quot;:&quot;0&quot;,&quot;copyright&quot;:&quot;&quot;,&quot;focal_length&quot;:&quot;0&quot;,&quot;iso&quot;:&quot;0&quot;,&quot;shutter_speed&quot;:&quot;0&quot;,&quot;title&quot;:&quot;&quot;,&quot;orientation&quot;:&quot;0&quot;}" data-image-title="slwa_b4721770_2" data-image-description="" data-image-caption="" data-medium-file="https://ghostswa.au/wp-content/uploads/2025/06/slwa_b4721770_2.jpg?w=300" data-large-file="https://ghostswa.au/wp-content/uploads/2025/06/slwa_b4721770_2.jpg?w=760" width="760" height="522" src="https://ghostswa.au/wp-content/uploads/2025/06/slwa_b4721770_2.jpg?w=760" alt="" class="wp-image-3723" /><figcaption class="wp-element-caption">This photo shows the Roebourne &amp; Cossack cricket teams three years later in 1900.<sup data-fn="e10b719b-93b1-4706-9187-a2ddf7417631" class="fn"><a href="#e10b719b-93b1-4706-9187-a2ddf7417631" id="e10b719b-93b1-4706-9187-a2ddf7417631-link">2</a></sup></figcaption></figure>



<p>&#8220;Watty,&#8221; the Cossack barracker, was all there in his usual bass voice and sung himself hoarse. &#8220;Snap-shot&#8221; Renold, with his camera, took a &#8220;shot&#8221; at both teams, and &#8220;never smiled again.&#8221; He should have thrown the &#8220;X-rays&#8221; on them.</p>



<p>Like the &#8220;Arizona Kicker,&#8221; good old &#8220;Sol.&#8221; blessed the players with a beautiful day, and the Cossackites prepared a good wicket for the Boebourne boys.</p>



<p>Through the instrumentality of a few members of the home team, led by indefatigable &#8220;Donny,&#8221; a shed was erected on the ground, and the scorers were accommodated with a &#8220;cose&#8221; and a few &#8220;ax.l.&#8221; boxes to squat down on.</p>



<p>The Union Jack—or perhaps the Cossack coat-of-arms-out-of-pocket—was flying on top of the shed, whilst the decorations at the bottom comprised two fine jars of water (not of Babel, but of Nature).</p>



<p>There is no denying the fact that the pitch at Cossack, and also the fielding ground, is far superior to that of Boebourne, and if it always remained in the condition it was on Saturday, all matches could be played on the marsh.</p>



<p>The Rovers played a really well-combined game and deserved to win, whilst the Cossack lads defended splendidly and did everything in their power to avert defeat.</p>



<p>&#8220;Luck was agin’ them,&#8221; said Watty, after a whiskey and soda.</p>



<p>Whilst travelling with the cricketers on Saturday (not a Parliamentary team), our worthy and much-respected member, Mr. H. W. Sholl, M.L.A., opened his many-a-time generous heart and proved to those who travelled by the tram that he was a sportsman as well as a &#8220;Member of Parliament,&#8221; and a gentleman as well as a public &#8220;sarvint.&#8221; He informed the members of the B.C.C. that he was that day presenting a bat to the individual, of either team, who made the highest score in the match, for the sake of evincing some interest among the batsmen.</p>



<p>This goes to show that Mr. Sholl&#8217;s heart and soul were with them in their recreation, and he deserves the thanks of all true and honest sportsmen in both Boebourne and Cossack.</p>



<p>I may state that the genial skipper of the Rovers, &#8220;Sam&#8221; Hemingway, won the bat to which I refer, his score being 81, made by sterling cricket. I offer him my heartiest congratulations.</p>



<p>One of the most pleasing sights that I have yet seen on a North-West cricket ground appeared before me on Saturday. It was simply the Cossack &#8220;willow wielders&#8221; turning out in their true colours—wearing scalp-caps of yellow and black hue, and with a green kerchief (ould Oirish, begorra) round their waists.</p>



<p>It reminded me of an old English cricketer (Dr. Grace), who, when in Australia, had, on entering the Melbourne ground, decked himself with blue dungaree pants, and what he termed an &#8220;old physician&#8217;s waistband&#8221; (green). When the spectators eyed him, they shouted out, &#8220;We bar the Irish navvy!&#8221; But the good old medico held his peace, and the crowd silenced down.</p>



<p>The query is asked—&#8221;Why don&#8217;t the Boebourne teams play in their colours?&#8221; Echo answers—&#8221;They have none!&#8221;</p>



<p>At two o’clock, both captains met, and the coin was tossed, turning in favour of George Snook, who elected to bat. &#8220;Sam&#8221; Hemingway then led his men into the field, and at twenty minutes past two, a start was made.</p>



<p>&#8220;Uncle&#8221; Harding and &#8220;Bob&#8221; Selway were the first men to pad up and take strike for Cossack, while &#8220;Jim&#8221; Hubbard and &#8220;Jack&#8221; Keogh took the cudgels for the Rovers.</p>



<p>&#8220;Bob&#8221; did not celebrate a record reign at the crease. Taking strike to Hubbard, he received the dreaded &#8220;duck,&#8221; which he has for so long been unaccustomed to. This was rather unlucky for Cossack, to lose their best man by the first bell of the day, and there was &#8220;weeping and gnashing of teeth&#8221; when Bob returned to the shed.</p>



<blockquote class="wp-block-quote is-layout-flow wp-block-quote-is-layout-flow">
<p>&#8220;It was a splendidly pitched ball, and broke to the off, beating me all the way.&#8221; – <em>Selway</em></p>
</blockquote>



<p>&#8220;Donny&#8221; took the vacancy and, after making a dozen, was clean bowled by &#8220;Tommy&#8221; Molster, who was brought in from long-field to do the trundling. &#8220;Uncle&#8221; soon followed, being caught and bowled by &#8220;Sam&#8221; Hemingway. He had made nine by good cricket.</p>



<p>&#8220;Georgy&#8221; Fry and &#8220;Georgy&#8221; Brown, the two pavilion cricketers, made four and eight respectively. They were off duty (cricket, I mean) and did not take much advantage of their &#8220;staff.&#8221;</p>



<p>&#8220;Snoofie,&#8221; the skipper, played a free bat and made some very pretty strokes, notching fourteen before he drove one very hard to Jack Keogh, who accepted it. It was a splendid catch, and Jack received an ovation.</p>



<p>&#8220;Jum&#8221; Louden fell a victim to Tommy Molster, after breaking the shell of the &#8220;duck.&#8221;</p>



<p>&#8220;Watty&#8221; Moore surprised everyone by his fine exhibition, his leg hits being marvellous, and he received a cheer when he had carried out his bat for thirteen.</p>



<p>&#8220;Slurry&#8221; Wilson and &#8220;Carbine&#8221; Moore both succumbed to Hubbard before they had scored and joined the ranks of &#8220;Short-slip&#8217;s&#8221; spoon competition.</p>



<p>An amusing thing occurred while &#8220;Carbine&#8221; was batting. He played the first ball of Hubbard’s onto his cranium, and with a cricketer&#8217;s oath, the next one gave him a clip in the ear, but the third one hit the—<strong>w-i-c-k-e-t.</strong></p>



<p>&#8220;Herb.&#8221; Birch, who was put in last wicket down (who should have followed the seventh man), just reached double figures when he was snapped up by Jim Hubbard.</p>



<p>Tommy Molster, in the long field, made a &#8220;bolster,&#8221; fell down, and got stuck in a mud-hole. Here&#8217;s to him, with &#8220;Short-slip&#8217;s&#8221; sympathy!</p>



<blockquote class="wp-block-quote is-layout-flow wp-block-quote-is-layout-flow">
<p>The ball went off the cricket bat,<br>And travelled far away!<br>&#8220;Tom&#8221; Molster in his big white hat<br>Fell on it in the clay.</p>



<p>The clay was soft, the ball was round,<br>Poor &#8220;Tom&#8221; he couldn&#8217;t stir,<br>So all the boys they stood around<br>And left him in despair.</p>



<p>He with the ball at last did rise<br>With language that was wicked,<br>And told them that he&#8217;d cause surprise<br>When he got at the wicket.</p>



<p>Covered with mud, he took the ball<br>And bowled a maiden over,<br>And in the next surprised them all<br>By scattering bails in clover.</p>
</blockquote>



<p>For fielding, Church, Naish, and Hemingway were excellent, and it speaks well for Cossack&#8217;s wicket-keeper that a bye was not recorded in the innings.</p>



<p>The bowling honours for the Rovers were carried off by Jim Hubbard, who came out of the cupboard (his shell, I mean), and got the splendid average of 5 wickets for 11 runs. A word of praise is also due to Tommy Molster, his 2 wickets only costing him 7 runs.</p>



<p>After a blow, the Rovers commenced their innings, having to make 72 to win. &#8220;Bannerman&#8221; Raymond and &#8220;Sam&#8221; Hemingway were the first representatives, and the bowlers were Brown and Louden.</p>



<p>&#8220;Banner,&#8221; after his usual careful play, had the misfortune to snick one of Louden’s into the hands of Brown and retired with four to his credit.</p>



<p>The skipper played a very useful innings. It was really a treat to watch his well-timed strokes and neat cuts, and when he had reached 81, he was captured by Selway. &#8220;Sam&#8221; was received with three times three when he reached the shed.</p>



<p>&#8220;Jim&#8221; Hubbard, after making seven, was foolishly run out. &#8220;Bert&#8221; Naish trebled his misfortune on Saturday, falling a victim to Brown for a duck.</p>



<p>&#8220;Herb.&#8221; Church, who had received a nasty blow on the left cheek through coming into contact with the ball, played a good innings and was bowled by Fry for a well-made 18.</p>



<p>&#8220;Jack&#8221; Keogh fell a victim to Fry for five, and &#8220;Burly&#8221; bowed to the same bowler for a unit. &#8220;Dawesie,&#8221; who must be credited with making the winning hit, cried &#8216;nough to Fry for 11. A.E.D. hit a fiver, which was the biggest hit of the day.</p>



<p>&#8220;Willie&#8221; Fuller threw his bat at Donny (unintentionally), and after making a single was bowled by Walter Moore. &#8220;Jack&#8221; Wotherspoon &#8220;spooned&#8221; the ball into the &#8220;dukes&#8221; of Fry.</p>



<blockquote class="wp-block-quote is-layout-flow wp-block-quote-is-layout-flow">
<p><strong>Pass along the banjo!</strong><br>&#8220;Oh Jack, why did you hit that ball?&#8221;<br>Cried Boebourne Rovers one and all.<br>&#8220;I went to place it to the leg,&#8221;<br>Said poor old &#8220;Wother,&#8221; with his egg.<br>The game was o’er, the match was won—<br>So it didn’t matter what he done.<br>But on this man there was a doom,<br>Because his name was Wotherspoon.</p>
</blockquote>



<p>&#8220;Tom&#8221; Molster carried his bat to the wicket, but had no chance to use it.</p>



<p>George Fry did the trundling for the home team, bagging 6 wickets for 22 runs. Selway and Walter Moore also bowled well. Little &#8220;Donny&#8221; was the best man in the field, ably assisted by Brown and Harding, while &#8220;Snookie&#8221; performed well behind the sticks.</p>



<p>Owing to the bogey condition of the Cossack cricket ground, caused by the high tide at the port, the Civil Service–Cossack match has been postponed till today week.</p>



<p>Some of our local cricketers, I learn, are striving to arrange an all-day match (one end of the town against the other), for Wednesday next. It is to be hoped that &#8220;both ends will meet.&#8221;</p>



<p>&#8220;Short-slip&#8221; was met by an indignant cricketer the other day, and was thus warned:</p>



<blockquote class="wp-block-quote is-layout-flow wp-block-quote-is-layout-flow">
<p>&#8220;Look here you blonky (hic) quill-driver, if you (hic) say a word about me in the (hic) paper, I’ll punch your blinky nose (hic)&#8230; take it from me.&#8221;</p>
</blockquote>



<p>I don’t think he was—<strong>d-r-u-n-k.</strong></p>



<hr class="wp-block-separator has-alpha-channel-opacity" />



<h4 class="wp-block-heading">Source</h4>


<ol class="wp-block-footnotes"><li id="f8180ae8-6aac-43ab-ad4a-845b79f02d44">CATCHES. (1897, June 19). <em>Northern Public Opinion and Mining and Pastoral News (Roebourne, WA : 1894 &#8211; 1902)</em>, p. 3. Retrieved June 11, 2025, from <a href="http://nla.gov.au/nla.news-article255736548">http://nla.gov.au/nla.news-article255736548</a> <a href="#f8180ae8-6aac-43ab-ad4a-845b79f02d44-link">↩︎</a></li><li id="e10b719b-93b1-4706-9187-a2ddf7417631">State Library of Western Australia, 2025.  <em>Cossack and Roebourne Cricket Teams</em>. Photograph taken 1900 in the North West Australian series.  Retrieved June 11, 2025 from <a href="https://encore.slwa.wa.gov.au/iii/encore/search/C__SNorth%20West%20Australia%20SMCLN__Orightresult?lang=eng&amp;suite=def"><br>North West Australia ; BA338/1/36</a> <a href="#e10b719b-93b1-4706-9187-a2ddf7417631-link">↩︎</a></li></ol>


<p></p>
<div id="jp-post-flair" class="sharedaddy sd-like-enabled sd-sharing-enabled"><div class="sharedaddy sd-sharing-enabled"><div class="robots-nocontent sd-block sd-social sd-social-icon sd-sharing"><h3 class="sd-title">Share this:</h3><div class="sd-content"><ul><li class="share-facebook"><a rel="nofollow noopener noreferrer"
				data-shared="sharing-facebook-3719"
				class="share-facebook sd-button share-icon no-text"
				href="https://ghostswa.au/2025/06/12/otd18970612/?share=facebook"
				target="_blank"
				aria-labelledby="sharing-facebook-3719"
				>
				<span id="sharing-facebook-3719" hidden>Click to share on Facebook (Opens in new window)</span>
				<span>Facebook</span>
			</a></li><li class="share-pinterest"><a rel="nofollow noopener noreferrer"
				data-shared="sharing-pinterest-3719"
				class="share-pinterest sd-button share-icon no-text"
				href="https://ghostswa.au/2025/06/12/otd18970612/?share=pinterest"
				target="_blank"
				aria-labelledby="sharing-pinterest-3719"
				>
				<span id="sharing-pinterest-3719" hidden>Click to share on Pinterest (Opens in new window)</span>
				<span>Pinterest</span>
			</a></li><li class="share-linkedin"><a rel="nofollow noopener noreferrer"
				data-shared="sharing-linkedin-3719"
				class="share-linkedin sd-button share-icon no-text"
				href="https://ghostswa.au/2025/06/12/otd18970612/?share=linkedin"
				target="_blank"
				aria-labelledby="sharing-linkedin-3719"
				>
				<span id="sharing-linkedin-3719" hidden>Click to share on LinkedIn (Opens in new window)</span>
				<span>LinkedIn</span>
			</a></li><li class="share-jetpack-whatsapp"><a rel="nofollow noopener noreferrer"
				data-shared="sharing-whatsapp-3719"
				class="share-jetpack-whatsapp sd-button share-icon no-text"
				href="https://ghostswa.au/2025/06/12/otd18970612/?share=jetpack-whatsapp"
				target="_blank"
				aria-labelledby="sharing-whatsapp-3719"
				>
				<span id="sharing-whatsapp-3719" hidden>Click to share on WhatsApp (Opens in new window)</span>
				<span>WhatsApp</span>
			</a></li><li class="share-end"></li></ul></div></div></div><div class='sharedaddy sd-block sd-like jetpack-likes-widget-wrapper jetpack-likes-widget-unloaded' id='like-post-wrapper-*********-3719-6854caa89598d' data-src='//widgets.wp.com/likes/index.html?ver=20250620#blog_id=*********&amp;post_id=3719&amp;origin=ghostswa.wordpress.com&amp;obj_id=*********-3719-6854caa89598d&amp;domain=ghostswa.au' data-name='like-post-frame-*********-3719-6854caa89598d' data-title='Like or Reblog'><div class='likes-widget-placeholder post-likes-widget-placeholder' style='height: 55px;'><span class='button'><span>Like</span></span> <span class='loading'>Loading...</span></div><span class='sd-text-color'></span><a class='sd-link-color'></a></div>
<div id='jp-relatedposts' class='jp-relatedposts' >
	<h3 class="jp-relatedposts-headline"><em>Related</em></h3>
</div></div>	</div><!-- .entry-content -->

	<div class="entry-footer">
		<ul class="post-tags light-text"><li>Tagged</li><li><a href="https://ghostswa.au/tag/cossack/" rel="tag">Cossack</a></li><li><a href="https://ghostswa.au/tag/cricket/" rel="tag">Cricket</a></li><li><a href="https://ghostswa.au/tag/ghost-towns/" rel="tag">Ghost Towns</a></li><li><a href="https://ghostswa.au/tag/history/" rel="tag">history</a></li><li><a href="https://ghostswa.au/tag/on-this-day/" rel="tag">On This Day</a></li><li><a href="https://ghostswa.au/tag/roebourne/" rel="tag">Roebourne</a></li><li><a href="https://ghostswa.au/tag/western-australia-2/" rel="tag">Western Australia</a></li></ul><!-- .post-tags -->	</div><!-- .entry-footer -->

	<div class="entry-author-wrapper">
				<div class="site-posted-on">
			<strong>Published</strong>
			<time class="entry-date published" datetime="2025-06-12T07:40:00+08:00">12 June, 2025</time><time class="updated" datetime="2025-06-11T20:02:32+08:00">11 June, 2025</time>		</div><!-- .site-posted-on -->
	</div>
</article><!-- #post-## -->

			
	<nav class="navigation post-navigation" aria-label="Posts">
		<h2 class="screen-reader-text">Post navigation</h2>
		<div class="nav-links"><div class="nav-previous"><a href="https://ghostswa.au/2025/06/07/20250531-stats/" rel="prev"><span class="meta-nav screen-reader-text">Previous Post</span> Captured Records at 31 May&nbsp;2025</a></div></div>
	</nav>
			
<div id="comments" class="comments-area">

	
	
	
		<div id="respond" class="comment-respond">
		<h3 id="reply-title" class="comment-reply-title">Leave a comment <small><a rel="nofollow" id="cancel-comment-reply-link" href="/2025/06/12/otd18970612/#respond" style="display:none;">Cancel reply</a></small></h3><form action="https://ghostswa.au/wp-comments-post.php" method="post" id="commentform" class="comment-form" novalidate>



<div class="comment-form__verbum transparent"></div><div class="verbum-form-meta"><input type='hidden' name='comment_post_ID' value='3719' id='comment_post_ID' />
<input type='hidden' name='comment_parent' id='comment_parent' value='0' />

			<input type="hidden" name="highlander_comment_nonce" id="highlander_comment_nonce" value="98de79e22a" />
			<input type="hidden" name="verbum_show_subscription_modal" value="" /></div><p style="display: none;"><input type="hidden" id="akismet_comment_nonce" name="akismet_comment_nonce" value="e129344b7e" /></p><p style="display: none !important;" class="akismet-fields-container" data-prefix="ak_"><label>&#916;<textarea name="ak_hp_textarea" cols="45" rows="8" maxlength="100"></textarea></label><input type="hidden" id="ak_js_1" name="ak_js" value="181"/><script>document.getElementById( "ak_js_1" ).setAttribute( "value", ( new Date() ).getTime() );</script></p></form>	</div><!-- #respond -->
	
</div><!-- #comments -->

		
		</main><!-- #main -->
	</div><!-- #primary -->

<div id="secondary" class="widget-area" role="complementary">
	<aside id="block-3" class="widget widget_block widget_text">
<p class="has-text-align-center"></p>
</aside><aside id="block-5" class="widget widget_block widget_media_image"><div class="wp-block-image is-style-default">
<figure class="aligncenter size-large is-resized"><a href="https://www.mycause.com.au/page/372166/the-people-of-western-australias-ghost-towns-project"><img loading="lazy" width="374" height="552" src="https://ghostswa.wordpress.com/wp-content/uploads/2025/05/screenshot-2025-05-31-095602.png?w=374" alt="" class="wp-image-3669" style="width:250px" /></a></figure></div></aside><aside id="block-6" class="widget widget_block widget_text">
<p class="has-text-align-center"></p>
</aside></div><!-- #secondary -->

		</div><!-- #content -->

		<footer id="colophon" class="site-footer" role="contentinfo">
							<div class="footer-widgets clear">
					<div class="widget-areas">
													<div class="widget-area">
								<aside id="block-2" class="widget widget_block">
<h3 class="wp-block-heading has-text-align-center"><strong>Please support</strong> our Project<br>Donate <a href="https://www.fhwa.org.au/index.cfm?module=catalogue&amp;pagemode=indiv&amp;catalogue_item_id=80524&amp;category=6986" target="_blank" rel="noreferrer noopener">Now</a></h3>
</aside>							</div><!-- .widget-area -->
						
						
											</div><!-- .widget-areas -->
				</div><!-- .footer-widgets -->
						<div class="site-info">
				<a href="https://wordpress.com/?ref=footer_blog" rel="nofollow">Blog at WordPress.com.</a>
				
							</div><!-- .site-info -->
		</footer><!-- #colophon -->
	</div><!-- #content-wrapper -->
</div><!-- #page -->

<!--  -->
<script type="speculationrules">
{"prefetch":[{"source":"document","where":{"and":[{"href_matches":"\/*"},{"not":{"href_matches":["\/wp-*.php","\/wp-admin\/*","\/files\/*","\/wp-content\/*","\/wp-content\/plugins\/*","\/wp-content\/themes\/pub\/independent-publisher-2\/*","\/*\\?(.+)"]}},{"not":{"selector_matches":"a[rel~=\"nofollow\"]"}},{"not":{"selector_matches":".no-prefetch, .no-prefetch a"}}]},"eagerness":"conservative"}]}
</script>
<script type="importmap" id="wp-importmap">
{"imports":{"@wordpress\/interactivity":"https:\/\/s0.wp.com\/wp-content\/plugins\/gutenberg-core\/v20.6.0\/build-module\/interactivity\/index.min.js?ver=d9948f13df6155717023"}}
</script>
<script type="module" src="https://s0.wp.com/wp-content/mu-plugins/jetpack-plugin/moon/jetpack_vendor/automattic/jetpack-forms/src/contact-form/../../dist/modules/form/view.js?ver=14.8-a.6" id="jp-forms-view-js-module"></script>
<link rel="modulepreload" href="https://s0.wp.com/wp-content/plugins/gutenberg-core/v20.6.0/build-module/interactivity/index.min.js?ver=d9948f13df6155717023" id="@wordpress/interactivity-js-modulepreload"><script type="application/json" id="wp-script-module-data-@wordpress/interactivity">
{"config":{"jetpack/form":{"error_types":{"is_required":"This field is required.","invalid_form_empty":"The form you are trying to submit is empty.","invalid_form":"Please fill out the form correctly."}}}}
</script>
<script type="text/javascript" src="//0.gravatar.com/js/hovercards/hovercards.min.js?ver=20252517d2298fc173af206bd9f65440008ab9e1ee2bd896241083289a9e27bd0efc7c" id="grofiles-cards-js"></script>
<script type="text/javascript" id="wpgroho-js-extra">
/* <![CDATA[ */
var WPGroHo = {"my_hash":""};
/* ]]> */
</script>
<script crossorigin='anonymous' type='text/javascript'  src='https://s0.wp.com/wp-content/mu-plugins/gravatar-hovercards/wpgroho.js?m=1610363240i'></script>

	<script>
		// Initialize and attach hovercards to all gravatars
		( function() {
			function init() {
				if ( typeof Gravatar === 'undefined' ) {
					return;
				}

				if ( typeof Gravatar.init !== 'function' ) {
					return;
				}

				Gravatar.profile_cb = function ( hash, id ) {
					WPGroHo.syncProfileData( hash, id );
				};

				Gravatar.my_hash = WPGroHo.my_hash;
				Gravatar.init(
					'body',
					'#wp-admin-bar-my-account',
					{
						i18n: {
							'Edit your profile →': 'Edit your profile →',
							'View profile →': 'View profile →',
							'Contact': 'Contact',
							'Send money': 'Send money',
							'Sorry, we are unable to load this Gravatar profile.': 'Sorry, we are unable to load this Gravatar profile.',
							'Gravatar not found.': 'Gravatar not found.',
							'Too Many Requests.': 'Too Many Requests.',
							'Internal Server Error.': 'Internal Server Error.',
							'Is this you?': 'Is this you?',
							'Claim your free profile.': 'Claim your free profile.',
							'Email': 'Email',
							'Home Phone': 'Home Phone',
							'Work Phone': 'Work Phone',
							'Cell Phone': 'Cell Phone',
							'Contact Form': 'Contact Form',
							'Calendar': 'Calendar',
						},
					}
				);
			}

			if ( document.readyState !== 'loading' ) {
				init();
			} else {
				document.addEventListener( 'DOMContentLoaded', init );
			}
		} )();
	</script>

		<div style="display:none">
	</div>
		<div id="actionbar" dir="ltr" style="display: none;"
			class="actnbr-pub-independent-publisher-2 actnbr-has-follow actnbr-has-actions">
		<ul>
								<li class="actnbr-btn actnbr-hidden">
						<a class="actnbr-action actnbr-actn-comment" href="https://ghostswa.au/2025/06/12/otd18970612/#respond">
							<svg class="gridicon gridicons-comment" height="20" width="20" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g><path d="M12 16l-5 5v-5H5c-1.1 0-2-.9-2-2V5c0-1.1.9-2 2-2h14c1.1 0 2 .9 2 2v9c0 1.1-.9 2-2 2h-7z"/></g></svg>							<span>Comment						</span>
						</a>
					</li>
									<li class="actnbr-btn actnbr-hidden">
								<a class="actnbr-action actnbr-actn-follow " href="">
			<svg class="gridicon" height="20" width="20" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path clip-rule="evenodd" d="m4 4.5h12v6.5h1.5v-6.5-1.5h-1.5-12-1.5v1.5 10.5c0 1.1046.89543 2 2 2h7v-1.5h-7c-.27614 0-.5-.2239-.5-.5zm10.5 2h-9v1.5h9zm-5 3h-4v1.5h4zm3.5 1.5h-1v1h1zm-1-1.5h-1.5v1.5 1 1.5h1.5 1 1.5v-1.5-1-1.5h-1.5zm-2.5 2.5h-4v1.5h4zm6.5 1.25h1.5v2.25h2.25v1.5h-2.25v2.25h-1.5v-2.25h-2.25v-1.5h2.25z"  fill-rule="evenodd"></path></svg>
			<span>Subscribe</span>
		</a>
		<a class="actnbr-action actnbr-actn-following  no-display" href="">
			<svg class="gridicon" height="20" width="20" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path fill-rule="evenodd" clip-rule="evenodd" d="M16 4.5H4V15C4 15.2761 4.22386 15.5 4.5 15.5H11.5V17H4.5C3.39543 17 2.5 16.1046 2.5 15V4.5V3H4H16H17.5V4.5V12.5H16V4.5ZM5.5 6.5H14.5V8H5.5V6.5ZM5.5 9.5H9.5V11H5.5V9.5ZM12 11H13V12H12V11ZM10.5 9.5H12H13H14.5V11V12V13.5H13H12H10.5V12V11V9.5ZM5.5 12H9.5V13.5H5.5V12Z" fill="#008A20"></path><path class="following-icon-tick" d="M13.5 16L15.5 18L19 14.5" stroke="#008A20" stroke-width="1.5"></path></svg>
			<span>Subscribed</span>
		</a>
							<div class="actnbr-popover tip tip-top-left actnbr-notice" id="follow-bubble">
							<div class="tip-arrow"></div>
							<div class="tip-inner actnbr-follow-bubble">
															<ul>
											<li class="actnbr-sitename">
			<a href="https://ghostswa.au">
				<img loading='lazy' alt='' src='https://s0.wp.com/i/logo/wpcom-gray-white.png' srcset='https://s0.wp.com/i/logo/wpcom-gray-white.png 1x' class='avatar avatar-50' height='50' width='50' />				People of Western Australia&#039;s Ghost Towns			</a>
		</li>
										<div class="actnbr-message no-display"></div>
									<form method="post" action="https://subscribe.wordpress.com" accept-charset="utf-8" style="display: none;">
																				<div>
										<input type="email" name="email" placeholder="Enter your email address" class="actnbr-email-field" aria-label="Enter your email address" />
										</div>
										<input type="hidden" name="action" value="subscribe" />
										<input type="hidden" name="blog_id" value="*********" />
										<input type="hidden" name="source" value="https://ghostswa.au/2025/06/12/otd18970612/" />
										<input type="hidden" name="sub-type" value="actionbar-follow" />
										<input type="hidden" id="_wpnonce" name="_wpnonce" value="120aa3abaa" />										<div class="actnbr-button-wrap">
											<button type="submit" value="Sign me up">
												Sign me up											</button>
										</div>
									</form>
									<li class="actnbr-login-nudge">
										<div>
											Already have a WordPress.com account? <a href="https://wordpress.com/log-in?redirect_to=https%3A%2F%2Fr-login.wordpress.com%2Fremote-login.php%3Faction%3Dlink%26back%3Dhttps%253A%252F%252Fghostswa.au%252F2025%252F06%252F12%252Fotd18970612%252F">Log in now.</a>										</div>
									</li>
								</ul>
															</div>
						</div>
					</li>
									<li class="actnbr-btn actnbr-hidden no-display" onclick="javascript:__tcfapi( 'showUi' );">
						<a class="actnbr-action actnbr-actn-privacy" href="#">
							<svg class="gridicon gridicons-info-outline" height="20" width="20" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g><path d="M13 9h-2V7h2v2zm0 2h-2v6h2v-6zm-1-7c-4.41 0-8 3.59-8 8s3.59 8 8 8 8-3.59 8-8-3.59-8-8-8m0-2c5.523 0 10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12 6.477 2 12 2z"/></g></svg>							<span>Privacy						</span>
						</a>
					</li>
							<li class="actnbr-ellipsis actnbr-hidden">
				<svg class="gridicon gridicons-ellipsis" height="24" width="24" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g><path d="M7 12c0 1.104-.896 2-2 2s-2-.896-2-2 .896-2 2-2 2 .896 2 2zm12-2c-1.104 0-2 .896-2 2s.896 2 2 2 2-.896 2-2-.896-2-2-2zm-7 0c-1.104 0-2 .896-2 2s.896 2 2 2 2-.896 2-2-.896-2-2-2z"/></g></svg>				<div class="actnbr-popover tip tip-top-left actnbr-more">
					<div class="tip-arrow"></div>
					<div class="tip-inner">
						<ul>
								<li class="actnbr-sitename">
			<a href="https://ghostswa.au">
				<img loading='lazy' alt='' src='https://s0.wp.com/i/logo/wpcom-gray-white.png' srcset='https://s0.wp.com/i/logo/wpcom-gray-white.png 1x' class='avatar avatar-50' height='50' width='50' />				People of Western Australia&#039;s Ghost Towns			</a>
		</li>
								<li class="actnbr-folded-follow">
										<a class="actnbr-action actnbr-actn-follow " href="">
			<svg class="gridicon" height="20" width="20" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path clip-rule="evenodd" d="m4 4.5h12v6.5h1.5v-6.5-1.5h-1.5-12-1.5v1.5 10.5c0 1.1046.89543 2 2 2h7v-1.5h-7c-.27614 0-.5-.2239-.5-.5zm10.5 2h-9v1.5h9zm-5 3h-4v1.5h4zm3.5 1.5h-1v1h1zm-1-1.5h-1.5v1.5 1 1.5h1.5 1 1.5v-1.5-1-1.5h-1.5zm-2.5 2.5h-4v1.5h4zm6.5 1.25h1.5v2.25h2.25v1.5h-2.25v2.25h-1.5v-2.25h-2.25v-1.5h2.25z"  fill-rule="evenodd"></path></svg>
			<span>Subscribe</span>
		</a>
		<a class="actnbr-action actnbr-actn-following  no-display" href="">
			<svg class="gridicon" height="20" width="20" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path fill-rule="evenodd" clip-rule="evenodd" d="M16 4.5H4V15C4 15.2761 4.22386 15.5 4.5 15.5H11.5V17H4.5C3.39543 17 2.5 16.1046 2.5 15V4.5V3H4H16H17.5V4.5V12.5H16V4.5ZM5.5 6.5H14.5V8H5.5V6.5ZM5.5 9.5H9.5V11H5.5V9.5ZM12 11H13V12H12V11ZM10.5 9.5H12H13H14.5V11V12V13.5H13H12H10.5V12V11V9.5ZM5.5 12H9.5V13.5H5.5V12Z" fill="#008A20"></path><path class="following-icon-tick" d="M13.5 16L15.5 18L19 14.5" stroke="#008A20" stroke-width="1.5"></path></svg>
			<span>Subscribed</span>
		</a>
								</li>
														<li class="actnbr-signup"><a href="https://wordpress.com/start/">Sign up</a></li>
							<li class="actnbr-login"><a href="https://wordpress.com/log-in?redirect_to=https%3A%2F%2Fr-login.wordpress.com%2Fremote-login.php%3Faction%3Dlink%26back%3Dhttps%253A%252F%252Fghostswa.au%252F2025%252F06%252F12%252Fotd18970612%252F">Log in</a></li>
																<li class="actnbr-shortlink">
										<a href="https://wp.me/pf5S7V-XZ">
											<span class="actnbr-shortlink__text">Copy shortlink</span>
											<span class="actnbr-shortlink__icon"><svg class="gridicon gridicons-checkmark" height="16" width="16" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g><path d="M9 19.414l-6.707-6.707 1.414-1.414L9 16.586 20.293 5.293l1.414 1.414"/></g></svg></span>
										</a>
									</li>
																<li class="flb-report">
									<a href="https://wordpress.com/abuse/?report_url=https://ghostswa.au/2025/06/12/otd18970612/" target="_blank" rel="noopener noreferrer">
										Report this content									</a>
								</li>
															<li class="actnbr-reader">
									<a href="https://wordpress.com/reader/blogs/*********/posts/3719">
										View post in Reader									</a>
								</li>
															<li class="actnbr-subs">
									<a href="https://subscribe.wordpress.com/">Manage subscriptions</a>
								</li>
																<li class="actnbr-fold"><a href="">Collapse this bar</a></li>
														</ul>
					</div>
				</div>
			</li>
		</ul>
	</div>
	
<script>
window.addEventListener( "load", function( event ) {
	var link = document.createElement( "link" );
	link.href = "https://s0.wp.com/wp-content/mu-plugins/actionbar/actionbar.css?v=20250116";
	link.type = "text/css";
	link.rel = "stylesheet";
	document.head.appendChild( link );

	var script = document.createElement( "script" );
	script.src = "https://s0.wp.com/wp-content/mu-plugins/actionbar/actionbar.js?v=20250204";
	script.defer = true;
	document.body.appendChild( script );
} );
</script>

			<div id="jp-carousel-loading-overlay">
			<div id="jp-carousel-loading-wrapper">
				<span id="jp-carousel-library-loading">&nbsp;</span>
			</div>
		</div>
		<div class="jp-carousel-overlay" style="display: none;">

		<div class="jp-carousel-container">
			<!-- The Carousel Swiper -->
			<div
				class="jp-carousel-wrap swiper-container jp-carousel-swiper-container jp-carousel-transitions"
				itemscope
				itemtype="https://schema.org/ImageGallery">
				<div class="jp-carousel swiper-wrapper"></div>
				<div class="jp-swiper-button-prev swiper-button-prev">
					<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
						<mask id="maskPrev" mask-type="alpha" maskUnits="userSpaceOnUse" x="8" y="6" width="9" height="12">
							<path d="M16.2072 16.59L11.6496 12L16.2072 7.41L14.8041 6L8.8335 12L14.8041 18L16.2072 16.59Z" fill="white"/>
						</mask>
						<g mask="url(#maskPrev)">
							<rect x="0.579102" width="23.8823" height="24" fill="#FFFFFF"/>
						</g>
					</svg>
				</div>
				<div class="jp-swiper-button-next swiper-button-next">
					<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
						<mask id="maskNext" mask-type="alpha" maskUnits="userSpaceOnUse" x="8" y="6" width="8" height="12">
							<path d="M8.59814 16.59L13.1557 12L8.59814 7.41L10.0012 6L15.9718 12L10.0012 18L8.59814 16.59Z" fill="white"/>
						</mask>
						<g mask="url(#maskNext)">
							<rect x="0.34375" width="23.8822" height="24" fill="#FFFFFF"/>
						</g>
					</svg>
				</div>
			</div>
			<!-- The main close buton -->
			<div class="jp-carousel-close-hint">
				<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
					<mask id="maskClose" mask-type="alpha" maskUnits="userSpaceOnUse" x="5" y="5" width="15" height="14">
						<path d="M19.3166 6.41L17.9135 5L12.3509 10.59L6.78834 5L5.38525 6.41L10.9478 12L5.38525 17.59L6.78834 19L12.3509 13.41L17.9135 19L19.3166 17.59L13.754 12L19.3166 6.41Z" fill="white"/>
					</mask>
					<g mask="url(#maskClose)">
						<rect x="0.409668" width="23.8823" height="24" fill="#FFFFFF"/>
					</g>
				</svg>
			</div>
			<!-- Image info, comments and meta -->
			<div class="jp-carousel-info">
				<div class="jp-carousel-info-footer">
					<div class="jp-carousel-pagination-container">
						<div class="jp-swiper-pagination swiper-pagination"></div>
						<div class="jp-carousel-pagination"></div>
					</div>
					<div class="jp-carousel-photo-title-container">
						<h2 class="jp-carousel-photo-caption"></h2>
					</div>
					<div class="jp-carousel-photo-icons-container">
						<a href="#" class="jp-carousel-icon-btn jp-carousel-icon-info" aria-label="Toggle photo metadata visibility">
							<span class="jp-carousel-icon">
								<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
									<mask id="maskInfo" mask-type="alpha" maskUnits="userSpaceOnUse" x="2" y="2" width="21" height="20">
										<path fill-rule="evenodd" clip-rule="evenodd" d="M12.7537 2C7.26076 2 2.80273 6.48 2.80273 12C2.80273 17.52 7.26076 22 12.7537 22C18.2466 22 22.7046 17.52 22.7046 12C22.7046 6.48 18.2466 2 12.7537 2ZM11.7586 7V9H13.7488V7H11.7586ZM11.7586 11V17H13.7488V11H11.7586ZM4.79292 12C4.79292 16.41 8.36531 20 12.7537 20C17.142 20 20.7144 16.41 20.7144 12C20.7144 7.59 17.142 4 12.7537 4C8.36531 4 4.79292 7.59 4.79292 12Z" fill="white"/>
									</mask>
									<g mask="url(#maskInfo)">
										<rect x="0.8125" width="23.8823" height="24" fill="#FFFFFF"/>
									</g>
								</svg>
							</span>
						</a>
												<a href="#" class="jp-carousel-icon-btn jp-carousel-icon-comments" aria-label="Toggle photo comments visibility">
							<span class="jp-carousel-icon">
								<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
									<mask id="maskComments" mask-type="alpha" maskUnits="userSpaceOnUse" x="2" y="2" width="21" height="20">
										<path fill-rule="evenodd" clip-rule="evenodd" d="M4.3271 2H20.2486C21.3432 2 22.2388 2.9 22.2388 4V16C22.2388 17.1 21.3432 18 20.2486 18H6.31729L2.33691 22V4C2.33691 2.9 3.2325 2 4.3271 2ZM6.31729 16H20.2486V4H4.3271V18L6.31729 16Z" fill="white"/>
									</mask>
									<g mask="url(#maskComments)">
										<rect x="0.34668" width="23.8823" height="24" fill="#FFFFFF"/>
									</g>
								</svg>

								<span class="jp-carousel-has-comments-indicator" aria-label="This image has comments."></span>
							</span>
						</a>
											</div>
				</div>
				<div class="jp-carousel-info-extra">
					<div class="jp-carousel-info-content-wrapper">
						<div class="jp-carousel-photo-title-container">
							<h2 class="jp-carousel-photo-title"></h2>
						</div>
						<div class="jp-carousel-comments-wrapper">
															<div id="jp-carousel-comments-loading">
									<span>Loading Comments...</span>
								</div>
								<div class="jp-carousel-comments"></div>
								<div id="jp-carousel-comment-form-container">
									<span id="jp-carousel-comment-form-spinner">&nbsp;</span>
									<div id="jp-carousel-comment-post-results"></div>
																														<form id="jp-carousel-comment-form">
												<label for="jp-carousel-comment-form-comment-field" class="screen-reader-text">Write a Comment...</label>
												<textarea
													name="comment"
													class="jp-carousel-comment-form-field jp-carousel-comment-form-textarea"
													id="jp-carousel-comment-form-comment-field"
													placeholder="Write a Comment..."
												></textarea>
												<div id="jp-carousel-comment-form-submit-and-info-wrapper">
													<div id="jp-carousel-comment-form-commenting-as">
																													<fieldset>
																<label for="jp-carousel-comment-form-email-field">Email (Required)</label>
																<input type="text" name="email" class="jp-carousel-comment-form-field jp-carousel-comment-form-text-field" id="jp-carousel-comment-form-email-field" />
															</fieldset>
															<fieldset>
																<label for="jp-carousel-comment-form-author-field">Name (Required)</label>
																<input type="text" name="author" class="jp-carousel-comment-form-field jp-carousel-comment-form-text-field" id="jp-carousel-comment-form-author-field" />
															</fieldset>
															<fieldset>
																<label for="jp-carousel-comment-form-url-field">Website</label>
																<input type="text" name="url" class="jp-carousel-comment-form-field jp-carousel-comment-form-text-field" id="jp-carousel-comment-form-url-field" />
															</fieldset>
																											</div>
													<input
														type="submit"
														name="submit"
														class="jp-carousel-comment-form-button"
														id="jp-carousel-comment-form-button-submit"
														value="Post Comment" />
												</div>
											</form>
																											</div>
													</div>
						<div class="jp-carousel-image-meta">
							<div class="jp-carousel-title-and-caption">
								<div class="jp-carousel-photo-info">
									<h3 class="jp-carousel-caption" itemprop="caption description"></h3>
								</div>

								<div class="jp-carousel-photo-description"></div>
							</div>
							<ul class="jp-carousel-image-exif" style="display: none;"></ul>
							<a class="jp-carousel-image-download" href="#" target="_blank" style="display: none;">
								<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
									<mask id="mask0" mask-type="alpha" maskUnits="userSpaceOnUse" x="3" y="3" width="19" height="18">
										<path fill-rule="evenodd" clip-rule="evenodd" d="M5.84615 5V19H19.7775V12H21.7677V19C21.7677 20.1 20.8721 21 19.7775 21H5.84615C4.74159 21 3.85596 20.1 3.85596 19V5C3.85596 3.9 4.74159 3 5.84615 3H12.8118V5H5.84615ZM14.802 5V3H21.7677V10H19.7775V6.41L9.99569 16.24L8.59261 14.83L18.3744 5H14.802Z" fill="white"/>
									</mask>
									<g mask="url(#mask0)">
										<rect x="0.870605" width="23.8823" height="24" fill="#FFFFFF"/>
									</g>
								</svg>
								<span class="jp-carousel-download-text"></span>
							</a>
							<div class="jp-carousel-image-map" style="display: none;"></div>
						</div>
					</div>
				</div>
			</div>
		</div>

		</div>
		
	<script type="text/javascript">
		window.WPCOM_sharing_counts = {"https:\/\/ghostswa.au\/2025\/06\/12\/otd18970612\/":3719};
	</script>
						<link crossorigin='anonymous' rel='stylesheet' id='all-css-0-2' href='https://s0.wp.com/_static/??-eJyljMEKwjAQRH/IuBZrxYP4LUm6htRkN2Sz9PdrwYp47WVgHvMG5mI8U0Nq4BIHU5KGSAIhPlukYJwlwgpe/tHxjQ7wo2f9yhO2Yv3r0yEzrzFqQgFvK6tgApljwWqc0phw99s22sB6+Mj37tpfbt3Qn0/TAkmfWCE=&cssminify=yes' type='text/css' media='all' />
<script type="text/javascript" crossorigin='anonymous' src="https://s0.wp.com/wp-content/js/mobile-useragent-info.js?m=1609849039i&amp;ver=20241018" id="mobile-useragent-info-js" defer="defer" data-wp-strategy="defer"></script>
<script type="text/javascript" id="verbum-settings-js-before">
/* <![CDATA[ */
window.VerbumComments = {"Log in or provide your name and email to leave a reply.":"Log in or provide your name and email to leave a reply.","Log in or provide your name and email to leave a comment.":"Log in or provide your name and email to leave a comment.","Receive web and mobile notifications for posts on this site.":"Receive web and mobile notifications for posts on this site.","Name":"Name","Email (address never made public)":"Email (address never made public)","Website (optional)":"Website (optional)","Leave a reply. (log in optional)":"Leave a reply. (log in optional)","Leave a comment. (log in optional)":"Leave a comment. (log in optional)","Log in to leave a reply.":"Log in to leave a reply.","Log in to leave a comment.":"Log in to leave a comment.","Logged in via %s":"Logged in via %s","Log out":"Log out","Email":"Email","(Address never made public)":"(Address never made public)","Instantly":"Instantly","Daily":"Daily","Reply":"Reply","Comment":"Comment","WordPress":"WordPress","Weekly":"Weekly","Notify me of new posts":"Notify me of new posts","Email me new posts":"Email me new posts","Email me new comments":"Email me new comments","Cancel":"Cancel","Write a comment...":"Write a comment...","Write a reply...":"Write a reply...","Website":"Website","Optional":"Optional","We'll keep you in the loop!":"We'll keep you in the loop!","Loading your comment...":"Loading your comment...","Discover more from":"Discover more from People of Western Australia's Ghost Towns","Subscribe now to keep reading and get access to the full archive.":"Subscribe now to keep reading and get access to the full archive.","Continue reading":"Continue reading","Never miss a beat!":"Never miss a beat!","Interested in getting blog post updates? Simply click the button below to stay in the loop!":"Interested in getting blog post updates? Simply click the button below to stay in the loop!","Enter your email address":"Enter your email address","Subscribe":"Subscribe","Comment sent successfully":"Comment sent successfully","Save my name, email, and website in this browser for the next time I comment.":"Save my name, email, and website in this browser for the next time I comment.","hovercardi18n":{"Edit your profile \u2192":"Edit your profile \u2192","View profile \u2192":"View profile \u2192","Contact":"Contact","Send money":"Send money","Profile not found.":"Profile not found.","Too Many Requests.":"Too Many Requests.","Internal Server Error.":"Internal Server Error.","Sorry, we are unable to load this Gravatar profile.":"Sorry, we are unable to load this Gravatar profile."},"siteId":*********,"postId":3719,"mustLogIn":false,"requireNameEmail":true,"commentRegistration":false,"connectURL":"https:\/\/ghostswa.wordpress.com\/public.api\/connect\/?action=request&domain=ghostswa.au","logoutURL":"https:\/\/ghostswa.wordpress.com\/wp-login.php?action=logout&_wpnonce=e4f61ecbc5","homeURL":"https:\/\/ghostswa.au\/","subscribeToBlog":true,"subscribeToComment":true,"isJetpackCommentsLoggedIn":false,"jetpackUsername":"","jetpackUserId":0,"jetpackSignature":"","jetpackAvatar":"https:\/\/0.gravatar.com\/avatar\/?s=96&amp;d=identicon&amp;r=G","enableBlocks":true,"enableSubscriptionModal":true,"currentLocale":"en","isJetpackComments":false,"allowedBlocks":["core\/paragraph","core\/list","core\/code","core\/list-item","core\/quote","core\/image","core\/embed","core\/quote","core\/code"],"embedNonce":"3fa88cea4e","verbumBundleUrl":"https:\/\/s0.wp.com\/wp-content\/mu-plugins\/jetpack-mu-wpcom-plugin\/moon\/jetpack_vendor\/automattic\/jetpack-mu-wpcom\/src\/features\/verbum-comments\/dist\/index.js","isRTL":false,"vbeCacheBuster":1738686361,"iframeUniqueId":0,"colorScheme":false}
/* ]]> */
</script>
<script crossorigin='anonymous' type='text/javascript'  src='https://s0.wp.com/wp-content/mu-plugins/likes/queuehandler.js?m=1741961244i'></script>
<script type="text/javascript" crossorigin='anonymous' src="https://s0.wp.com/wp-content/mu-plugins/jetpack-mu-wpcom-plugin/moon/jetpack_vendor/automattic/jetpack-mu-wpcom/src/build/verbum-comments/assets/dynamic-loader.js?m=1741801507i&amp;minify=false&amp;ver=74baca08b8baf473b8a0" id="verbum-dynamic-loader-js" defer="defer" data-wp-strategy="defer"></script>
<script type="text/javascript" id="comment-like-js-extra">
/* <![CDATA[ */
var comment_like_text = {"loading":"Loading...","swipeUrl":"https:\/\/s0.wp.com\/wp-content\/mu-plugins\/comment-likes\/js\/lib\/swipe.js?ver=20131008"};
/* ]]> */
</script>
<script type="text/javascript" id="gifting-banner-js-extra">
/* <![CDATA[ */
var gifting_banner = {"dismiss_days_count":"365","checkout_link":"https:\/\/wordpress.com\/checkout\/personal-bundle\/gift\/21869228?cancel_to=\/home","more_info_link":"https:\/\/wordpress.com\/support\/gift-a-wordpress-com-subscription\/","i18n":{"title":"Enjoy this site?","subtitle":"Gift the author a WordPress.com plan.","button_text":"Gift"}};
/* ]]> */
</script>
<script type="text/javascript" id="jetpack-carousel-js-extra">
/* <![CDATA[ */
var jetpackSwiperLibraryPath = {"url":"https:\/\/s0.wp.com\/wp-content\/mu-plugins\/jetpack-plugin\/moon\/_inc\/build\/carousel\/swiper-bundle.min.js"};
var jetpackCarouselStrings = {"widths":[370,700,1000,1200,1400,2000],"is_logged_in":"","lang":"en","ajaxurl":"https:\/\/ghostswa.au\/wp-admin\/admin-ajax.php","nonce":"e451c53cc9","display_exif":"1","display_comments":"1","single_image_gallery":"1","single_image_gallery_media_file":"","background_color":"black","comment":"Comment","post_comment":"Post Comment","write_comment":"Write a Comment...","loading_comments":"Loading Comments...","image_label":"Open image in full-screen.","download_original":"View full size <span class=\"photo-size\">{0}<span class=\"photo-size-times\">\u00d7<\/span>{1}<\/span>","no_comment_text":"Please be sure to submit some text with your comment.","no_comment_email":"Please provide an email address to comment.","no_comment_author":"Please provide your name to comment.","comment_post_error":"Sorry, but there was an error posting your comment. Please try again later.","comment_approved":"Your comment was approved.","comment_unapproved":"Your comment is in moderation.","camera":"Camera","aperture":"Aperture","shutter_speed":"Shutter Speed","focal_length":"Focal Length","copyright":"Copyright","comment_registration":"0","require_name_email":"1","login_url":"https:\/\/ghostswa.wordpress.com\/wp-login.php?redirect_to=https%3A%2F%2Fghostswa.au%2F2025%2F06%2F12%2Fotd18970612%2F","blog_id":"*********","meta_data":["camera","aperture","shutter_speed","focal_length","copyright"],"stats_query_args":"blog=*********&v=wpcom&tz=8&user_id=0&subd=ghostswa","is_public":"1"};
/* ]]> */
</script>
<script type="text/javascript" id="sharing-js-js-extra">
/* <![CDATA[ */
var sharing_js_options = {"lang":"en","counts":"1","is_stats_active":"1"};
/* ]]> */
</script>
<script crossorigin='anonymous' type='text/javascript'  src='https://s0.wp.com/_static/??-eJydUEluwzAM/FBldsmCHoo+pZAlWqYtUYKWpvl95SQOHKPowRdBHMzCIZyCUJ4zcgZXRLDFECdQ3rkKCUsjJhhWQDOkJ1gIc4+u0kJpgVhjwPpUbp0tpR6jeJ0sWH6TkZk8b9OnkULN51F0XpUkOvqZjYiVLfpx04jBnhtHW9KqHyQVKWRxCtVwbdFab+63MtRlYiNayYxxWuERWYsXZx4wB6nG2wzOe4avKbwtZDUoGX1JaO+8Gfir1sJWjpQcZvHWPF/tZqCLF7L+RztHXIuvxu1VUi8jaqn1+fKt17l1+HQfL8fdYb87vh/2wy/v7/T1'></script>
<script type="text/javascript" id="sharing-js-js-after">
/* <![CDATA[ */
var windowOpen;
			( function () {
				function matches( el, sel ) {
					return !! (
						el.matches && el.matches( sel ) ||
						el.msMatchesSelector && el.msMatchesSelector( sel )
					);
				}

				document.body.addEventListener( 'click', function ( event ) {
					if ( ! event.target ) {
						return;
					}

					var el;
					if ( matches( event.target, 'a.share-facebook' ) ) {
						el = event.target;
					} else if ( event.target.parentNode && matches( event.target.parentNode, 'a.share-facebook' ) ) {
						el = event.target.parentNode;
					}

					if ( el ) {
						event.preventDefault();

						// If there's another sharing window open, close it.
						if ( typeof windowOpen !== 'undefined' ) {
							windowOpen.close();
						}
						windowOpen = window.open( el.getAttribute( 'href' ), 'wpcomfacebook', 'menubar=1,resizable=1,width=600,height=400' );
						return false;
					}
				} );
			} )();
var windowOpen;
			( function () {
				function matches( el, sel ) {
					return !! (
						el.matches && el.matches( sel ) ||
						el.msMatchesSelector && el.msMatchesSelector( sel )
					);
				}

				document.body.addEventListener( 'click', function ( event ) {
					if ( ! event.target ) {
						return;
					}

					var el;
					if ( matches( event.target, 'a.share-linkedin' ) ) {
						el = event.target;
					} else if ( event.target.parentNode && matches( event.target.parentNode, 'a.share-linkedin' ) ) {
						el = event.target.parentNode;
					}

					if ( el ) {
						event.preventDefault();

						// If there's another sharing window open, close it.
						if ( typeof windowOpen !== 'undefined' ) {
							windowOpen.close();
						}
						windowOpen = window.open( el.getAttribute( 'href' ), 'wpcomlinkedin', 'menubar=1,resizable=1,width=580,height=450' );
						return false;
					}
				} );
			} )();
/* ]]> */
</script>

	<script type="text/javascript">
		(function () {
			var wpcom_reblog = {
				source: 'toolbar',

				toggle_reblog_box_flair: function (obj_id, post_id) {

					// Go to site selector. This will redirect to their blog if they only have one.
					const postEndpoint = `https://wordpress.com/post`;

					// Ideally we would use the permalink here, but fortunately this will be replaced with the 
					// post permalink in the editor.
					const originalURL = `${ document.location.href }?page_id=${ post_id }`; 
					
					const url =
						postEndpoint +
						'?url=' +
						encodeURIComponent( originalURL ) +
						'&is_post_share=true' +
						'&v=5';

					const redirect = function () {
						if (
							! window.open( url, '_blank' )
						) {
							location.href = url;
						}
					};

					if ( /Firefox/.test( navigator.userAgent ) ) {
						setTimeout( redirect, 0 );
					} else {
						redirect();
					}
				},
			};

			window.wpcom_reblog = wpcom_reblog;
		})();
	</script>
	<iframe src='https://widgets.wp.com/likes/master.html?ver=20250620#ver=20250620' scrolling='no' id='likes-master' name='likes-master' style='display:none;'></iframe>
	<div id='likes-other-gravatars' role="dialog" aria-hidden="true" tabindex="-1"><div class="likes-text"><span>%d</span></div><ul class="wpl-avatars sd-like-gravatars"></ul></div>
	<script src="//stats.wp.com/w.js?68" defer></script> <script type="text/javascript">
_tkq = window._tkq || [];
_stq = window._stq || [];
_tkq.push(['storeContext', {'blog_id':'*********','blog_tz':'8','user_lang':'en','blog_lang':'en','user_id':'0'}]);
_stq.push(['view', {'blog':'*********','v':'wpcom','tz':'8','user_id':'0','post':'3719','subd':'ghostswa'}]);
_stq.push(['extra', {'crypt':'UE5tW3cvZGRTd1VXTHx0Q0pyYT89K3QrZXh+YlZCT3lNLEN2NzQ2Njh8QTJiL1BzZVh+MTlwZTc3TkdTOFRnU0tVUE1VQj1TaW03RjJMN210ckxtUiZtOHFKRGJ+VjJTWEZbSF9zbDMrQ3BBP352NkNkMWw/SGZfK3EsfD1DaWlXX09UdG8rQ3g4Vlk/dV1bQzRBNj1HZmZmeGxyMTdJXVJKZ1RYUWJaXTR2RSZEeTNVejdMV3picm42OGF8dzdwJm5ofjY9V2NqW0VqVVVMSC11SWI='}]);
_stq.push([ 'clickTrackerInit', '*********', '3719' ]);
</script>
<noscript><img src="https://pixel.wp.com/b.gif?v=noscript" style="height:1px;width:1px;overflow:hidden;position:absolute;bottom:1px;" alt="" /></noscript>
<script defer id="bilmur" data-custom-props="{&quot;logged_in&quot;:&quot;0&quot;,&quot;wptheme&quot;:&quot;pub\/independent-publisher-2&quot;,&quot;wptheme_is_block&quot;:&quot;0&quot;}" data-provider="wordpress.com" data-service="simple" data-site-tz="Australia/Perth"  src="/wp-content/js/bilmur.min.js?i=15&m=202525"></script><script>
( function() {
	function getMobileUserAgentInfo() {
		if ( typeof wpcom_mobile_user_agent_info === 'object' ) {
			wpcom_mobile_user_agent_info.init();
			var mobileStatsQueryString = '';

			if ( wpcom_mobile_user_agent_info.matchedPlatformName !== false ) {
				mobileStatsQueryString += '&x_' + 'mobile_platforms' + '=' + wpcom_mobile_user_agent_info.matchedPlatformName;
			}

			if ( wpcom_mobile_user_agent_info.matchedUserAgentName !== false ) {
				mobileStatsQueryString += '&x_' + 'mobile_devices' + '=' + wpcom_mobile_user_agent_info.matchedUserAgentName;
			}

			if ( wpcom_mobile_user_agent_info.isIPad() ) {
				mobileStatsQueryString += '&x_' + 'ipad_views' + '=' + 'views';
			}

			if ( mobileStatsQueryString != '' ) {
				new Image().src = document.location.protocol + '//pixel.wp.com/g.gif?v=wpcom-no-pv' + mobileStatsQueryString + '&baba=' + Math.random();
			}
		}
	}

	document.addEventListener( 'DOMContentLoaded', getMobileUserAgentInfo );
} )();
</script>
<script>
(function() {
	'use strict';

	const fetches = {};
	const promises = {};
	const urls = {
		'wp-polyfill': 'https://s0.wp.com/wp-includes/js/dist/vendor/wp-polyfill.min.js?m=1744646400i&ver=3.15.0',
		'verbum': 'https://s0.wp.com/wp-content/mu-plugins/jetpack-mu-wpcom-plugin/moon/jetpack_vendor/automattic/jetpack-mu-wpcom/src/build/verbum-comments/verbum-comments.js?m=1749490696i&minify=false&ver=d4dc558a5ff8343c9778'
	};
	const loaders = {
		'verbum': () => {
			fetchExternalScript('wp-polyfill');
			fetchExternalScript('verbum');
			promises['wp-polyfill'] = promises['wp-polyfill'] || loadWPScript('wp-polyfill');
			promises['verbum'] = promises['verbum'] || promises['wp-polyfill'].then( () => loadWPScript('verbum') );
			return promises['verbum'];
		},
		
	};
	const scriptExtras = {
		
	};

	window.WP_Enqueue_Dynamic_Script = {
		loadScript: (handle) => {
			if (!loaders[handle]) {
				console.error('WP_Enqueue_Dynamic_Script: unregistered script `' + handle + '`.');
			}
			return loaders[handle]();
		}
	};

	function fetchExternalScript(handle) {
		if (!urls[handle]) {
			return Promise.resolve();
		}

		fetches[handle] = fetches[handle] || fetch(urls[handle], { mode: 'no-cors' });
		return fetches[handle];
	}

	function runExtraScript(handle, type, index) {
		const id = 'wp-enqueue-dynamic-script:' + handle + ':' + type + ':' + (index + 1);
		const template = document.getElementById(id);
		if (!template) {
			return Promise.reject();
		}

		const script = document.createElement( 'script' );
		script.innerHTML = template.innerHTML;
		document.body.appendChild( script );
		return Promise.resolve();
	}

	function loadExternalScript(handle) {
		if (!urls[handle]) {
			return Promise.resolve();
		}

		return fetches[handle].then(() => {
			return new Promise((resolve, reject) => {
				const script = document.createElement('script');
				script.onload = () => resolve();
				script.onerror = (e) => reject(e);
				script.src = urls[handle];
				document.body.appendChild(script);
			});
		});
	}

	function loadExtra(handle, pos) {
		const count = (scriptExtras[handle] && scriptExtras[handle][pos]) || 0;
		let promise = Promise.resolve();

		for (let i = 0; i < count; i++) {
			promise = promise.then(() => runExtraScript(handle, pos, i));
		}

		return promise;
	}

	function loadWPScript(handle) {
		// Core loads scripts in this order. See: https://github.com/WordPress/WordPress/blob/a59eb9d39c4fcba834b70c9e8dfd64feeec10ba6/wp-includes/class-wp-scripts.php#L428.
		return loadExtra(handle, 'translations')
			.then(() => loadExtra(handle, 'before'))
			.then(() => loadExternalScript(handle))
			.then(() => loadExtra(handle, 'after'));
	}
} )();
</script>

</body>
</html>
