# People of Western Australias Ghost Towns

## Website Development

### PWD Quote Comments

There are a range of considerations posed by the proposal.
Give me a call to discuss further.
If you want me to discuss with PWD as well, then happy to do so.

#### Executive Summary

1. Stage 1 also needs editing capability.
2. Duplication of user registration - issue or not?
3. What does Stage 2 actually do?
4. Stage 1 - A Component or a Throwaway?
    Should Stage 1 actually be the initial build of the final solution?
    If Staged 1 is effectively a throwaway, then alternative solutions could be considered which would save the $12,000 cost of the throwaway solution, which is then applied to the final solution.

#### Recommendation

a. Ask PWD to consider whether Stage 1 should be the initial build of the final solution, rather than a throwaway solution.
b. Ask PWD to provide the cost for each stage (based on Stage 1 being the initial build), and provide a little more detail on what is being proposed in each stage.
c. If PWD still considers that Stage 1 should effectively be a throwaway (as per their current proposal), then consider some low-cost no-code alternatives for Stage 1 and confirm the full cost of a PWD final solution build importing data from the Stage 1 alternative.


More detail follows, which elucidates on the points above ... ...

#### PWD Staging
It is usually a good idea to break the project down into stages or phases,
but I am not sure that I understand whether the stages proposed are actually
that useful or workable in practice.

They propose the following stages, which will theoretically deliver the whole project:

- Stage 1 - Data Collection Module (and exporting existing database)
- Stage 2 - Centralise data for better search functionality.
- Stage 3 - Enhance the design and functionalities based on the collected data to create a full custom public facing website

Some comments below in re each stage.

#### Stage 1 - Data Collection Module (and exporting existing database)
PWD proposes using Gravity Forms running within/under Wordpress using WPEngine to host the Wordpress site.

##### Gravity Forms
Gravity Forms allows for entry of data in a form. The form can be saved and then edited prior to submission, but when submitted, the form data is no longer available for editing (this is the standard form behaviour - verify with PWD whether this is incorrect).

##### Users
The Wordpress site containing the Gravity Form will have to be controlled to only allow registered user access - otherwise anyone on the internet could enter bogus data (and cause all sorts of problems).

How many users are you proposing to allow entry of data in the initial stage?
Are these the same users as already registered in main FHWA site?
Will the new data.fhwa.org.au site share the registered user base, or will it require a separate registration process? 

Based on what PWD state ("Volunteers will be able to create accounts"), it is likely to be a separate registration process.

Not much of a problem if only a few users, but potentially problematical if wanting to allow everyone in FHWA to be able to enter records, and if there are many registered users.

The presumption is that the final ghostswa.org.au site will share the registered user base with fhwa.org.au? Is this correct? If this is the case, having too many people having to multiply register will cause some confusion etc, although this may be a relatively minor concern and could be managed.

##### Data Storage
When the form is submitted, where is the data stored?  In a relational database (like PostgreSQL) or as CSV files etc or some other mechanism?
It appears that it will be stored using Wordpress facilities ("Backend (Wordpress) database setup to store and manage submitted data").

##### Attachments
If photos/documents etc are a part of the data entry, where and how are these stored? How many can be attached per each form?

Large attachments like photos/documents etc can not be exported in CSV (unless one used various advanced techniques like Base64 conversion, and then usually the line lengths are far too long to handle in CSV).

How will attachments be handled in the conversion process to the new platform?

##### Data Editing
Once the Gravity Form is submitted, it is unclear that any of the data entered will be able to be edited, e.g. to fix a typo, to add some additional information, to upload another photo or document etc. The presumption is that the data will NOT be able to edited, because this is not what normal Gravity Forms does.
I did find an add-on product called GravityView which is part of GravityKit (https://www.gravitykit.com/). It costs (https://www.gravitykit.com/intro-pricing/) either USD$86 or USD$132 for the first year and then increasing thereafter.

There is also a technical administrative backend inside Wordpress which would allow editing, but I would suggest that that would not be for ordinary users.

##### Stage 1 - A Component or a Throwaway
The big question that I have is whether this initial Stage 1 solution is going to be (a) a component of the final solution, or (b) will the final solution replace the data entry with another product/solution?

If the former (a), then why would there not be an editing capability?

If the latter (b) - which is more likely, given their statement "Export existing database into new platfrom (from CSV)" (sic) - then will the Gravity Forms interface be completely abandoned (most likely)?

As mentioned above, migrating attachments into the new platform/database may pose some additional coding work, all of which is "wasted" from a future perspective.

##### Overall Costings and Proposed Solution Savings
How does the proposed $12,000 cost for Stage 1 fit into the overall project costings?
Does it save money overall, or add to the final total cost?
(Incidentally, what was the proposed cost for the final solution?)

##### Stage 1 should be initial build of final solution
Given all the above, why would PWD not use stage 1 to build the initial aspect of the final solution?

These steps would presumably be:

1. Implement basic platform facility (e.g. if the final solution is all Wordpress based and uses a relational database backend, then create the Wordpress configuration and installation, install and configure the database, provide basic user management functionality). As an adjunct, maybe implement the connection between registered users on FHWA and the new site, to avoid the overlapping of registrations;
2. Design and create the data structures for the final solution, and implement in the database;
3. Design the basic data entry and editing screen(s) for the main data entry (as per what was proposed for Stage 1 by PWD but including editing capability);
4. Build and test the data entry and editing screen(s) for the main data entry, implementing basic CRUD functionality (Create Read Update Delete), allowing for attachments to be processed.

The initial implementation would have limited search capabilities, and possibly just provide a list of records entered, which one could scroll down to find the record to view and edit if required.

This would then neatly lead into Stage 2 and follow-on to Stage 3.

This solution obviates the need for GravityForms and other add-ons.


#### Stage 2 - Centralise data for better search functionality.

##### What does Stage 2 actually do
In the PWD proposal, I did not understand what Stage 2 was supposed to do at all.
What does "Centralise data" mean?
What exactly is being proposed in terms of development etc?

Following on from the outline and queries in re Stage 1, at some point, it appears that PWD are proposing to have to develop a new platform, with all data entry, editing, search and other functionality built into it. The GravityForms solution would then be abandoned after the data migration to the new platform.

Is this what Stage 2 is supposed to be?

If not, then what is Stage 2 actually implementing in what environment?

And if this new development is what Stage 2 is, then why not commence it in Stage 1 (as outlined above) and then extend it in Stage 2 for further search and view functionality?


#### Stage 3 - Enhance the design and functionalities based on the collected data to create a full custom public facing website

I then presume that Stage 3 is taking the new platform that was implemented in Stage 2 (and if it was not implemented in Stage 2, it would have to be implemented in Stage 3 - further exacerbating the issues of wasted development in earlier stages) and adding all the rest of the functionality required, such as different views of the data, advanced search capabilities, etc.

Once again, the issue is whether Stage 1 and Stage 2 are just the early stages of the complete development on the new platform, or whether the work in any of these stages is thrown away?

#### Alternatives for a Stage 1 Data Entry Throwaway

##### Number of Data Entry Registered Users Required
How many people might have to be registered to do data entry in Stage 1?
Or alternatively, do you think it would work to have just 2 or 3 registered users (i.e. FH1, FH2, FH3) and people share the login when doing some data entry (saves on costs)?

##### Data Entry NoCode Products
There are quite a few products which allow you to define really nice data entry forms and store the entries in a database, for later display, editing, etc. They allow uploading of multiple photos/documents etc. They also allow for exporting the data in various formats, and have interfaces to download all the photos/documents as well.

These products are very easy to setup in the first instance and pretty easy to use.

All the products offer a free tier which limits the number of users and the number of records that can be stored. This can be used to test out the design for the data entry app and pick which solution you like the best.

For instance Baserow (https://baserow.io) does all the above and much more, for either USD$12/user/month or USD$22/user/month. See https://baserow.io/pricing.

Other options (which are of a similar pricing nature to baserow.io) include:

   * https://www.clappia.com/
   * https://www.getgrist.com/
   * https://www.airtable.com/
   * https://www.notion.com/
   * https://clickup.com/


##### Data Entry Development
If you want a simple data entry solution and save money for the final build,
then one could use one of the above NoCode products to do the data entry, and then export the data to CSV and import into the final solution (in the same manner that PWD were proposing for their Stage 1 to subsequent stages proposal).

Given the fields which the Ghosts form wants to populate (you would provide that), I could easily generate a solution using a free tier of a good product and that prototype could be iterated to make sure it works really well.

It would not take long to get something going to see what it looks like - I could do that within a day if you provide me with all the fields.

A simple manual could be written and people could get going with the data entry.

I would have to implement the data export facility, which requires a small amount of work to export all the attachments as well as the data itself (the data export is built-in to the product and requires no work - it is just getting all the attachments which requires a little bit of work.  PWD would have to do the same work in their GravityForms solution.)

The data export could be run regularly to provide backups.

When PWD are ready with the basics of their final solution (enough to enter and edit data and read in date from an export), then the latest export could be provided to PWD.

----------------------------

On a side note, some of the costing PWD provided seemed a little strange ...

##### WP Engine hosting
PWD suggest that the data entry needs to be hosted on WPEngine (https://wpengine.com/au/plans/), at a cost of $99 per month. But the actual WPEngine site states that the cost is AUD$83 per month if one pays monthly and only AUD$69 per month if pay annually (i.e. $826/year). Why the pricing discrepancy?

More than that, why is such a high-end hosting solution needed for data entry only. If it goes down and takes some time to recover (which is highly unlikely even for the most basic web hosting solution), it simply means that some data entry is delayed for a little while. As long as backups and data copies are made regularly, no data should be lost.

When the full system is in place, obviously a robust infrastructure solution is necessary (365/24/7 operation, auto-failover, recovery, etc). But it is very unclear from the quote whether a Wordpress site is the end-point infrastructure for the final solution.

The https://ghostswa.au/ website is obviously Wordpress, using WordPress.com (because of the promotional header which still appears).
I am presuming that the main site (https://www.fhwa.org.au/) is not Wordpress (based on the PWD statement: "Although the main FHWA site is not built on WordPress") - presumably it is MemberJungle.



### PWD Quote
20250313_PWD_quote_People_of_Western_Australias_Ghost_Towns_Stage_1_20250313t175404_6Cm8.pdf

Full Project Objectives:

* Develop a comprehensive genealogical index of people who resided in Western Australian ghost towns
* Include historical, demographic, and geographical information on all known ghost towns, abandoned towns/settlements, and similar places in Western Australia
* Make the information accessible through a dedicated website with advanced search / filtering capabilities
* Aim for the project to be self-funding through public and private grants, public donations, and monetisation of the collected information
* Proposed Stages:
    - Stage 1 - Data Collection Module (and exporting existing database)
    - Stage 2 - Centralise data for better search functionality.
    - Stage 3 - Enhance the design and functionalities based on the collected data to create a full custom public facing website

53 Hours Allocated: 195.00 x 53 = 10,335.00


#### Data Collection Module - Stage 1

We will need to create a subdomain on WordPress for this project (e.g., data.fhwa.org.au) to host the data collection module. Although the main FHWA site is not built on WordPress, the subdomain will be configured to run on a separate WordPress instance, ensuring smooth integration with the main site while maintaining flexibility for future expansion. This approach allows the data collection tool to function independently, with its own database and access controls, while still aligning with the overall FHWA infrastructure.

Stage 1 of the project focuses on developing a secure, role-based data collection module for the "People of Western Australian Ghost Towns" initiative. Volunteers will be able to create accounts and submit data via a password-protected subdomain, using Gravity Forms integrated with a backend database. The module will allow for efficient data entry and future scalability, with an emphasis on secure access and ease of use. This phase will be completed with a design, development, testing, and volunteer account setup, ensuring smooth integration into Phase 2.

#### Design: - Estimated at 8 hours

* User-friendly, intuitive interface for easy data submission
* Clean and accessible layout
* High visibility design for an older demographic
* Fully responsive, compatible across devices
* Simple, easy-to-navigate structure
* Supports role-based access for volunteers and admins
* Scalable design to accommodate future integration with the data collection module

#### Development: - Estimated at 39 Hours

* Development of a secure, password-protected subdomain for data collection
* Integration of Gravity Forms for data entry and submission
* Backend (Wordpress) database setup to store and manage submitted data
* Role-based access system to manage volunteer and admin accounts
* Implementation of quality assurance and testing to ensure functionality and security
* Export existing database into new platfrom (from CSV)
* Full QA and testing prior to launch
* Scalable architecture to support future phases and database centralisation

#### Project Management: - Estimated at 6 hours

* Dedicated project manager
* Development progress meetings
* Pre-launch review meeting
* Training for volunteers

#### WP Engine Premium Hosting - Recommended for Security
WP Engine is a premium managed WordPress hosting provider designed to deliver high performance, secure, and scalable solutions for WordPress websites including features such as automated backups, managed security, a content delivery network (CDN), and expert WordPress support, ensuring your website operates at peak performance.

#### Costs

```
99.00 x1 = 99.00 per month (for 6 months)

Subtotal   GST 10%
99.00      9.90
Total AUD including GST: $108.90 per month (for 6 months)
Total over 6 months AUD including GST: $653.40

Subtotal   GST 10%
10,335.00  1,033.50
Total AUD including GST: $11,368.50

Total for hosting and development AUD including GST: $12,021.90

```
