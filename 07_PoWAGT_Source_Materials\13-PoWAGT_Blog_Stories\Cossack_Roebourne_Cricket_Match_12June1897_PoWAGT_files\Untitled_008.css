p.comment-likes {
	height: 16px;
	float: none;
	width: 100%;
	clear: both;
	display: block;
	margin-left: -2px;
}

p.comment-not-liked {
	cursor: pointer;
}

p.comment-likes a.view-likers {
	text-decoration: underline;
	border: none;
}

div.comment-likes-overlay {
	color: rgb(85, 85, 85);
	position: absolute;
	font-size: 9pt;
	padding: 0;
	margin: 0;
	
	z-index: 20000;
}

div.comment-likes-overlay div.inner {
	background-color: white;
	border: 1px solid #dfdfdf;
	border-color: rgba(0, 0, 0, 0.1);
	padding: 8px;
	margin: 0;
	max-width: 310px;
	max-height: 250px;
	overflow: hidden;

	-webkit-box-shadow: 0 0 2px rgba(0, 0, 0, 0.15), 0 3px 8px rgba(0, 0, 0, 0.1);
	-moz-box-shadow: 0 0 2px rgba(0, 0, 0, 0.15), 0 3px 8px rgba(0, 0, 0, 0.1);
	box-shadow: 0 0 2px rgba(0, 0, 0, 0.15), 0 3px 8px rgba(0, 0, 0, 0.1);
}

div.comment-likes-overlay div.inner a img {
	text-decoration: none;
	height: 25px;
	width: 25px;
	margin: 2px 10px 2px 2px;
	vertical-align: middle;
}

div.comment-likes-overlay div.inner ul {
	margin: 0;
	padding: 0;
}

div.comment-likes-overlay div.inner ul li {
	float: left;
	display: inline;
	padding: 0;
	margin: 0 0 5px 0;
	
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

div.comment-likes-overlay div.inner ul.single li {
	width: 100%; 
}

div.comment-likes-overlay div.inner ul.double li {
	width: 50%; 
}

div.comment-likes-overlay div.inner a,
div.comment-likes-overlay div.inner a:hover {
	text-decoration: none;
	border-bottom: none;
	background: transparent;
}

div.comment-likes-overlay div.inner ul li a span.user-name {
	color: rgb(85, 85, 85);
}

p.comment-likes span.comment-like-feedback {
	font-size: 12px;
	line-height: 1.8em;
	opacity: .8;
	float: left;
	padding: 1px 0 0 5px;
	display: block !important;
}

a.comment-like-link:hover {
	background: transparent;
}

a.comment-like-link span {
	display: none;
}

a.comment-like-link {
	outline: none;
	border: 0 !important;
	text-decoration: none !important;
	padding: 0;
	margin: 0;
	float: left;
	text-decoration: none;
	line-height: 70%;
	background: transparent;
}

a.comment-like-link:before,
div.comment-likes-overlay span.icon {
	-webkit-font-smoothing: antialiased;
	font-family: "Noticons";
	font-size: 20px;
	line-height: .9;
}

div.comment-likes-overlay span.icon {
	color: white;
	height: 100%;
	margin: 0;
	padding: 3px 0 1px 0;
}

a.comment-like-link:before {
	color: #2EA2CC;
	content: '\f408';
	width: 16px;
	display: inline-block;
}

a.comment-like-link.loading {
	-webkit-transition: opacity 2s;
	-moz-transition: opacity 2s;
	-ms-transition: opacity 2s;
	-o-transition: opacity 2s;
	transition: opacity 2s;

	opacity: 0;
}

a.comment-like-link:hover:before,
p.comment-liked a.comment-like-link:before,
p.comment-not-liked:hover a.comment-like-link:before {
	color: #f1831e;
	background: transparent;
}



div.comment-likes-overlay div .slider-nav {
	position: relative;
	clear: both;
	width: 310px;
	height: 40px;
	margin: 15px 0 -15px -12px;
	text-align: center;
	border-top: 1px solid #dfdfdf;
}

div.comment-likes-overlay div .slider-nav em {
	font-style: normal;
}

div.comment-likes-overlay div .slider-nav .prev {
	position: absolute;
	top: 0;
	left: 0;
	display: block;
	width: 40px;
	height: 40px;
	border-right: 1px solid #dfdfdf;
}

div.comment-likes-overlay div .slider-nav .noticon {
	width: 40px;
	height: 40px;
	font-size: 25px;
	line-height: 40px;
}

div.comment-likes-overlay div .slider-nav .next {
	position: absolute;
	top: 0;
	right: 0;
	display: block;
	display: block;
	width: 40px;
	height: 40px;
	border-left: 1px solid #dfdfdf;
}

div.comment-likes-overlay div .slider-nav .position {
	position: absolute;
	top: 0;
	left: 40px;
	bottom: 0;
	right: 40px;
	text-align:center;
}

div.comment-likes-overlay div .slider-nav .position div {
	display: inline;
}

div.comment-likes-overlay div .slider-nav .position em {
	display: inline-block;
	padding: 0 3px;
	font-size: 30px;
	line-height: 40px;
	color: #bbb;
	cursor: pointer;
}

div.comment-likes-overlay div .slider-nav .position .on {
	color: #1e8cbe;
}



div.comment-likes-overlay .swipe {
	overflow: hidden;
	visibility: hidden;
	position: relative;
}

div.comment-likes-overlay .swipe-wrap {
	overflow: hidden;
	position: relative;
}


div.comment-likes-overlay .swipe-wrap > div {
	float: left;
	width: 100%;
	position: relative;
}




@font-face {
  font-family: "Noticons";
  src: url(/i/noticons/./Noticons.eot?) format("embedded-opentype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "Noticons";
  src: url("data:application/x-font-woff;charset=utf-8;base64,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") format("woff"),
       url(/i/noticons/./Noticons.ttf) format("truetype"),
       url(/i/noticons/./Noticons.svg#Noticons) format("svg");
  font-weight: normal;
  font-style: normal;
}

@media screen and (-webkit-min-device-pixel-ratio:0) {
  @font-face {
    font-family: "Noticons";
    src: url(/i/noticons/./Noticons.svg#Noticons) format("svg");
  }
}




.noticon {
	font-size: 16px;
	vertical-align: top;
	text-align: center;
	-moz-transition: color .1s ease-in 0;
	-webkit-transition: color .1s ease-in 0;
	display: inline-block;
	font-family: "Noticons";
	font-style: normal;
	font-weight: normal;
	font-variant: normal;
	line-height: 1;
	text-decoration: inherit;
	text-transform: none;
	-moz-osx-font-smoothing: grayscale;
	-webkit-font-smoothing: antialiased;
	speak: none;
}




.noticon-404:before { content: "\f423"; }
.noticon-activity:before { content: "\f508"; }
.noticon-add:before { content: "\f8b3"; }
.noticon-add-media:before { content: "\f8d9"; }
.noticon-akismet:before { content: "\f8d2"; }
.noticon-anchor:before { content: "\f509"; }
.noticon-art:before { content: "\f8b4"; }
.noticon-aside:before { content: "\f101"; }
.noticon-atsign:before { content: "\f814"; }
.noticon-attachment:before { content: "\f416"; }
.noticon-audio:before { content: "\f109"; }
.noticon-automattic:before { content: "\f815"; }
.noticon-automattic-blip:before { content: "\f817"; }
.noticon-automattic-ring:before { content: "\f816"; }
.noticon-bell:before { content: "\f8d4"; }
.noticon-bold:before { content: "\f471"; }
.noticon-book:before { content: "\f444"; }
.noticon-bug:before { content: "\f50a"; }
.noticon-bullhorn:before { content: "\f8a5"; }
.noticon-bullseye:before { content: "\f8a0"; }
.noticon-cart:before { content: "\f447"; }
.noticon-category:before { content: "\f301"; }
.noticon-chat:before { content: "\f108"; }
.noticon-checkmark:before { content: "\f418"; }
.noticon-close:before { content: "\f405"; }
.noticon-close-alt:before { content: "\f406"; }
.noticon-cloud:before { content: "\f426"; }
.noticon-cloud-download:before { content: "\f440"; }
.noticon-cloud-upload:before { content: "\f441"; }
.noticon-code:before { content: "\f462"; }
.noticon-codepen:before { content: "\f216"; }
.noticon-cog:before { content: "\f445"; }
.noticon-collapse:before { content: "\f432"; }
.noticon-colors:before { content: "\f8a7"; }
.noticon-comment:before { content: "\f300"; }
.noticon-compact:before { content: "\f807"; }
.noticon-day:before { content: "\f305"; }
.noticon-digg:before { content: "\f221"; }
.noticon-document:before { content: "\f443"; }
.noticon-dot:before { content: "\f428"; }
.noticon-downarrow:before { content: "\f502"; }
.noticon-download:before { content: "\f50b"; }
.noticon-draggable:before { content: "\f436"; }
.noticon-dribbble:before { content: "\f201"; }
.noticon-dropbox:before { content: "\f225"; }
.noticon-dropdown:before { content: "\f433"; }
.noticon-dropdown-left:before { content: "\f434"; }
.noticon-edit:before { content: "\f411"; }
.noticon-ellipsis:before { content: "\f476"; }
.noticon-eventbrite:before { content: "\f8a6"; }
.noticon-expand:before { content: "\f431"; }
.noticon-external:before { content: "\f442"; }
.noticon-facebook:before { content: "\f203"; }
.noticon-facebook-alt:before { content: "\f204"; }
.noticon-fastforward:before { content: "\f458"; }
.noticon-features:before { content: "\f8a8"; }
.noticon-feed:before { content: "\f413"; }
.noticon-flag:before { content: "\f468"; }
.noticon-flickr:before { content: "\f211"; }
.noticon-follow:before { content: "\f801"; }
.noticon-following:before { content: "\f803"; }
.noticon-fonts:before { content: "\f8b5"; }
.noticon-foursquare:before { content: "\f226"; }
.noticon-fullscreen:before { content: "\f474"; }
.noticon-gallery:before { content: "\f103"; }
.noticon-ghost:before { content: "\f8d5"; }
.noticon-gift:before { content: "\f8a4"; }
.noticon-github:before { content: "\f200"; }
.noticon-googleplus:before { content: "\f206"; }
.noticon-googleplus-alt:before { content: "\f218"; }
.noticon-gravatar:before { content: "\f8d0"; }
.noticon-gridview:before { content: "\f808"; }
.noticon-handset:before { content: "\f50c"; }
.noticon-heart:before { content: "\f461"; }
.noticon-help:before { content: "\f457"; }
.noticon-hide:before { content: "\f404"; }
.noticon-hierarchy:before { content: "\f505"; }
.noticon-home:before { content: "\f409"; }
.noticon-horizon:before { content: "\f8d8"; }
.noticon-image:before { content: "\f102"; }
.noticon-info:before { content: "\f455"; }
.noticon-instagram:before { content: "\f215"; }
.noticon-italic:before { content: "\f472"; }
.noticon-jetpack:before { content: "\f8d3"; }
.noticon-key:before { content: "\f427"; }
.noticon-layouts:before { content: "\f8a9"; }
.noticon-leftarrow:before { content: "\f503"; }
.noticon-lightbulb:before { content: "\f8a1"; }
.noticon-link:before { content: "\f107"; }
.noticon-linkedin:before { content: "\f207"; }
.noticon-linkedin-alt:before { content: "\f208"; }
.noticon-localization:before { content: "\f8b2"; }
.noticon-location:before { content: "\f417"; }
.noticon-lock:before { content: "\f470"; }
.noticon-mail:before { content: "\f410"; }
.noticon-maximize:before { content: "\f422"; }
.noticon-medium:before { content: "\f8d6"; }
.noticon-menu:before { content: "\f419"; }
.noticon-microphone:before { content: "\f50d"; }
.noticon-milestone:before { content: "\f806"; }
.noticon-minimize:before { content: "\f421"; }
.noticon-minus:before { content: "\f50e"; }
.noticon-month:before { content: "\f307"; }
.noticon-move:before { content: "\f50f"; }
.noticon-next:before { content: "\f429"; }
.noticon-notice:before { content: "\f456"; }
.noticon-notification:before { content: "\f800"; }
.noticon-paintbrush:before { content: "\f506"; }
.noticon-path:before { content: "\f219"; }
.noticon-pause:before { content: "\f448"; }
.noticon-phone:before { content: "\f437"; }
.noticon-picture:before { content: "\f473"; }
.noticon-pinned:before { content: "\f308"; }
.noticon-pinterest:before { content: "\f209"; }
.noticon-pinterest-alt:before { content: "\f210"; }
.noticon-play:before { content: "\f452"; }
.noticon-plugin:before { content: "\f439"; }
.noticon-plus:before { content: "\f510"; }
.noticon-pocket:before { content: "\f224"; }
.noticon-polldaddy:before { content: "\f217"; }
.noticon-portfolio:before { content: "\f460"; }
.noticon-previous:before { content: "\f430"; }
.noticon-price:before { content: "\f8b0"; }
.noticon-print:before { content: "\f469"; }
.noticon-promoted:before { content: "\f812"; }
.noticon-quote:before { content: "\f106"; }
.noticon-rating-empty:before { content: "\f511"; }
.noticon-rating-full:before { content: "\f512"; }
.noticon-rating-half:before { content: "\f513"; }
.noticon-reader:before { content: "\f8a2"; }
.noticon-reader-alt:before { content: "\f8a3"; }
.noticon-reblog:before { content: "\f805"; }
.noticon-reddit:before { content: "\f222"; }
.noticon-refresh:before { content: "\f420"; }
.noticon-reply:before { content: "\f412"; }
.noticon-reply-alt:before { content: "\f466"; }
.noticon-reply-single:before { content: "\f467"; }
.noticon-rewind:before { content: "\f459"; }
.noticon-rightarrow:before { content: "\f501"; }
.noticon-search:before { content: "\f400"; }
.noticon-send-to-phone:before { content: "\f438"; }
.noticon-send-to-tablet:before { content: "\f454"; }
.noticon-share:before { content: "\f415"; }
.noticon-show:before { content: "\f403"; }
.noticon-shuffle:before { content: "\f514"; }
.noticon-sitemap:before { content: "\f507"; }
.noticon-skip-ahead:before { content: "\f451"; }
.noticon-skip-back:before { content: "\f450"; }
.noticon-skype:before { content: "\f220"; }
.noticon-spam:before { content: "\f424"; }
.noticon-spike:before { content: "\f811"; }
.noticon-spotify:before { content: "\f515"; }
.noticon-squarespace:before { content: "\f8d7"; }
.noticon-standard:before { content: "\f100"; }
.noticon-star:before { content: "\f408"; }
.noticon-status:before { content: "\f105"; }
.noticon-stop:before { content: "\f449"; }
.noticon-stumbleupon:before { content: "\f223"; }
.noticon-subscribe:before { content: "\f463"; }
.noticon-subscribed:before { content: "\f465"; }
.noticon-summary:before { content: "\f425"; }
.noticon-tablet:before { content: "\f453"; }
.noticon-tag:before { content: "\f302"; }
.noticon-time:before { content: "\f303"; }
.noticon-title:before { content: "\f8b6"; }
.noticon-top:before { content: "\f435"; }
.noticon-trapper:before { content: "\f810"; }
.noticon-trash:before { content: "\f407"; }
.noticon-trophy:before { content: "\f804"; }
.noticon-tumblr:before { content: "\f214"; }
.noticon-twitch:before { content: "\f516"; }
.noticon-twitter:before { content: "\f202"; }
.noticon-types:before { content: "\f8b1"; }
.noticon-unapprove:before { content: "\f446"; }
.noticon-unfollow:before { content: "\f802"; }
.noticon-unsubscribe:before { content: "\f464"; }
.noticon-unzoom:before { content: "\f401"; }
.noticon-uparrow:before { content: "\f500"; }
.noticon-user:before { content: "\f304"; }
.noticon-vaultpress:before { content: "\f8d1"; }
.noticon-video:before { content: "\f104"; }
.noticon-videocamera:before { content: "\f517"; }
.noticon-vimeo:before { content: "\f212"; }
.noticon-warning:before { content: "\f414"; }
.noticon-website:before { content: "\f475"; }
.noticon-week:before { content: "\f306"; }
.noticon-wordads:before { content: "\f813"; }
.noticon-wordpress:before { content: "\f205"; }
.noticon-xpost:before { content: "\f504"; }
.noticon-youtube:before { content: "\f213"; }
.noticon-zoom:before { content: "\f402"; }




.noticon-like:before { content: "\f408"; }




@font-face {
  font-family: Genericons;
  src: url(/wp-content/mu-plugins/jetpack-plugin/sun/_inc/genericons/genericons/./Genericons.eot);
  src: url(/wp-content/mu-plugins/jetpack-plugin/sun/_inc/genericons/genericons/./Genericons.eot?) format("embedded-opentype");
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: Genericons;
  src: url(data:font/woff;base64,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) format("woff"),
       url(/wp-content/mu-plugins/jetpack-plugin/sun/_inc/genericons/genericons/./Genericons.ttf) format("truetype"),
       url(/wp-content/mu-plugins/jetpack-plugin/sun/_inc/genericons/genericons/./Genericons.svg#Genericons) format("svg");
  font-weight: 400;
  font-style: normal;
}

@media screen and (-webkit-min-device-pixel-ratio:0) {

  @font-face {
    font-family: Genericons;
    src: url(/wp-content/mu-plugins/jetpack-plugin/sun/_inc/genericons/genericons/./Genericons.svg#Genericons) format("svg");
  }
}




.genericon {
	font-size: 16px;
	vertical-align: top;
	text-align: center;
	transition: color .1s ease-in 0;
	display: inline-block;
	font-family: Genericons;
	font-style: normal;
	font-weight: 400;
	font-variant: normal;
	line-height: 1;
	text-decoration: inherit;
	text-transform: none;
	-moz-osx-font-smoothing: grayscale;
	-webkit-font-smoothing: antialiased;
	speak: none;
}




.genericon-rotate-90 {
	transform: rotate(90deg);
	filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=1);
}

.genericon-rotate-180 {
	transform: rotate(180deg);
	filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=2);
}

.genericon-rotate-270 {
	transform: rotate(270deg);
	filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=3);
}

.genericon-flip-horizontal {
	transform: scale(-1, 1);
}

.genericon-flip-vertical {
	transform: scale(1, -1);
}




.genericon-404::before { content: "\f423"; }

.genericon-activity::before { content: "\f508"; }

.genericon-anchor::before { content: "\f509"; }

.genericon-aside::before { content: "\f101"; }

.genericon-attachment::before { content: "\f416"; }

.genericon-audio::before { content: "\f109"; }

.genericon-bold::before { content: "\f471"; }

.genericon-book::before { content: "\f444"; }

.genericon-bug::before { content: "\f50a"; }

.genericon-cart::before { content: "\f447"; }

.genericon-category::before { content: "\f301"; }

.genericon-chat::before { content: "\f108"; }

.genericon-checkmark::before { content: "\f418"; }

.genericon-close::before { content: "\f405"; }

.genericon-close-alt::before { content: "\f406"; }

.genericon-cloud::before { content: "\f426"; }

.genericon-cloud-download::before { content: "\f440"; }

.genericon-cloud-upload::before { content: "\f441"; }

.genericon-code::before { content: "\f462"; }

.genericon-codepen::before { content: "\f216"; }

.genericon-cog::before { content: "\f445"; }

.genericon-collapse::before { content: "\f432"; }

.genericon-comment::before { content: "\f300"; }

.genericon-day::before { content: "\f305"; }

.genericon-digg::before { content: "\f221"; }

.genericon-document::before { content: "\f443"; }

.genericon-dot::before { content: "\f428"; }

.genericon-downarrow::before { content: "\f502"; }

.genericon-download::before { content: "\f50b"; }

.genericon-draggable::before { content: "\f436"; }

.genericon-dribbble::before { content: "\f201"; }

.genericon-dropbox::before { content: "\f225"; }

.genericon-dropdown::before { content: "\f433"; }

.genericon-dropdown-left::before { content: "\f434"; }

.genericon-edit::before { content: "\f411"; }

.genericon-ellipsis::before { content: "\f476"; }

.genericon-expand::before { content: "\f431"; }

.genericon-external::before { content: "\f442"; }

.genericon-facebook::before { content: "\f203"; }

.genericon-facebook-alt::before { content: "\f204"; }

.genericon-fastforward::before { content: "\f458"; }

.genericon-feed::before { content: "\f413"; }

.genericon-flag::before { content: "\f468"; }

.genericon-flickr::before { content: "\f211"; }

.genericon-foursquare::before { content: "\f226"; }

.genericon-fullscreen::before { content: "\f474"; }

.genericon-gallery::before { content: "\f103"; }

.genericon-github::before { content: "\f200"; }

.genericon-googleplus::before { content: "\f206"; }

.genericon-googleplus-alt::before { content: "\f218"; }

.genericon-handset::before { content: "\f50c"; }

.genericon-heart::before { content: "\f461"; }

.genericon-help::before { content: "\f457"; }

.genericon-hide::before { content: "\f404"; }

.genericon-hierarchy::before { content: "\f505"; }

.genericon-home::before { content: "\f409"; }

.genericon-image::before { content: "\f102"; }

.genericon-info::before { content: "\f455"; }

.genericon-instagram::before { content: "\f215"; }

.genericon-italic::before { content: "\f472"; }

.genericon-key::before { content: "\f427"; }

.genericon-leftarrow::before { content: "\f503"; }

.genericon-link::before { content: "\f107"; }

.genericon-linkedin::before { content: "\f207"; }

.genericon-linkedin-alt::before { content: "\f208"; }

.genericon-location::before { content: "\f417"; }

.genericon-lock::before { content: "\f470"; }

.genericon-mail::before { content: "\f410"; }

.genericon-maximize::before { content: "\f422"; }

.genericon-menu::before { content: "\f419"; }

.genericon-microphone::before { content: "\f50d"; }

.genericon-minimize::before { content: "\f421"; }

.genericon-minus::before { content: "\f50e"; }

.genericon-month::before { content: "\f307"; }

.genericon-move::before { content: "\f50f"; }

.genericon-next::before { content: "\f429"; }

.genericon-notice::before { content: "\f456"; }

.genericon-paintbrush::before { content: "\f506"; }

.genericon-path::before { content: "\f219"; }

.genericon-pause::before { content: "\f448"; }

.genericon-phone::before { content: "\f437"; }

.genericon-picture::before { content: "\f473"; }

.genericon-pinned::before { content: "\f308"; }

.genericon-pinterest::before { content: "\f209"; }

.genericon-pinterest-alt::before { content: "\f210"; }

.genericon-play::before { content: "\f452"; }

.genericon-plugin::before { content: "\f439"; }

.genericon-plus::before { content: "\f510"; }

.genericon-pocket::before { content: "\f224"; }

.genericon-polldaddy::before { content: "\f217"; }

.genericon-portfolio::before { content: "\f460"; }

.genericon-previous::before { content: "\f430"; }

.genericon-print::before { content: "\f469"; }

.genericon-quote::before { content: "\f106"; }

.genericon-rating-empty::before { content: "\f511"; }

.genericon-rating-full::before { content: "\f512"; }

.genericon-rating-half::before { content: "\f513"; }

.genericon-reddit::before { content: "\f222"; }

.genericon-refresh::before { content: "\f420"; }

.genericon-reply::before { content: "\f412"; }

.genericon-reply-alt::before { content: "\f466"; }

.genericon-reply-single::before { content: "\f467"; }

.genericon-rewind::before { content: "\f459"; }

.genericon-rightarrow::before { content: "\f501"; }

.genericon-search::before { content: "\f400"; }

.genericon-send-to-phone::before { content: "\f438"; }

.genericon-send-to-tablet::before { content: "\f454"; }

.genericon-share::before { content: "\f415"; }

.genericon-show::before { content: "\f403"; }

.genericon-shuffle::before { content: "\f514"; }

.genericon-sitemap::before { content: "\f507"; }

.genericon-skip-ahead::before { content: "\f451"; }

.genericon-skip-back::before { content: "\f450"; }

.genericon-spam::before { content: "\f424"; }

.genericon-spotify::before { content: "\f515"; }

.genericon-standard::before { content: "\f100"; }

.genericon-star::before { content: "\f408"; }

.genericon-status::before { content: "\f105"; }

.genericon-stop::before { content: "\f449"; }

.genericon-stumbleupon::before { content: "\f223"; }

.genericon-subscribe::before { content: "\f463"; }

.genericon-subscribed::before { content: "\f465"; }

.genericon-summary::before { content: "\f425"; }

.genericon-tablet::before { content: "\f453"; }

.genericon-tag::before { content: "\f302"; }

.genericon-time::before { content: "\f303"; }

.genericon-top::before { content: "\f435"; }

.genericon-trash::before { content: "\f407"; }

.genericon-tumblr::before { content: "\f214"; }

.genericon-twitch::before { content: "\f516"; }

.genericon-twitter::before { content: "\f202"; }

.genericon-unapprove::before { content: "\f446"; }

.genericon-unsubscribe::before { content: "\f464"; }

.genericon-unzoom::before { content: "\f401"; }

.genericon-uparrow::before { content: "\f500"; }

.genericon-user::before { content: "\f304"; }

.genericon-video::before { content: "\f104"; }

.genericon-videocamera::before { content: "\f517"; }

.genericon-vimeo::before { content: "\f212"; }

.genericon-warning::before { content: "\f414"; }

.genericon-website::before { content: "\f475"; }

.genericon-week::before { content: "\f306"; }

.genericon-wordpress::before { content: "\f205"; }

.genericon-xpost::before { content: "\f504"; }

.genericon-youtube::before { content: "\f213"; }

.genericon-zoom::before { content: "\f402"; }










html {
	font-family: sans-serif;

	-webkit-text-size-adjust: 100%;
		-ms-text-size-adjust: 100%;
}

body {
	margin: 0;
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
menu,
nav,
section {
	display: block;
}

summary {
	display: list-item;
}

audio,
canvas,
progress,
video {
	display: inline-block;
	vertical-align: baseline;
}

audio:not([controls]) {
	display: none;
	height: 0;
}

[hidden],
template {
	display: none;
}

a {
	background-color: transparent;
}

a:active,
a:hover {
	outline: 0;
}

abbr[title] {
	border-bottom: 1px dotted;
}

b,
strong {
	font-weight: 700;
}

dfn {
	font-style: italic;
}

h1 {
	margin: .67em 0;
	font-size: 2em;
}

mark {
	color: #000;
	background: #ff0;
}

small {
	font-size: 80%;
}

sub,
sup {
	position: relative;
	font-size: 75%;
	line-height: 0;
	vertical-align: baseline;
}

sup {
	top: -.5em;
}

sub {
	bottom: -.25em;
}

img {
	max-width: 100%;
	height: auto;
	border: 0;
}

svg:not(:root) {
	overflow: hidden;
}

figure {
	margin: 1em 40px;
}

hr {
	box-sizing: content-box;
	height: 0;
}

pre {
	overflow: auto;
}

code,
kbd,
pre,
samp {
	font-family: Monaco, Consolas, "Andale Mono", "DejaVu Sans Mono", monospace;
	font-size: 1em;
}

button,
input,
optgroup,
select,
textarea {
	margin: 0;
	font: inherit;
	color: inherit;
}

button {
	overflow: visible;
}

button,
select {
	text-transform: none;
}

button,
html input[type="button"],
input[type="reset"],
input[type="submit"] {
	cursor: pointer;

	-webkit-appearance: button;
}

button[disabled],
html input[disabled] {
	cursor: default;
}

button::-moz-focus-inner,
input::-moz-focus-inner {
	padding: 0;
	border: 0;
}

input {
	line-height: normal;
}

input[type="checkbox"],
input[type="radio"] {
	box-sizing: border-box;
	padding: 0;
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
	height: auto;
}

input[type="search"] {
	box-sizing: content-box;

	-webkit-appearance: textfield;
}

input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-decoration {
	-webkit-appearance: none;
}

fieldset {
	margin: 0 2px;
	padding: .35em .625em .75em;
	border: 1px solid #c0c0c0;
}

legend {
	padding: 0;
	border: 0;
}

textarea {
	overflow: auto;
}

optgroup {
	font-weight: 700;
}

table {
	border-spacing: 0;
	border-collapse: collapse;
}

td,
th {
	padding: 0;
}



body {
	background: #fff;
}

body,
button,
input,
select,
textarea {
	font-family: Georgia, "Times New Roman", serif;
	font-size: 18px;
	line-height: 1.75;
	color: #383838;
}

select {
	font-size: 13px;
}

h1,
h2,
h3,
h4,
h5,
h6 {
	margin-top: .875em;
	margin-bottom: .875em;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen-Sans", "Ubuntu", "Cantarell", "Helvetica Neue", sans-serif;
	line-height: 1.1;
	color: #333332;
}

h1 {
	font-size: 32px;
	margin-bottom: .4375em;
}

h2 {
	font-size: 24px;
	margin-bottom: .4375em;
}

h3 {
	font-size: 19px;
}

h4 {
	font-size: 15px;
}

h5 {
	font-size: 14px;
}

h6 {
	font-size: 13px;
}

h5,
h6 {
	line-height: 1.3;
}

hr {
	height: 1px;
	margin: 1.75em 0;
	border: 0;
	background-color: #ccc;
}

p {
	margin-bottom: .875em;
}

ul,
ol {
	margin: 0 0 1.75em 2em;
	padding: 0;
	list-style-position: outside;
}

ul ul,
ol ul,
ul ol,
ol ol {
	margin-bottom: 0;
	margin-left: 2em;
}

ul li,
ol li {
	margin-top: .4375em;
}

ul {
	list-style-type: disc;
}

ol {
	list-style-type: decimal;
}

dt {
	font-weight: 700;
}

dd {
	margin: .4375em 1.75em 1.75em;
}

b,
strong {
	font-weight: 700;
}

dfn,
cite,
em,
i {
	font-style: italic;
}

blockquote {
	margin: 1.75em .875em 1.75em -1.9em;
	padding: 0 0 0 1.75em;
	font-family: Georgia, "Times New Roman", serif;
	font-style: italic;
	border: solid #0087be;
	border-width: 0 0 0 3px;
}

.comments-area blockquote {
	margin-left: 0;
}

blockquote blockquote {
	margin-left: 1.75em;
}

blockquote.aligncenter,
blockquote.alignleft,
blockquote.alignright {
	padding: .875em 0;
	font-family: Georgia, "Times New Roman", serif;
	font-size: 20px;
	font-style: italic;
	text-align: center;
	color: #a5a5a5;
	border: 0;
}
blockquote.alignright,
blockquote.alignleft {
	max-width: 100%;
	padding: 0;
	margin-left: auto;
	margin-right: auto;
	text-align: left;
}

blockquote cite {
	display: block;
	margin-top: .875em;
	font-size: .875em;
	font-weight: bold;
}

blockquote cite:before {
	margin-right: .21875em;
	margin-left: 0;
	content: "\2014";
	font-weight: normal;
}

blockquote h1,
blockquote h2,
blockquote h3,
blockquote h4 {
	font-family: inherit;
	font-weight: normal;
}

address {
	margin: 0 0 1.75em;
}

pre {
	overflow: auto;
	max-width: 100%;
	padding: 1.75em;
	font-family: Monaco, Consolas, "Andale Mono", "DejaVu Sans Mono", monospace;
	line-height: 1.75;
	background: #f5f5f5;
}

pre,
code,
kbd,
tt,
var {
	font-size: 18px;
}

pre pre,
pre code,
pre kbd,
pre tt,
pre var {
	background: transparent;
}

code,
kbd,
tt,
var {
	padding: 2px 5px 2px 5px;
	font-family: Monaco, Consolas, "Andale Mono", "DejaVu Sans Mono", monospace;
	background: #eee;
}

abbr,
acronym {
	cursor: help;
	border-bottom: 1px dotted #666;
}

mark,
ins {
	text-decoration: none;
	background: #fff9c0;
}

sup,
sub {
	position: relative;
	height: 0;
	font-size: 75%;
	line-height: 0;
	vertical-align: baseline;
}

sup {
	bottom: 1ex;
}

sub {
	top: .5ex;
}

small {
	font-size: 75%;
}

big {
	font-size: 125%;
}

figure {
	margin: 0;
}

table {
	width: 100%;
	margin: 0 0 1.75em;
	font-size: 90%;
}

th {
	font-weight: 700;
}

textarea {
	overflow: auto;
	width: 95%;
	padding-left: 3px;
	vertical-align: top;
}

a {
	text-decoration: none;
	color: #0087be;
}

a:visited {
	text-decoration: none;
	color: #0087be;
}

a:hover {
	text-decoration: underline;
}

a:hover,
a:focus,
a:active {
	color: #00aadc;
}

a:focus,
a:active,
button:focus,
button:active {
	outline: thin dotted;
}

table,
th,
td {
	border: 1px solid #ddd;
}

table {
	
	width: 100%;
	margin: 0 0 1.75em;
	border-spacing: 0;
	border-collapse: separate;
	border-width: 1px 0 0 1px;
}

caption,
th,
td {
	font-weight: normal;
	text-align: left;
}

th {
	font-weight: 700;
	border-width: 0 1px 1px 0;
}

td {
	border-width: 0 1px 1px 0;
}

th,
td {
	padding: .4375em;
	word-break: break-word;
}

.wp-block-cover.has-white-background-color .wp-block-cover__inner-container {
	color: #383838;
}



button,
input,
select,
textarea {
	max-width: 100%;
	margin: 0;
	font: inherit;
	vertical-align: middle;
}

button:focus,
input:focus,
button:active,
input:active {
	outline: 0;
}

.button,
.more-link,
button,
input[type="button"],
input[type="reset"],
input[type="submit"],
.posts-navigation .nav-links a,
#content #infinite-handle span button {
	display: inline-block;
	box-sizing: content-box;
	padding: .4375em .875em;
	cursor: pointer;
	-webkit-transition: background 120ms ease-in-out, box-shadow 120ms ease-in-out;
			transition: background 120ms ease-in-out, box-shadow 120ms ease-in-out;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen-Sans", "Ubuntu", "Cantarell", "Helvetica Neue", sans-serif;
	font-size: 16px;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	color: #fff;
	border: solid 1px transparent;
	border-radius: 3px;
	background: #0087be;

	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.button:focus,
.button:active,
.more-link:focus,
.more-link:active,
button:focus,
input[type="button"]:focus,
input[type="reset"]:focus,
input[type="submit"]:focus,
.posts-navigation .nav-links a:focus,
#content #infinite-handle span button:focus,
button:active,
input[type="button"]:active,
input[type="reset"]:active,
input[type="submit"]:active,
.posts-navigation .nav-links a:active,
#content #infinite-handle span button:active {
	outline: 0;
	background: #767676;
	box-shadow: inset 0 2px 2px rgba(0, 0, 0, .25), 0 0 0 6px rgba(0, 0, 0, .08);
}

.button:hover,
.more-link:hover,
button:hover,
input[type="button"]:hover,
input[type="reset"]:hover,
input[type="submit"]:hover,
.posts-navigation .nav-links a:hover,
#content #infinite-handle span button:hover {
	text-decoration: none;
	color: #fff;
	background: #767676;
}

.button:visited,
.more-link:visited {
	color: white;
}

.more-link {
	display: block;
	clear: both;
	width: 130px;
	margin: .4375em 0 1.75em;
	text-align: center;
}

input + button,
input + input[type="button"],
input + input[type="reset"],
input + input[type="submit"],
input + .posts-navigation .nav-links a,
input + #content #infinite-handle span button,
label + button,
label + input[type="button"],
label + input[type="reset"],
label + input[type="submit"],
label + .posts-navigation .nav-links a,
label + #content #infinite-handle span button {
	margin-left: .21875em;
}

input[type="text"],
input[type="password"],
input[type="email"],
input[type="search"],
input[type="url"],
textarea {
	box-sizing: border-box;
	-webkit-transition: color 160ms ease, border-color 160ms ease, box-shadow 160ms ease;
			transition: color 160ms ease, border-color 160ms ease, box-shadow 160ms ease;
	font-family: Georgia, "Times New Roman", serif;
	color: #404040;
	border: 1px solid #a1a1a1;
	border-radius: 3px;

	-webkit-appearance: none;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

input[type="text"]:focus,
input[type="password"]:focus,
input[type="email"]:focus,
input[type="search"]:focus,
input[type="url"]:focus,
textarea:focus {
	border-color: #0087be;
	outline: 0;
	box-shadow: 0 0 3px rgba(87, 173, 104, .15) inset;
}

input[type="text"],
input[type="password"],
input[type="email"],
input[type="search"],
input[type="url"] {
	height: 1.75em;
	padding: 0 .875em;
	line-height: 2;
}

textarea {
	padding: .4375em .875em;
}

input[type="checkbox"],
input[type="radio"] {
	position: relative;
	top: 0;
	box-sizing: border-box;
	margin-right: .1em;
	padding: 0;
	line-height: 1;
	vertical-align: baseline;
}

.entry-content input[type="checkbox"],
.entry-content input[type="radio"],
.entry-summary input[type="checkbox"],
.entry-summary input[type="radio"] {
	top: -.1em;
}

button::-moz-focus-inner,
input::-moz-focus-inner {
	padding: 0;
	border: 0;
}

:focus,
:active {
	outline: 0;
}



.site-content .nav-previous {
	float: left;
}

.site-content .nav-next {
	float: right;
	text-align: right;
}



.menu-toggle {
	margin: 0 auto;
	display: block;
	padding: .4375em .875em;
	-webkit-transition: background 200ms ease-in-out, box-shadow 200ms ease-in-out, color 200ms ease-in-out;
			transition: background 200ms ease-in-out, box-shadow 200ms ease-in-out, color 200ms ease-in-out;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen-Sans", "Ubuntu", "Cantarell", "Helvetica Neue", sans-serif;
	font-size: 17px;
	font-weight: bold;
	line-height: 1;	
	border: solid 2px currentColor;
	border-radius: 4px;
	background: none;
	color: #383838;

	-webkit-font-smoothing: antialiased;
}

.menu-toggle:hover,
.menu-toggle:focus,
.menu-toggle:active {
	text-decoration: none;
	color: #7e7f80;
	outline: 0;
	background: transparent;
}

.menu-toggle:focus,
.menu-toggle:active {
	box-shadow: 0 0 0 6px rgba(187, 199, 211, .17);
}

.toggled .menu-toggle:before {
	top: 3px;
	position: relative;
	content: "\f405";
	font-family: Genericons;
	line-height: .65;
	margin-right: 3px;
	font-size: 16px;
	-webkit-transform: scale(1.5);
		-ms-transform: scale(1.5);
			transform: scale(1.5);
}



.posts-navigation {
	margin-top: 1.75em;
	padding-top: 1.75em;
	border-top: solid 1px #ddd;
}

.infinite-scroll .posts-navigation {
	display: none;
}

.posts-navigation .nav-links a {
	font-size: 15px;
}

.posts-navigation .nav-links .nav-previous a:before,
.posts-navigation .nav-links .nav-next a:after {
	position: relative;
	top: 0;
}

.posts-navigation .nav-links .nav-previous a:before {
	margin-right: .4375em;
	content: "\2190" ;
}

.posts-navigation .nav-links .nav-next a:after {
	margin-left: .4375em;
	content: "\2192" ;
}



.post-navigation {
	margin-top: 2.675em;
	padding: .4375em 0;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen-Sans", "Ubuntu", "Cantarell", "Helvetica Neue", sans-serif;
	border-bottom: solid 1px #ddd;
	border-top: solid 1px #ddd;
}

.post-navigation .nav-links {
	font-size: 15px;
}

.post-navigation .nav-links a {
	display: inline-block;
	padding: .21875em 0;
	-webkit-transition: color 140ms ease-in-out;
			transition: color 140ms ease-in-out;
	font-weight: normal;
	vertical-align: middle;
	color: #b6b6b4;
}

.post-navigation .nav-links a:hover,
.post-navigation .nav-links a:focus,
.post-navigation .nav-links a:active {
	text-decoration: none;
	color: #747471;
}

.post-navigation .nav-links .nav-previous a:before,
.post-navigation .nav-links .nav-next a:after {
	position: relative;
	display: inline-block;
	margin: 0;
	font-family: "Genericons";
	vertical-align: middle;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.post-navigation .nav-links .nav-previous {
	clear: both;
	margin: 0 0 0 -.21875em;
	width: 100%;
}

.post-navigation .nav-links .nav-previous a:before {
	margin-right: .21875em;
	content: "\f431";
	-webkit-transform: rotate(90deg);
		-ms-transform: rotate(90deg);
			transform: rotate(90deg);
}

.post-navigation .nav-links .nav-next {
	margin: 0 -.21875em 0 0;
}

.post-navigation .nav-links .nav-next a:after {
	margin-left: .21875em;
	content: "\f432";
	-webkit-transform: rotate(90deg);
		-ms-transform: rotate(90deg);
			transform: rotate(90deg);
}



.comment-navigation {
	padding: 0;
	border: none;
}

.comments-area #comment-nav-above {
	padding-bottom: .21875em;
}

.comments-area #comment-nav-above + .comment-list > li.comment:last-child {
	padding-bottom: 1.75em;
	border-bottom-width: 1px;
}

.comments-area #comment-nav-above + .comment-list > li.comment:last-child .reply {
	margin-bottom: 0;
}

#comment-nav-below {
	padding-top: .21875em;
}



.main-navigation {
	display: block;
	clear: both;
	width: 100%;
	margin: 0 auto;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen-Sans", "Ubuntu", "Cantarell", "Helvetica Neue", sans-serif;
	font-size: 17px;
	font-weight: 700;
	text-align: left;
}
.main-navigation a {
	display: block;
	padding: .20875em .4375em;
	text-decoration: none;
	-webkit-transition: color 140ms ease-in-out;
			transition: color 140ms ease-in-out;
}
.main-navigation ul {
	display: none;
	margin: 0;
	padding-left: 0;
	list-style: none;
}
.main-navigation li {
	border-top: 1px solid #ccc;
	position: relative;
	display: inline-block;
	margin-top: .4375em;
	padding-top: .4375em;
	line-height: 1.3;
	width: 100%;
}
.main-navigation ul:first-child > li:first-child {
	border-top: 0;
}

.main-navigation > div > ul > li.current-menu-item > a,
.main-navigation > div > ul > li.current_page_item > a {
	color: #00aadc;
}


.menu-toggle,
.main-navigation.toggled ul {
	display: block;
}

.menu-toggle:before {
	display: inline-block;
	margin-right: 5px;
	content: "\2630";
}

.main-navigation ul a {
	padding-left: 1.75em;
	padding-right: 1.75em;
}

.main-navigation ul ul a {
	padding-left: 3.5em;
}

.main-navigation ul ul ul a {
	padding-left: 5.25em;
}

.main-navigation ul ul ul ul a {
	padding-left: 7em;
}

.main-navigation ul ul ul ul ul a {
	padding-left: 8.75em;
}

.main-navigation ul ul ul ul ul ul a {
	padding-left: 10.5em;
}

.has-header-image .menu-toggle {
	color: white;
	border-color: white;
}




.screen-reader-text {
	position: absolute !important;
	overflow: hidden;
	clip: rect(1px, 1px, 1px, 1px);
	width: 1px;
	height: 1px;
}

.screen-reader-text:focus {
	z-index: 100000;
	top: 7px;
	right: auto;
	left: 5px;
	display: block;
	clip: auto !important;
	width: auto;
	height: auto;
	padding: 15px 23px 14px;
	font-size: 15px;
	font-weight: 700;
	line-height: normal;
	text-decoration: none;
	color: #21759b;
	outline: solid 1px;
	background-color: #f1f1f1;
	box-shadow: 0 0 2px 2px rgba(0, 0, 0, .25);
}



.entry-author:before,
.comment-author:before,
.site-branding:before,
.entry-content:before,
.entry-footer:before,
.site-logo:before,
.gallery:before,
.nav-links:before,
.post-tags:before,
.content-wrapper:before,
.jetpack-social-navigation:before,
.site-content:before,
.footer-widgets:before,
.entry-author:after,
.comment-author:after,
.site-branding:after,
.entry-content:after,
.entry-footer:after,
.site-logo:after,
.gallery:after,
.nav-links:after,
.post-tags:after,
.content-wrapper:after,
.jetpack-social-navigation:after,
.site-content:after,
.footer-widgets:after {
	display: table;
	content: " ";
}

.entry-author:after,
.comment-author:after,
.site-branding:after,
.entry-content:after,
.entry-footer:after,
.site-logo:after,
.gallery:after,
.nav-links:after,
.post-tags:after,
.content-wrapper:after,
.jetpack-social-navigation:after,
.site-content:after,
.footer-widgets:after {
	clear: both;
}



.entry-header,
.entry-content,
.entry-summary,
.entry-meta,
.comment-content,
.widget {
	word-wrap: break-word;
}

img[class*="wp-image-"] {
	vertical-align: middle;
	border-radius: 3px;
}

.wp-post-image {
	display: block;
	margin: 0 auto;
	vertical-align: middle;
	border-radius: 3px;
}

.entry-header + .wp-post-image {
	margin-top: 1.75em;
}

a.more-link .meta-nav {
	display: none;
}

.entry-content,
.entry-summary {
	margin: 0 0 .875em;
	font-size: 17px;
}

.post-image-link + .entry-content,
.post-image-link + .entry-summary,
.wp-post-image + .entry-content,
.wp-post-image + .entry-summary,
.entry-header + .entry-content,
.entry-header + .entry-summary {
	margin-top: 1.75em;
}

.entry-header + .entry-content,
.entry-header + .entry-summary {
	margin-top: .875em;
}

.entry-content {
	margin-top: 0;
}

body:not(.single) .hentry.empty-content .entry-content {
	margin-top: 0;
}

.entry-content .sharedaddy {
	margin: 1.75em 0;
}

.entry-content + .entry-footer,
.entry-summary + .entry-footer {
	margin-top: 1.75em;
}

body:not(.single) .entry-content + .entry-footer,
body:not(.single) .entry-summary + .entry-footer {
	margin-top: .875em;
}

.entry-footer > ul,
.entry-footer > span,
.entry-footer > div {
	margin: 1.75em 0 0;
}

.entry-content,
.entry-summary,
.page-content {
	font-family: Georgia, "Times New Roman", serif;
}

.entry-content .subtitle {
	margin-top: -5px;
	margin-bottom: 1.75em;
	font-family: Georgia, "Times New Roman", serif;
	font-size: 24px;
	font-weight: normal;
	font-style: italic;
	line-height: 1.2;
}

div#jp-relatedposts h3.jp-relatedposts-headline em {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen-Sans", "Ubuntu", "Cantarell", "Helvetica Neue", sans-serif;
}

.site .comments-area > :first-child,
.site #jp-post-flair > :first-child,
.site .taxonomy-description > :first-child,
.site .comment-content > :first-child,
.site .entry-footer > :first-child,
.site .entry-caption > :first-child,
.site .entry-header > :first-child,
.site .entry-content > :first-child,
.site .entry-summary > :first-child,
.site .entry-meta > :first-child,
.site .page-content > :first-child,
.site .page-header > :first-child,
.site .widget > :first-child,
.site blockquote > :first-child {
	margin-top: 0;
}

.site .comments-area > :last-child,
.site #jp-post-flair > :last-child,
.site .taxonomy-description > :last-child,
.site .comment-content > :last-child,
.site .entry-footer > :last-child,
.site .entry-caption > :last-child,
.site .entry-header > :last-child,
.site .entry-content > :last-child,
.site .entry-summary > :last-child,
.site .entry-meta > :last-child,
.site .page-content > :last-child,
.site .page-header > :last-child,
.site .widget > :last-child,
.site blockquote > :last-child {
	margin-bottom: 0;
}

.alignleft {
	display: inline;
	float: left;
	margin-right: 1.75em;
}

.alignright {
	display: inline;
	float: right;
	margin-left: 1.75em;
}

.aligncenter {
	display: block;
	clear: both;
	margin: .875em auto;
}

img.alignleft {
	margin: 5px 20px 10px 0;
}

img.alignright {
	margin: 5px 0 10px 20px;
}

img.aligncenter {
	margin: .4375em auto .875em;
}



.page-content .wp-smiley,
.entry-content .wp-smiley,
.comment-content .wp-smiley {
	margin-top: 0;
	margin-bottom: 0;
	padding: 0;
	border: none;
}

.wp-caption {
	max-width: 100%;
	margin-bottom: 1.75em;
}

.wp-caption.aligncenter,
.wp-caption.alignleft,
.wp-caption.alignright {
	margin-bottom: 1.75em;
}

.wp-caption img {
	display: block;
	max-width: 98%;
	margin: 5px auto 0;
}

.wp-caption-text,
.wp-caption-dd {
	clear: both;
	font-size: 75%;
	font-weight: 400;
	font-style: italic;
	text-align: center;
	color: #939393;
	width: 100%;
}

.wp-caption-text strong,
.wp-caption-dd strong {
	color: #454545;
}

.wp-caption .wp-caption-text,
.wp-caption .wp-caption-dd {
	margin: .875em 0;
}


embed,
iframe,
object {
	max-width: 100%;
}

img.grav-hijack,
img.no-grav {
    border-radius: 80px;
    border: none;
}



.tiled-gallery {
	margin: 1.75em 0;
}

.tiled-gallery a:focus {
	outline: 0;
}

.gallery {
	margin: 1.75em 0;
}

.hentry .entry-content > .gallery:first-child {
	margin-top: .875em;
}

.gallery-item {
	position: relative;
	float: left;
	overflow: hidden;
	width: 100%;
	margin: 1.75em 0 0 4px;
}

.gallery-icon {
	text-align: center;
}

.gallery-icon a {
	display: block;
	outline: 0;
}

.gallery-icon img {
	line-height: 1;
	vertical-align: middle;
	border-radius: 3px;
}

.gallery-columns-1 .gallery-item {
	max-width: 100%;
}

.gallery-columns-1 .gallery-item:first-child {
	margin-top: 0;
}

.gallery-columns-2 .gallery-item {
	max-width: -webkit-calc(50% - 2px);
	max-width:				 calc(50% - 2px);
}

.gallery-columns-2 .gallery-item:nth-child(-n+2) {
	margin-top: 0;
}

.gallery-columns-3 .gallery-item {
	max-width: -webkit-calc(100%/3 - 5px*2/3);
	max-width:				 calc(100%/3 - 5px*2/3);
}

.gallery-columns-3 .gallery-item:nth-child(-n+3) {
	margin-top: 0;
}

.gallery-columns-4 .gallery-item {
	max-width: -webkit-calc(100%/4 - 4px*3/4);
	max-width:				 calc(100%/4 - 4px*3/4);
}

.gallery-columns-4 .gallery-item:nth-child(-n+4) {
	margin-top: 0;
}

.gallery-columns-5 .gallery-item {
	max-width: -webkit-calc(100%/5 - 4px*4/5);
	max-width:				 calc(100%/5 - 4px*4/5);
}

.gallery-columns-5 .gallery-item:nth-child(-n+5) {
	margin-top: 0;
}

.gallery-columns-6 .gallery-item {
	max-width: -webkit-calc(100%/6 - 4px*5/6);
	max-width:				 calc(100%/6 - 4px*5/6);
}

.gallery-columns-6 .gallery-item:nth-child(-n+6) {
	margin-top: 0;
}

.gallery-columns-7 .gallery-item {
	max-width: -webkit-calc(100%/7 - 4px*6/7);
	max-width:				 calc(100%/7 - 4px*6/7);
}

.gallery-columns-7 .gallery-item:nth-child(-n+7) {
	margin-top: 0;
}

.gallery-columns-8 .gallery-item {
	max-width: -webkit-calc(100%/8 - 4px*7/8);
	max-width:				 calc(100%/8 - 4px*7/8);
}

.gallery-columns-8 .gallery-item:nth-child(-n+8) {
	margin-top: 0;
}

.gallery-columns-9 .gallery-item {
	max-width: -webkit-calc(100%/9 - 4px*8/9);
	max-width:				 calc(100%/9 - 4px*8/9);
}

.gallery-columns-9 .gallery-item:nth-child(-n+9) {
	margin-top: 0;
}

.gallery-caption {
	margin-top: .875em;
}

.gallery-columns-7 .gallery-caption,
.gallery-columns-8 .gallery-caption,
.gallery-columns-9 .gallery-caption {
	display: none;
}

.gallery-columns-1 .gallery-item:nth-of-type(1n+1),
.gallery-columns-2 .gallery-item:nth-of-type(2n+1),
.gallery-columns-3 .gallery-item:nth-of-type(3n+1),
.gallery-columns-4 .gallery-item:nth-of-type(4n+1),
.gallery-columns-5 .gallery-item:nth-of-type(5n+1),
.gallery-columns-6 .gallery-item:nth-of-type(6n+1),
.gallery-columns-7 .gallery-item:nth-of-type(7n+1),
.gallery-columns-8 .gallery-item:nth-of-type(8n+1),
.gallery-columns-9 .gallery-item:nth-of-type(9n+1) {
	clear: left;
	margin-left: 0;
}



.comments-area {
	padding-top: 2.675em;
	font-family: Georgia, "Times New Roman", serif;
}

.comments-title,
.comment-reply-title {
	display: inline-block;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen-Sans", "Ubuntu", "Cantarell", "Helvetica Neue", sans-serif;
	font-size: 32px;
	font-weight: 700;
}

.comment-reply-title {
	margin: 0 0 .875em;
}

.comment-reply-title small {
	visibility: hidden;
	vertical-align: middle;
}

.comment .comment-reply-title small {
	visibility: visible;
}

body:not(.highlander-enabled) .comment-reply-title small:before {
	margin: 0 .875em;
	content: "|";
	font-size: 15px;
	opacity: .35;

	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.comments-title {
	margin: 0 0 1.75em;
}

.form-allowed-tags,
span.says {
	display: none;
}

.comment-list {
	margin: 0;
	padding: 0;
	list-style: none;
}

.comment-list > li {
	margin: 0;
	padding: 0;
	list-style: none;
	list-style-position: inside;
}

.comment-list > li > a:hover {
	text-decoration: none;
}

.comment-list ol.children {
	margin: 0;
	padding: 0 0 0 1.75em;
	list-style: none;
}

.comment-list ol.children > li {
	margin: 0;
	padding: 1.75em 1.75em 0;
	list-style: none;
	list-style-position: inside;
}

.comment-list ol.children > li > a:hover {
	text-decoration: none;
}

.comment-list li.comment,
.comment-list ol.children li.comment {
	margin-top: 1.75em;
	margin-left: -1px;
}

.comment-list li.pingback,
.comment-list li.trackback {
	margin-top: 1.75em;
}

.comment-list > li:first-child {
	margin: 0;
}

.pingback,
.trackback {
	font-size: 17px;
	border-left: solid 1px #e8e8e8;
}

.comment-list .pingback,
.comment-list .trackback {
	padding-left: .875em;
}

.pingback:before,
.trackback:before {
	position: relative;
	top: 2px;
	float: left;
	font-size: 24px;
	line-height: 1;
	color: #0087be;

	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.pingback a:first-child,
.trackback a:first-child {
	margin: 0 0 0 .21875em;
}

.pingback span.edit-link,
.trackback span.edit-link {
	margin-left: .21875em;
	font-size: 14px;
	color: #4c4c4c;
}

.pingback span.edit-link:before,
.trackback span.edit-link:before {
	margin: 0 .109375em 0 0;
	content: "|";
	opacity: .5;
	color: inherit;

	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}
.pingback span.edit-link .comment-edit-link,
.trackback span.edit-link .comment-edit-link {
	color: inherit;

	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.pingback span.edit-link .comment-edit-link:hover,
.trackback span.edit-link .comment-edit-link:hover {
	text-decoration: none;
	color: #000;
}

.comment {
	position: relative;
	margin-top: 3.5em;
	font-size: 17px;
	border: solid #e8e8e8;
	border-width: 1px 0 0 1px;
}

.comment .reply {
	margin: .875em 0 0;
}

.comment .comment-body {
	padding: 1.75em 0 0 1.75em;
}

.comment .comment-reply-link {
	font-size: 15px;
	text-decoration: none;
}

.comment .comment-reply-link:before {
	margin-right: .25em;
	content: "\f412";
	font-family: "Genericons";
	font-size: 16px;

	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.comment .comment-reply-link:hover {
	color: #70b97f;
}

.comment .comment-content {
	margin-top: 1.75em;
}

.comment .comment-meta {
	position: relative;
	min-height: 48px;
	padding-left: 63px;
	font-size: 14px;
	color: #b3b3b1;
}

.comment .comment-meta .comment-metadata a {
	text-decoration: none;
	color: inherit;
}

.comment .comment-meta .comment-metadata a:hover {
	color: #0087be;
}

.comment .comment-meta span.edit-link:before {
	margin: 0 .4375em;
	content: "|";
	opacity: .5;
}

.comment .comment-meta .comment-author .avatar {
	position: absolute;
	top: 0;
	left: 0;
	display: block;
	line-height: 1;
	border-radius: 100%;
}

.comment .comment-meta .comment-author .fn {
	font-size: 17px;
	color: #000;
}

.comment .comment-meta .comment-author .fn a {
	font: inherit;
	color: inherit;
}

.comment-awaiting-moderation {
	margin: 1.2em 0 -5px -63px;
	font-size: 17px;
	font-style: italic;
}

.comment-awaiting-moderation:before,
.comment-awaiting-moderation:after {
	content: "\2014";

	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.comment-awaiting-moderation:before {
	margin-right: .4375em;
}

.comment-awaiting-moderation:after {
	margin-left: .4375em;
}

.bypostauthor {
	border-color: #92bd9a;
	box-shadow: 2px 2px 3px #e7f3e9 inset;
}

.comment-respond {
	margin-top: 1.75em;
}

.comment > .comment-respond {
	margin-top: 1.75em;
	margin-left: 1.75em;
}

body:not(.highlander-enabled) .comment-respond {
	border-radius: 10px;
}

body.highlander-enabled .comment-respond {
	padding-top: 3.5em;
}

body.highlander-enabled .comment-respond .comment-reply-title {
	margin-top: 0;
}

body.highlander-enabled .comment .comment-respond,
body.highlander-enabled .comments-area > .comment-respond:first-child {
	padding-top: 0;
}

.comment-form input[type="text"],
.comment-form input[type="password"],
.comment-form input[type="email"],
.comment-form input[type="search"],
.comment-form input[type="url"] {
	width: 100%;
	padding: .21875em .4375em;
}

.comment-form input[type="text"],
.comment-form input[type="password"],
.comment-form input[type="email"],
.comment-form input[type="search"],
.comment-form input[type="url"],
.comment-form textarea {
	font-size: 18px;
	border-color: #ccc;
	box-shadow: 0 1px 5px rgba(0, 0, 0, .06);
}

.comment-form input[type="text"]:focus,
.comment-form input[type="password"]:focus,
.comment-form input[type="email"]:focus,
.comment-form input[type="search"]:focus,
.comment-form input[type="url"]:focus,
.comment-form textarea:focus,
.comment-form input[type="text"]:active,
.comment-form input[type="password"]:active,
.comment-form input[type="email"]:active,
.comment-form input[type="search"]:active,
.comment-form input[type="url"]:active,
.comment-form textarea:active {
	border-color: #0087be;
	box-shadow: 0 1px 5px rgba(0, 0, 0, .07), 0 0 3px rgba(87, 173, 104, .15) inset;
}

.comment-form textarea#comment {
	display: block;
	width: 100%;
	resize: vertical;
}

.comment-form label {
	display: block;
	margin-bottom: .21875em;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen-Sans", "Ubuntu", "Cantarell", "Helvetica Neue", sans-serif;
	font-size: 17px;
	color: #b3b3b1;
}

.comment-form span.required {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen-Sans", "Ubuntu", "Cantarell", "Helvetica Neue", sans-serif;
	font-size: 20px;
	line-height: 0;
	vertical-align: middle;
	color: #0087be;

	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.comment-form .comment-form-comment {
	margin: 0;
}

.comment-form .form-submit {
	margin: 1.75em 0 0;
}

.comment-form .logged-in-as {
	margin: 0;
	font-size: 17px;
}

.comment-form .logged-in-as + .comment-form-comment {
	margin-top: .875em;
}

.comment-form .comment-notes {
	margin: 0;
	font-size: 17px;
}

#cancel-comment-reply-link {
	-webkit-transition: color 160ms ease-in-out;
			transition: color 160ms ease-in-out;
	font-size: 15px;
	font-weight: normal;
	text-indent: 0;
	color: #a1a1a1;
}

#cancel-comment-reply-link:hover {
	text-decoration: none;
	color: #666;
}

p.no-comments {
	margin-top: 3.5em;
}



.widget-area {
	margin-top: 3.5em;
	font-size: 16px;
}


.footer-widgets {
	margin: 1.75em 0 0;
	padding: 1.75em 0 0;
}

.footer-widgets .widget-area {
	width: 100%;
	margin: 0 auto 3.5em;
}

.widget:nth-child(n+2) {
	margin-top: 3.5em;
}

.page-content > .widget {
	margin-top: 3.5em;
}

.widget .widget-title,
.widget .widgettitle {
	margin: 0 0 .4375em;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen-Sans", "Ubuntu", "Cantarell", "Helvetica Neue", sans-serif;
	font-weight: bold;
	margin-bottom: .875em;
	font-size: 17px;
}

.widget .widget-title + p,
.widget .widgettitle + p,
.widget .widget-title + ul,
.widget .widgettitle + ul,
.widget .widget-title + ol,
.widget .widgettitle + ol {
	margin-top: 0;
}

.widget ul,
.widget ol {
	margin-left: 1.75em;
}

.widget ul ul,
.widget ol ul,
.widget ul ol,
.widget ol ol {
	margin-left: .875em;
}

.widget ul ul,
.widget ol ul,
.widget ul ol,
.widget ol ol {
	margin-left: 1.75em;
}

.widget ul li,
.widget ol li {
	margin: .875em 0 0;
	line-height: 1.2;
}

.widget > ul > li:first-child,
.widget > ol > li:first-child {
	margin-top: 0;
}

.widget ul li a:hover {
	text-decoration: underline;
}

.widget select {
	max-width: 100%;
}

.widget {
	margin-top: 0;
}

.widget_search .search-submit {
	display: none;
}

.widget_rss .rss-date,
.widget_rss li > cite {
	display: block;
	font-size: 15px;
	color: #b3b3b1;
}

.widget_goodreads div a img {
	float: right;
	margin-left: .875em;
}
.widget_goodreads > div > div > div {
	clear: both;
	margin-bottom: .875em;
}
.widget_goodreads h2 {
	font-size: 17px;
	margin: 0 0 .875em;
}

#flickr_badge_uber_wrapper {
	border-color: #ddd;
}

.widget_recent_comments img.avatar {
	border-radius: 100px;
}

.widget_calendar #next {
	text-align: right;
}

.widget_authors a {
	color: inherit;
	line-height: 1.5;
}
.widget_authors strong {
	clear: both;
	display: block;
	width: 100%;
}
.widget-area .widget_authors ul {
	margin-left: 0;
}

.widget_authors a:hover {
	text-decoration: none;
	color: #696969;
}

.widget_authors img {
	margin-right: .4375em;
	border-radius: 100%;
}

.widget_authors img,
.widget_authors a {
	display: inline-block;
	vertical-align: middle;
}

.widget_recent_comments table,
.widget_recent_comments td {
	border: none;
}

.widget_recent_comments .widget-title + table.recentcommentsavatar {
	margin-top: -.4375em;
}

.widget_recent_entries {
	font-size: 15px;
	color: #939393;
}

.widget_recent_comments a,
.widget_recent_entries a {
	font-size: 17px;
	font-weight: bold;
	color: #383838;
}

.widget_recent_entries span.post-date {
	display: block;
	font-family: Georgia, "Times New Roman", serif;
	font-style: italic;
}

.widget_recent_entries ul {
	margin-top: 1.75em;
}

.widget_recent_entries ul li {
	margin-top: 1.3125em;
	line-height: 1.4;
}

#wp-calendar tbody td {
	white-space: nowrap;
}

.widget.widget_text ul,
.widget.widget_text ol {
	margin-left: .875em;
	padding-left: .875em;
}

.widget.widget_text ul {
	list-style-type: disc;
}

.widget.widget_text ol {
	list-style-type: decimal;
}

.jetpack_widget_social_icons a:hover {
	opacity: 0.7;
}

.gist table {
	table-layout: inherit;
}



.site {
	position: relative;
}

#secondary {
	margin-top: 3.75em;
}

#secondary:first-child {
	margin-top: 0;
}

.has-header-image .site-content .widget-area {
	padding-top: .21875em;
}

.custom-logo {
	display: block;
	width: auto;
	height: auto;
	max-height: 150px;
	margin: 0 auto .875em;
}

.site-logo-link {
	display: block;
}

.site-logo-link img {
	display: block;
	max-width: 80px;
	height: 80px;
	margin: auto;
	border-radius: 80px;
}

.site-logo-link:focus,
.site-logo-link:active {
	outline: 0;
}

.site-logo-link:focus img,
.site-logo-link:active img {
	outline: thin dotted;
}

.site-header {
	position: relative;
	min-height: 1em;
	margin: 2.625em auto;
	padding: 0 1.75em;
	text-align: center;
	word-wrap: break-word;
}

body:not(.has-header-image) .site-header {
	margin-bottom: 1.75em;
}

body.gravatar-logo-disabled:not(.has-header-image) .site-header {
	margin-bottom: 3.5em;
}

.archive .site-header {
	margin-bottom: 3.5em;
}

.site-title {
	margin: .4375em 0 0;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen-Sans", "Ubuntu", "Cantarell", "Helvetica Neue", sans-serif;
	font-size: 38px;
	font-weight: bold;
	line-height: 1.2;
	color: #333332;
}

.site-title a {
	text-decoration: none;
	color: inherit;
}

.site-branding {
	margin-bottom: 1.75em;
}

.site-description {
	margin: .4375em 0 0;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen-Sans", "Ubuntu", "Cantarell", "Helvetica Neue", sans-serif;
	font-size: 17px;
	font-weight: inherit;
	line-height: 1.4;
	word-wrap: break-word;
	color: #929292;
}

.site-published-date a {
	text-decoration: none;
	color: #929292;
}

.page-header:not(.page-header-light) {
	margin: 1.75em 0 3.5em;
}

.has-header-image .page-header:not(.page-header-light) {
	margin-top: 0;
}

.page-header:not(.page-header-light) h1 {
	margin: 0;
	font-size: 32px;
	line-height: 1.1;
	color: #444;
}

.page-header:not(.page-header-light) .taxonomy-description {
	margin-top: .875em;
	font-family: Georgia, "Times New Roman", serif;
}

.not-found .page-header-light {
	margin-bottom: .875em;
	padding-top: .875em;
}

.not-found .page-header-light .page-title {
	font-size: 24px;
}

.no-results .page-header {
	margin-bottom: 2.625em;
}

.archive .page-title,
.search .page-title,
.archive .archive-title {
	font-size: 24px;
}

.archive.author .archive-title {
	max-width: 80%;
}

.entry-title {
	margin-top: 0;
	margin-bottom: .4375em;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen-Sans", "Ubuntu", "Cantarell", "Helvetica Neue", sans-serif;
	font-size: 32px;
	line-height: 1.3;
	color: #333332;
	-webkit-font-smoothing: antialiased;
}

.entry-title a {
	text-decoration: none;
	color: inherit;
	-webkit-transition: opacity 140ms ease-in-out;
			transition: opacity 140ms ease-in-out;
}

.entry-title a:hover,
.entry-title a:focus,
.entry-title a:active {
	opacity: .8;
}

.entry-meta,
.entry-footer {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen-Sans", "Ubuntu", "Cantarell", "Helvetica Neue", sans-serif;
	font-size: 14px;
	font-weight: 400;
	font-style: normal;
	margin: .4375em 0 3.5em;
	color: #b3b3b1;
}

.entry-meta > span {
	top: 3px;
	display: inline-block;
	margin-right: 1.3125em;
	vertical-align: middle;
}

.entry-meta > span:last-child {
	margin-right: 0;
}

.entry-meta > span:before {
	position: relative;
	top: -1px;
	display: inline-block;
	font-family: "Genericons";
	font-size: 16px;
	line-height: 1;
	vertical-align: middle;
	transform: scale(1.25);
	-webkit-transform: scale(1.25);
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.entry-meta .cat-links:before {
	content: "\f301";
}

.entry-meta .edit-link:before {
	content: "\f411";
	margin-right: 4px;
}

.entry-meta .comments-link:before {
	content: "\f300";
	margin-right: 5px;
}
.entry-meta .byline:before {
	content: "\f304";
}

.entry-meta .published-on:before {
	content: "\f303";
}

.entry-meta .word-count:before {
	content: "\f100";
	margin-right: 4px;
	transform: scale(1.5);
	-webkit-transform: scale(1.5);
}

.entry-meta a,
.entry-footer a {
	-webkit-transition: color 140ms ease-in-out;
			transition: color 140ms ease-in-out;
	line-height: inherit;
	text-decoration: none;
	color: #b3b3b1;
}

.entry-meta a:hover {
	color: #00aadc;
	text-decoration: underline;
}

.post-tags a:hover {
	color: #747471;
}

.content-wrapper {
	box-sizing: content-box;
	max-width: 740px;
	margin: 0 auto;
	padding: 3.5em 1.75em 0;
}

.has-header-image #hero-header + .content-wrapper {
	margin-top: 3.5em;
	padding-top: 0;
}

.has-header-image .site-header {
	padding: 0;
}

.site-content {
	margin: 0 auto;
}

.entry-content,
.widget-area .widget,
.comment {
	word-wrap: break-word;
}

.entry-title {
	word-wrap: break-word;
}

.site-footer {
	clear: both;
	padding: 2.625em 0 1.75em;
	word-wrap: break-word;
}

.site-info a:hover {
	color: #0087be;
}

.site-info {
	padding: 5.25em 0 1.75em;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen-Sans", "Ubuntu", "Cantarell", "Helvetica Neue", sans-serif;
	font-size: 14px;
	text-align: center;
	color: #8d8d8a;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.widget-area ul,
.widget-area ol {
	margin: 0;
	padding: 0;
	list-style: none;
}

.site-main > .hentry:nth-child(n+2),
.site .infinite-wrap > .hentry:nth-child(n+2) {
	margin-top: 1.75em;
	padding-top: 1.75em;
	border-top: solid 1px #ddd;
}

.infinite-wrap {
	margin-top: 1.75em;
	padding-top: 1.75em;
	border-top: solid 1px #ddd;
}

.site-main .page-header + .hentry {
	margin-top: 0;
	padding-top: 0;
	border-top: none;
}

.post-image-link {
	text-align: center;
}

.post-image-link:not(:first-child) {
	margin-top: .875em;
}

.post-image-link + .entry-header {
	margin-top: .875em;
}

.post-image-link a {
	position: relative;
}

.post-image-link .sticky-label {
	border-radius: 2px 0 0 0;
}

body:not(.single) .hentry.empty-content .post-image-link {
	margin-bottom: .4375em;
}

.sticky-label {
	position: absolute;
	z-index: 10;
	top: 0;
	right: auto;
	left: 0;
	display: block;
	padding: .109375em .875em;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen-Sans", "Ubuntu", "Cantarell", "Helvetica Neue", sans-serif;
	font-size: 15px;
	font-weight: bold;
	color: white;
	background: #4d4d4b;

	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.post-image-link a {
	display: inline-block;
	max-width: 100%;
}

.post-image-link img {
	display: block;
}

.post-image-link img {
	max-width: 100%;
	height: auto;
	margin-bottom: -.4375em;
	-webkit-transition: opacity 200ms ease;
			transition: opacity 200ms ease;
}

.post-image-link a:focus {
	outline: 0;
}

.post-image-link a:hover img {
	opacity: .9;
}

.post-image-link a:focus img {
	outline: thin dotted;
}

.hentry {
	position: relative;
}

.hentry > .sticky-label {
	position: relative;
	float: left;
	margin-bottom: .875em;
}

.hentry > .sticky-label + .entry-content {
	clear: left;
}

time.published + .updated {
	display: none;
}

.post-tags {
	margin: 3.5em 0 1.75em;
	padding: 0;
	font-weight: 700;
}

.post-tags > li {
	margin: 0;
	padding: 0;
	list-style: none;
	list-style-position: inside;
}

.post-tags > li > a:hover {
	text-decoration: none;
}

.post-tags li {
	float: left;
	font-size: 16px;
	font-weight: 400;
}

.post-tags li:first-child {
	font-weight: 700;
	color: #515151;
}

.post-tags li:first-child:after {
	margin-right: .875em;
	margin-left: 0;
	content: ":";
}

.post-tags li:nth-child(n+2):not(:last-child):after {
	margin-right: .4375em;
	margin-left: 0;
	content: ", ";
}

.page-links {
	clear: both;
	margin: 0 0 .875em;
	font-family: Georgia, "Times New Roman", serif;
	line-height: 1.2;
	text-align: left;
}

.page-links > span:first-child,
.page-links > a:first-child {
	margin-right: 0;
	margin-left: .4375em;
}

.page-links > span {
	display: inline-block;
	min-width: 1em;
	padding: .21875em .21875em .109375em;
	text-align: center;
	background: #f7f7f7;
}

.page-links span {
	display: inline-block;
}

.page-links a {
	display: inline-block;
	min-width: 1em;
	margin: 0 -2px;
	padding: .21875em .21875em .109375em;
	text-align: center;
	text-decoration: none;
	border-bottom: solid 3px transparent;
}

.page-links a:hover {
	color: #2d5a36;
	border-bottom-color: #e0e0e0;
}

.single .post-edit-link {
	display: block;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen-Sans", "Ubuntu", "Cantarell", "Helvetica Neue", sans-serif;
	font-size: 15px;
}

.single .entry-header + .post-edit-link {
	margin-top: 1.75em;
}

.single .post-edit-link a {
	display: inline-block;
	padding: .4375em .875em;
	-webkit-transition: color 140ms ease-in-out;
		transition: color 140ms ease-in-out;
	line-height: 1;
	text-decoration: none;
	color: #92928f;
	border-radius: 3px;
	background: #f5f5f5;
}

.single .post-edit-link a:before {
	display: inline-block;
	margin: -.109375em .21875em 0 -.21875em;
	content: "\f411";
	font-family: "Genericons";
	vertical-align: middle;

	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.single .post-edit-link a:hover,
.single .post-edit-link a:focus,
.single .post-edit-link a:active {
	color: #0087be;
}



.has-header-image #hero-header {
	position: relative;
	padding: 5vh 1.75em;
}

.has-header-image #hero-header:before {
	position: absolute;
	z-index: 1;
	top: 0;
	left: 0;
	display: block;
	width: 100%;
	height: 100%;
	content: "\020";
	background: -webkit-linear-gradient(rgba(0, 0, 0, .15) 50%, rgba(0, 0, 0, .45));
	background:				 linear-gradient(rgba(0, 0, 0, .15) 50%, rgba(0, 0, 0, .45));
	box-shadow: 0 -1px 8px rgba(0, 0, 0, .2) inset;
}

.has-header-image #hero-header .inner {
	position: relative;
	z-index: 2;
	max-width: 740px;
	margin: 0 auto;
	padding-top: .875em;
	text-align: center;
	word-wrap: break-word;
	color: white;
}

.has-header-image #hero-header .site-title {
	color: white;
}

.has-header-image #hero-header .site-description {
	color: rgba(255, 255, 255, .84);
}

.has-header-image #hero-header .site-logo-link img {
	-webkit-transition: box-shadow 200ms ease;
		transition: box-shadow 200ms ease;
	outline: 0;
}

.has-header-image #hero-header .site-logo-link:focus img,
.has-header-image #hero-header .site-logo-link:active img {
	box-shadow: 0 0 0 7px rgba(255, 255, 255, .22);
}

.has-header-image #hero-header .jetpack-social-navigation ul.menu li a {
	padding: 0 0;
	color: rgba(255, 255, 255, .85);
}

.has-header-image #hero-header .jetpack-social-navigation ul.menu li a:hover {
	color: white;
}



.entry-author-wrapper {
	position: relative;
	margin: 1.75em 0 0;
	padding: 1.75em 0 0;
}

.entry-author .author-avatar {
	position: relative;
	z-index: 2;
	display: block;
	float: right;
	margin-left: .875em;
	width: 80px;
}

.entry-author .author-avatar img {
	position: relative;
	display: block;
	float: right;
	width: 80px;
	height: auto;
	border-radius: 80px;
}
.entry-author .author-title {
	clear: none;
	margin: 0;
	font-size: 19px;
	font-weight: 700;
	line-height: 1.4;
	color: #333332;
}

.entry-author .author-bio,
.entry-author .author-heading {
	position: relative;
	z-index: 1;
	display: block;
	clear: none;
	box-sizing: border-box;
}

.entry-author .author-bio {
	margin-top: 0;
	font-size: 15px;
	font-style: italic;
	color: #929292;
}

.site-posted-on {
	position: relative;
	display: none;
	margin-left: 100px;
}

.site-posted-on strong {
	display: block;
	clear: right;
	margin: 1.75em 0 0;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen-Sans", "Ubuntu", "Cantarell", "Helvetica Neue", sans-serif;
	font-size: 15px;
	font-weight: 700;
	font-style: normal;
	line-height: 1.4;
	color: #333332;
}

.site-posted-on time {
	display: block;
	margin: 0;
	font-family: Georgia, "Times New Roman", serif;
	font-size: 15px;
	font-weight: inherit;
	font-style: normal;
	line-height: 1.5;
	color: #929292;
	margin-top: 0.7em;
	max-width: 120px;
}

.site-posted-on time.updated:not(.published) {
	display: none;
}

.site-posted-on strong {
	margin-top: 0;
}

.site-posted-on:before {
	display: none;
}




.infinite-scroll .posts-navigation,
.infinite-scroll.neverending .site-footer {
	
	display: none;
}


.infinity-end.neverending .site-footer {
	display: block;
}

.infinite-loader {
	margin-top: 2.05em;
}

#content #infinite-handle {
	margin-top: 1.75em;
}

#content #infinite-handle span {
	padding: 0;
	background: none;
}

#content #infinite-handle span button {
	line-height: 1;
	outline: 0;
}

#infinite-footer .container {
	padding: 0;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen-Sans", "Ubuntu", "Cantarell", "Helvetica Neue", sans-serif;
	font-size: 12px;
	color: #a5a5a5;
	border: 0;
}
#infinite-footer .blog-info a,
#infinite-footer .blog-credits,
#infinite-footer .blog-credits a {
	font-size: 12px;
	color: #a5a5a5;
}
#infinite-footer .blog-info a:hover,
#infinite-footer .blog-credits a:hover {
	text-decoration: none;
}



.site-content .contact-form input[type="radio"],
.site-content .contact-form input[type="checkbox"] {
  margin-bottom: 6px;
  margin-right: .3em;
  vertical-align: middle;
}
.site-content .contact-form label.checkbox,
.site-content .contact-form label.checkbox-multiple,
.site-content .contact-form label.radio {
  font-weight: normal;
  margin-bottom: .21875em;
}
.site-content .contact-form label.checkbox,
.site-content .contact-form > div {
  margin-bottom: .875em;
}
.site-content .contact-form textarea,
.site-content .contact-form input[type='text'],
.site-content .contact-form input[type='email'],
.site-content .contact-form input[type='url'],
.site-content .contact-form select {
  margin-bottom: .4375em;
}
.site-content .contact-form .grunion-field-label {
	margin-bottom: .21875em;
}



.jetpack-social-navigation ul,
.widget_wpcom_social_media_icons_widget ul {
	margin: 0;
	padding: 0;
	list-style: none;
}

.jetpack-social-navigation ul li,
.widget_wpcom_social_media_icons_widget ul li {
	position: relative;
	display: inline-block;
	line-height: 1;
	margin: 0 .209875em;
}

.jetpack-social-navigation li a,
.widget_wpcom_social_media_icons_widget li a {
	display: block;
	padding: 0 3px;
	-webkit-transition: color ease .3s, opacity ease .3s;
			transition: color ease .3s, opacity ease .3s;
	font-size: 28px;
	color: #a4a4a4;
	text-decoration: none;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.jetpack-social-navigation li a:hover,
.widget.widget_wpcom_social_media_icons_widget ul li a:hover,
.jetpack-social-navigation li a:focus,
.widget.widget_wpcom_social_media_icons_widget ul li a:focus,
.jetpack-social-navigation li a:active,
.widget.widget_wpcom_social_media_icons_widget ul li a:active {
	color: #5e6166;
	text-decoration: none;
}

.widget.widget_wpcom_social_media_icons_widget li {
	margin: .21875em .4375em;
}

.widget.widget_wpcom_social_media_icons_widget li a {
	font-size: 32px;
}





@media screen and (min-width: 32.375em) {
	h1 {
		font-size: 47px;
	}

	h2 {
		font-size: 32px;
	}

	h3 {
		font-size: 24px;
	}

	h4 {
		font-size: 19px;
	}

	h5 {
		font-size: 15px;
	}

	h6 {
		font-size: 14px;
	}

	h5,
	h6 {
		line-height: 1.3;
	}

	.entry-content,
	.entry-summary {
		font-size: 19px;
	}

	.entry-title {
		font-size: 47px;
	}

	.page-header:not(.page-header-light) h1 {
		font-size: 47px;
		line-height: 1;
	}

	.has-header-image #hero-header .inner {
		padding-top: 0;
	}

	.menu-toggle {
		display: none;
	}

	.main-navigation {
		text-align: center;
	}

	.has-header-image .main-navigation {
		margin-top: .875em;
	}

	.main-navigation ul {
		display: block;
	}
	.main-navigation a {
		display: block;
	}
	.main-navigation li {
		padding: 0;
		width: auto;
		border: 0;
		margin: .209875em .4375em;
	}
	.main-navigation ul a,
	.main-navigation ul ul a,
	.main-navigation ul ul ul a,
	.main-navigation ul ul ul ul a,
	.main-navigation ul ul ul ul ul a,
	.main-navigation ul ul ul ul ul ul a {
		padding: .4835em .209375em;
	}

	.main-navigation ul ul {
		background: #0087be;
		position: absolute;
		z-index: 99999;
		top: 2em;
		left: -999em;
		margin-left: -.4em;
		float: left;
		text-align: left;
	}
	.main-navigation ul ul ul {
		top: -.209875em;
		left: -999em;
		margin: 0;
	}
	.main-navigation ul ul li:hover > ul,
	.main-navigation ul ul li.focus > ul {
		left: 100%;
	}
	.main-navigation ul ul a {
		color: white;
		width: 200px;
		-webkit-transition: opacity 140ms ease-in-out;
				transition: opacity 140ms ease-in-out;
		-webkit-font-smoothing: antialiased;
	}
	.main-navigation ul li:hover > ul,
	.main-navigation ul li.focus > ul {
		left: auto;
	}

	.main-navigation ul ul li:hover > a,
	.main-navigation ul ul li.focus > a,
	.main-navigation > div > ul > li.current-menu-item > a,
	.main-navigation > div > ul > li.current_page_item > a,
	.main-navigation > div > ul ul li.current-menu-item > a,
	.main-navigation > div > ul ul li.current_page_item > a {
		opacity: 0.7;
	}

	.main-navigation li.menu-item-has-children > a:after,
	.main-navigation li.page_item_has_children > a:after {
		position: relative;
		display: inline-block;
		margin-left: 6px;
		content: "\203A";
		-webkit-transform: rotate(90deg);
		transform: rotate(90deg);
		font-family: inherit;
		font-size: 20px;
		font-weight: bold;
		font-style: normal;
		font-variant: normal;
		line-height: 20px;
		text-align: center;
		vertical-align: baseline;
		text-decoration: inherit;
		text-transform: none;
		-webkit-font-smoothing: antialiased;
		speak: none;
	}
	.main-navigation ul ul li.menu-item-has-children > a:after,
	.main-navigation ul ul li.page_item_has_children > a:after {
		-webkit-transform: rotate(0deg);
		transform: rotate(0deg);
	}

	.main-navigation > div > ul > li.current-menu-item > ul > li a,
	.main-navigation > div > ul > li.current_page_item > ul > li a {
		color: #fff;
	}

	.post-navigation .nav-links .nav-previous {
		clear: none;
		margin: 0 0 0 -.21875em;
		width: auto;
	}

	blockquote.aligncenter {
		padding: .875em 3.5em;
	}
	blockquote.alignright,
	blockquote.alignleft {
		max-width: 34%;
	}
	blockquote.alignleft {
		margin-right: 1.75em;
	}
	blockquote.alignright {
		margin-left: 1.75em;
	}
}



@media screen and (min-width: 48.9375em) {

	.custom-logo {
		max-width: 750px;
	}
	.comment-form input[type="text"],
	.comment-form input[type="password"],
	.comment-form input[type="email"],
	.comment-form input[type="search"],
	.comment-form input[type="url"] {
		width: 70%;
	}
	.has-header-image #hero-header {
		padding: 10vh 3.5em;
	}
	.comments-area li.comment {
		padding: 1.75em 0 0 1.75em;
	}
	.comments-area li.comment .comment-respond {
		margin-left: 0;
	}
	.comments-area li.comment .comment {
		margin-left: 0;
	}
	.comments-area li.comment .comment-body {
		padding: 0;
	}
	.bypostauthor {
		box-shadow: 2px 2px 3px rgba(87, 173, 104, .13) inset;
	}
	.entry-author {
		min-height: 100px;
	}
	.entry-author .author-bio,
	.entry-author .author-heading {
		max-width: 70%;
	}
	.entry-author .author-title {
		font-size: 24px;
		line-height: 1.55;
		margin-top: -.21875em;
	}
	.entry-author .author-avatar,
	.entry-author .author-avatar img {
		float: left;
		margin: 0;
	}
	.entry-author .author-bio,
	.entry-author .author-heading {
		margin-left: 100px;
	}
	.site-posted-on {
		position: absolute;
		top: 0;
		right: 0;
		left: auto;
		display: block;
		margin-top: 1.75em;
		margin-left: 0;
		text-align: right;
	}

	.footer-widgets .widget-area {
		float: left;
	}
	.footer-widgets .widget-area:nth-child(1):nth-last-child(2),
	.footer-widgets .widget-area:nth-child(2):nth-last-child(1) {
		width: 48%;
		margin-right: 4%;
	}
	.footer-widgets .widget-area:nth-child(1):nth-last-child(2):last-of-type,
	.footer-widgets .widget-area:nth-child(2):nth-last-child(1):last-of-type {
		margin-right: 0;
	}
	.footer-widgets .widget-area:nth-child(1):nth-last-child(3),
	.footer-widgets .widget-area:nth-child(2):nth-last-child(2),
	.footer-widgets .widget-area:nth-child(3):nth-last-child(1) {
		width: 30%;
		margin-right: 5%;
	}
	.footer-widgets .widget-area:nth-child(1):nth-last-child(3):last-of-type,
	.footer-widgets .widget-area:nth-child(2):nth-last-child(2):last-of-type,
	.footer-widgets .widget-area:nth-child(3):nth-last-child(1):last-of-type {
		margin-right: 0;
	}
}



@media screen and (min-width: 62em) {
	.has-header-image #hero-header {
		padding: 16.6vh 3.5em;
	}
	.has-header-image #hero-header .inner {
		max-width: 1080px;
	}
	#infinite-footer .container {
		max-width: 740px;
	}
	.has-sidebar #infinite-footer .container {
		max-width: 1080px;
	}
	.content-wrapper {
		box-sizing: border-box;
		max-width: 1080px;
		margin: 2.625em auto 0;
		padding: 0 1.75em;
	}
	body:not(.has-header-image) .site-header {
		margin-bottom: 0;
	}
	.content-area {
		max-width: 740px;
		margin: 0 auto;
	}
	.has-sidebar .content-area {
		float: left;
		width: 100%;
		max-width: 100%;
		margin: 0 -26% 0 0;
	}
	.has-sidebar .site-main {
		margin: 0 26% 0 0;
		max-width: 740px;
	}
	.has-sidebar .site-content .widget-area {
		float: right;
		width: 22%;
	}

	body:not(.has-header-image) .site-main > article:first-child {
		padding-top: 1.75em;
	}
}



@media screen and (min-width: 68.75em) {
	
	body:not(.has-sidebar) .tiled-gallery-wrapper,
	body:not(.has-sidebar) .jetpack-video-wrapper,
	body:not(.has-sidebar) img.size-big,
	body:not(.has-sidebar) .wp-caption.size-big img {
		float: none;
		margin-left: -180px;
		max-width: 1100px;
		width: 1100px !important;
	}

	body:not(.has-sidebar) .jetpack-slideshow img.size-big {
		margin-left: auto;
		max-width: 100%;
		width: auto !important;
	}
}



@media screen and (min-width: 86.25em) {
	.content-wrapper {
		max-width: 1380px;
	}

	.has-header-image #hero-header .inner {
		max-width: 1380px;
	}
}


.wp-block-image {
	margin: 0 0 1em;
}





#page {
	overflow-x: hidden; 
}

.alignfull,
.alignwide {
	clear: both;
}

@media (min-width: 1140px) {
	body:not(.has-sidebar) .alignwide,
	body:not(.has-sidebar) .wp-block-group.alignfull .alignwide {
		margin-left: -25%;
		margin-right: -25%;
		max-width: 1000%;
		width: auto;
	}
}

body:not(.has-sidebar) .alignfull,
body:not(.has-sidebar) .wp-block-group.alignfull .alignfull {
	margin-left: calc(50% - 50vw);
	margin-right: calc(50% - 50vw);
	max-width: 1000%;
	width: auto;
}


body:not(.has-sidebar) .wp-block-columns.alignfull,
body:not(.has-sidebar) .wp-block-audio.alignfull,
body:not(.has-sidebar) .wp-block-table.alignfull,
body:not(.has-sidebar) .wp-block-latest-comments.alignfull,
body:not(.has-sidebar) .wp-block-categories.alignfull,
body:not(.has-sidebar) .wp-block-latest-posts.alignfull {
	margin-left: calc(50% - 48vw);
	margin-right: calc(50% - 48vw);
}

.wp-block-embed.is-type-video iframe {
	max-height: 100%;
}

.wp-block-embed.is-type-video.alignfull iframe {
	width: 100% !important;
	height: 100% !important;
}

body:not(.has-sidebar) *[class^="wp-block"] .alignwide,
body:not(.has-sidebar) *[class^="wp-block"] .alignfull {
	max-width: inherit;
	margin-left: inherit;
	margin-right: inherit;
}





[class^="wp-block-"] figcaption {
	font-size: 14.25px;
	font-weight: 400;
	font-style: italic;
	color: inherit;

}





p.has-drop-cap:not(:focus)::first-letter {
	font-size: 5em;
	margin-top: 0.15em;
}



.wp-block-image.is-style-default img[class*="wp-image-"] {
	border-radius: 3px;
}

.wp-block-image.is-style-rounded img[class*="wp-image-"] {
	border-radius: 9999px;
}



.wp-block-gallery {
	margin: 0 0 0.875em;
}



.wp-block-quote.is-large cite,
.wp-block-quote.is-style-large cite {
	text-align: inherit;
}

.rtl .wp-block-quote,
.wp-block-quote[style*="text-align:right"] {
	margin-left: .875em;
	margin-right: -1.9em;
	padding-left: 0;
	padding-right: 1.75em;
	border-width: 0 3px 0 0;
}

.rtl .wp-block-quote[style*="text-align:left"] {
	margin-left: -1.9em;
	margin-right: .875em;
	padding-left: 1.75em;
	padding-right: 0;
	border-width: 0 0 0 3px;
}

.wp-block-quote.is-large,
.wp-block-quote.is-style-large,
.wp-block-quote[style*="text-align:center"] {
	border: 0;
	margin-left: 0;
	margin-right: 0;
	padding: 0;
}



.wp-block-audio {
	margin-bottom: 0.875em;
}

.wp-block-audio audio {
	display: block;
	width: 100%;
}


.wp-block-image .alignleft,
.wp-block-image .alignright {
	margin-top: 30px;
}



.wp-block-cover.aligncenter,
.wp-block-cover-image.aligncenter,
.wp-block-cover.alignleft,
.wp-block-cover-image.alignleft,
.wp-block-cover-image.alignright {
	display: flex;
}

.wp-block-cover-image .wp-block-cover-image-text,
.wp-block-cover-image .wp-block-cover-text,
.wp-block-cover-image h2,
.wp-block-cover .wp-block-cover-image-text,
.wp-block-cover .wp-block-cover-text, .wp-block-cover h2 {
	font-size: 1.5em;
	margin-bottom: inherit;
}

.wp-block-cover.alignfull .wp-block-cover__inner-container {
	max-width: 740px;
	margin: 0 auto;
}



.wp-block-file .wp-block-file__button {
	background: #0087be;
	border: solid 1px transparent;
	border-radius: 3px;
	box-sizing: border-box;
	color: #fff;
	cursor: pointer;
	-webkit-transition: background 120ms ease-in-out, box-shadow 120ms ease-in-out;
			transition: background 120ms ease-in-out, box-shadow 120ms ease-in-out;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen-Sans", "Ubuntu", "Cantarell", "Helvetica Neue", sans-serif;
	font-size: 16px;
	font-weight: 400;
	font-style: normal;
	padding: .4375em 1.25em;
	text-decoration: none;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.wp-block-file .wp-block-file__button:focus,
.wp-block-file .wp-block-file__button:active {
	outline: 0;
	background: #767676;
	box-shadow: inset 0 2px 2px rgba(0, 0, 0, .25), 0 0 0 6px rgba(0, 0, 0, .08);
}

.wp-block-file .wp-block-file__button:hover {
	text-decoration: none;
	color: #fff;
	background: #767676;
}

.rtl .wp-block-file * + .wp-block-file__button {
	margin-left: .75em;
	margin-right: 0;
}





.wp-block-verse {
	background: none;
	font-family: inherit;
	font-style: italic;
	padding: 0;
}







.wp-block-pullquote {
	border: #0087be solid;
	border-width: 3px 0;
	padding: 0.5em;
}

.wp-block-pullquote blockquote {
	margin-left: 0;
	margin-right: 0;
}

.wp-block-pullquote.alignleft,
.wp-block-pullquote.alignright {
	margin-top: 0.5em;
	margin-bottom: 1.0em;
}

.wp-block-pullquote blockquote {
	border: 0;
	padding: 0;
}

.wp-block-pullquote cite {
	font-size: inherit;
	font-style: italic;
	text-transform: none;
}



@media (min-width: 1140px) {
	body:not(.has-sidebar) .wp-block-table.alignwide {
		width: 1100px;
	}
}

body:not(.has-sidebar) .wp-block-table.alignfull {
	width: 96vw;
}





.wp-block-button .wp-block-button__link {
	box-sizing: border-box;
	cursor: pointer;
	-webkit-transition: background 120ms ease-in-out, box-shadow 120ms ease-in-out;
			transition: background 120ms ease-in-out, box-shadow 120ms ease-in-out;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen-Sans", "Ubuntu", "Cantarell", "Helvetica Neue", sans-serif;
	font-size: 16px;
	font-weight: 400;
	font-style: normal;
	padding: .4375em 1.25em;
	text-decoration: none;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.wp-block-button__link,
.wp-block-button__link:visited {
	background: #0087be;
	color: #fff;
}

.is-style-outline .wp-block-button__link {
	border-color: currentColor;
}

.is-style-outline .wp-block-button__link:not(.has-text-color),
.is-style-outline .wp-block-button__link:not(.has-text-color):visited{
	color: #0087be;
}

.entry-content .wp-block-button .wp-block-button__link:focus,
.entry-content .wp-block-button .wp-block-button__link:active {
	background: #767676;
	box-shadow: inset 0 2px 2px rgba(0, 0, 0, .25), 0 0 0 6px rgba(0, 0, 0, .08);
}

.entry-content .wp-block-button .wp-block-button__link:hover {
	text-decoration: none;
	color: #fff;
	background: #767676;
}



.wp-block-group.alignfull .wp-block-group__inner-container {
	max-width: 740px;
	margin: 0 auto;
}



.wp-block-separator {
	border: 0;
}

.wp-block-separator:not(.is-style-wide):not(.is-style-dots) {
	margin-left: auto;
	margin-right: auto;
	max-width: 100px;
}



.wp-block-media-text {
	margin-bottom: 0.875em;
}





.wp-block-categories.aligncenter,
.wp-block-categories.aligncenter ul,
.wp-block-archives.aligncenter,
.wp-block-latest-posts.aligncenter,
.wp-block-latest-comments.aligncenter {
	list-style-position: inside;
	text-align: center;
}



.wp-block-latest-comments {
	margin-left: 0;
	margin-right: 0;
}

.wp-block-latest-comments__comment,
.wp-block-latest-comments__comment-date,
.wp-block-latest-comments__comment-excerpt p {
	font-size: 17px;
}

.wp-block-latest-comments__comment-meta a {
	color: #000;
	font-weight: bold;
}

.wp-block-latest-comments__comment-date {
	color: #b3b3b1;
	font-size: 14px;
}



.wp-block-latest-posts.is-grid {
	margin-left: 0;
	margin-right: 0;
}



.has-medium-blue-color,
.has-medium-blue-color:hover,
.has-medium-blue-color:active,
.has-medium-blue-color:focus,
.has-medium-blue-color:visited {
	color: #0087be;
}

.has-medium-blue-background-color,
.has-medium-blue-background-color:hover,
.has-medium-blue-background-color:active,
.has-medium-blue-background-color:focus,
.has-medium-blue-background-color:visited {
	background-color: #0087be;
}

.has-bright-blue-color,
.has-bright-blue-color:hover,
.has-bright-blue-color:focus,
.has-bright-blue-color:active,
.has-bright-blue-color:visited {
	color: #00aadc;
}

.has-bright-blue-background-color,
.has-bright-blue-background-color:hover,
.has-bright-blue-background-color:focus,
.has-bright-blue-background-color:active,
.has-bright-blue-background-color:visited {
	background-color: #00aadc;
}

.has-dark-gray-color,
.has-dark-gray-color:hover,
.has-dark-gray-color:focus,
.has-dark-gray-color:active,
.has-dark-gray-color:visited {
	color: #4d4d4b;
}

.has-dark-gray-background-color,
.has-dark-gray-background-color:hover,
.has-dark-gray-background-color:focus,
.has-dark-gray-background-color:active,
.has-dark-gray-background-color:visited {
	background-color: #4d4d4b;
}

.has-light-gray-color,
.has-light-gray-color:hover,
.has-light-gray-color:focus,
.has-light-gray-color:active,
.has-light-gray-color:visited {
	color: #b3b3b1;
}

.has-light-gray-background-color,
.has-light-gray-background-color:hover,
.has-light-gray-background-color:focus,
.has-light-gray-background-color:active,
.has-light-gray-background-color:visited {
	background-color: #b3b3b1;
}

.has-white-color,
.has-white-color:hover,
.has-white-color:focus,
.has-white-color:active,
.has-white-color:visited {
	color: #fff;
}

.has-white-background-color,
.has-white-background-color:hover,
.has-white-background-color:focus,
.has-white-background-color:active,
.has-white-background-color:visited {
	background-color: #fff;
}



li.a8c-posts-list__item {
	margin-top: 0;
}

li.a8c-posts-list__item article {
	margin-top: 1.75em;
	padding-top: 1.75em;
	border-top: solid 1px #ddd;
}

h2.a8c-posts-list-item__title {
	font-size: 32px;
	margin-top: 0;
	margin-bottom: 0;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen-Sans", "Ubuntu", "Cantarell", "Helvetica Neue", sans-serif;
	line-height: 1.3;
	color: #333332;
	-webkit-font-smoothing: antialiased;
}

@media screen and (min-width: 32.375em) {
	h2.a8c-posts-list-item__title {
		font-size: 47px;
	}	
}

h2.a8c-posts-list-item__title a {
	text-decoration: none;
	color: inherit;
	-webkit-transition: opacity 140ms ease-in-out;
			transition: opacity 140ms ease-in-out;
}

h2.a8c-posts-list-item__title a:hover,
h2.a8c-posts-list-item__title a:focus,
h2.a8c-posts-list-item__title a:active {
	opacity: .8;
}

div.a8c-posts-list-item__meta {
	margin-top: .875em;
	margin-bottom: .875em;
	color: #b3b3b1;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen-Sans", "Ubuntu", "Cantarell", "Helvetica Neue", sans-serif;
	font-size: 14px;
    font-weight: 400;
    font-style: normal;
}

span.a8c-posts-list-item__datetime:before {
	content: "\f303";
	position: relative;
	top: -1px;
	display: inline-block;
	font-family: "Genericons";
	font-size: 16px;
	line-height: 1;
	vertical-align: middle;
	transform: scale(1.25);
	-webkit-transform: scale(1.25);
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	padding-right: 5px;
}

span.a8c-posts-list-item__author a {
	color: #b3b3b1;
}

div.a8c-posts-list-item__excerpt {
	margin-top: .875em;
	font-family: Georgia, "Times New Roman", serif;
	font-size: 17px;
}

@media screen and (min-width: 32.375em) {
	div.a8c-posts-list-item__excerpt {
		font-size: 19px;
	}
}

figure.a8c-posts-list-item__post-thumbnail {
	margin-bottom: 1.75em;
}

a.a8c-posts-list__view-all {
	display: inline-block;
	box-sizing: border-box;
	padding: .4375em 1.25em;
	cursor: pointer;
	-webkit-transition: background 120ms ease-in-out, box-shadow 120ms ease-in-out;
			transition: background 120ms ease-in-out, box-shadow 120ms ease-in-out;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen-Sans", "Ubuntu", "Cantarell", "Helvetica Neue", sans-serif;
	font-size: 16px;
	font-weight: 400;
	font-style: normal;
	text-decoration: none;
	color: #fff;
	border: solid 1px transparent;
	border-radius: 3px;
	background: #0087be;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	margin: .875em 0;
}

a.a8c-posts-list__view-all:focus,
a.a8c-posts-list__view-all:active {
	outline: 0;
	background: #767676;
	box-shadow: inset 0 2px 2px rgba(0, 0, 0, .25), 0 0 0 6px rgba(0, 0, 0, .08);
}

a.a8c-posts-list__view-all:hover {
	text-decoration: none;
	color: #fff;
	background: #767676;
}

a.a8c-posts-list__view-all:visited {
	color: white;
}


#wpstats {
	display: none;
}

div#jp-relatedposts h3.jp-relatedposts-headline {
	font-size: 19px;
	margin: 0 0 .875em;
}
div#jp-relatedposts h3.jp-relatedposts-headline em:before {
	display: none;
}

div#jp-relatedposts div.jp-relatedposts-items-visual h4.jp-relatedposts-post-title {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen-Sans", "Ubuntu", "Cantarell", "Helvetica Neue", sans-serif;
	font-style: normal;
	line-height: 1.5;
	margin: .4375em 0;
}
div#jp-relatedposts div.jp-relatedposts-items .jp-relatedposts-post .jp-relatedposts-post-title a {
	font-weight: bold;
	font-size: 17px;
}
div#jp-relatedposts div.jp-relatedposts-items .jp-relatedposts-post .jp-relatedposts-post-title a:hover,
div#jp-relatedposts div.jp-relatedposts-items div.jp-relatedposts-post:hover .jp-relatedposts-post-title a {
	text-decoration: none;
}
div#jp-relatedposts div.jp-relatedposts-items p {
	font-size: 15px;
	line-height: 1.75;
}
div#jp-relatedposts div.jp-relatedposts-items-visual h4.jp-relatedposts-post-title,
div#jp-relatedposts div.jp-relatedposts-items p {
	margin-bottom: .4375em;
}

div#jp-relatedposts div.jp-relatedposts-items p.jp-relatedposts-post-context {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen-Sans", "Ubuntu", "Cantarell", "Helvetica Neue", sans-serif;
	font-size: 14px;
}

.mc-modal {
	z-index: 10001 !important;
}
.mc-banner {
	z-index: 10002 !important;
}




.cdm-icon__site_logo {
	display: none;
}




.cdm-icon--header-image {
	left: auto !important;
	right: 50px !important;
	z-index: 9999;
}


.reblogger-note img.avatar {
	float: left;
	padding: 0;
	border: 0;
}

.reblogger-note-content {
	margin: 0 0 20px;
}

.wpcom-reblog-snapshot .reblog-from img {
	margin: 0 .75em 0 0;
	padding: 0;
	border: 0;
}

.wpcom-reblog-snapshot .reblogger-note img.avatar {
	float: left;
	padding: 0;
	border: 0;
	margin: 0 .5em 0 0;
}

.wpcom-reblog-snapshot {
	margin-bottom: 1em;
}

.wpcom-reblog-snapshot p.reblog-from {
	margin: 0 0 1em 0;
}

.wpcom-reblog-snapshot p.reblogger-headline {
	line-height: 32px;
	margin: 0 0 1em 0;
}

.wpcom-reblog-snapshot .reblogged-content {
	margin: 0 0 1em 0;
}


.reblog-post .wpcom-enhanced-excerpt-content {
	border-left: 3px solid #eee;
	padding-left: 15px;
}

.reblog-post ul.thumb-list {
	display: block;
	list-style: none;
	margin: 2px 0;
	padding: 0;
	clear: both;
}

.reblog-post ul.thumb-list li {
	display: inline;
	margin: 0;
	padding: 0 1px;
	border: 0;
}

.reblog-post ul.thumb-list li a {
	margin: 0;
	padding: 0;
	border: 0;
}

.reblog-post ul.thumb-list li img {
	margin: 0;
	padding: 0;
	border: 0;
}

.reblog-post .wpcom-enhanced-excerpt {
	clear: both;
}

.reblog-post .wpcom-enhanced-excerpt address,
.reblog-post .wpcom-enhanced-excerpt li,
.reblog-post .wpcom-enhanced-excerpt h1,
.reblog-post .wpcom-enhanced-excerpt h2,
.reblog-post .wpcom-enhanced-excerpt h3,
.reblog-post .wpcom-enhanced-excerpt h4,
.reblog-post .wpcom-enhanced-excerpt h5,
.reblog-post .wpcom-enhanced-excerpt h6,
.reblog-post .wpcom-enhanced-excerpt p {
	font-size: 100% !important;
}

.reblog-post .wpcom-enhanced-excerpt blockquote,
.reblog-post .wpcom-enhanced-excerpt pre,
.reblog-post .wpcom-enhanced-excerpt code,
.reblog-post .wpcom-enhanced-excerpt q {
	font-size: 98% !important;
}


.reblog-from img {
	margin: 0 10px 0 0;
	vertical-align: middle;
	padding: 0;
	border: 0;
}

.reblog-source {
	margin-bottom: 0;
	font-size: .8em;
	line-height: 1;
}

.reblog-source .more-words {
	color: #668eaa;
}

.wpcom-reblog-snapshot .reblog-post {
	box-shadow: 0 0 0 1px rgba(46, 68, 83, .1) inset, 0 1px 1px rgba(46, 68, 83, .05);
	border-radius: 4px;
	padding: 24px;
}


.wpcom-reblog-snapshot .reblogger-note {
	margin: 0 0 1em 0;
	overflow: hidden;
}

.wpcom-reblog-snapshot p.reblogger-headline {
	line-height: 32px;
	margin: 0 0 1em 0;
}

.wpcom-reblog-snapshot .reblogger-note-content {
	margin: 0;
	padding: 0;
}

body .wpcom-reblog-snapshot .reblogger-note-content blockquote {
	font-style: normal;
	font-weight: normal;
	font-size: 1em;
	margin: 0;
	padding: 0;
	position: relative;
	border: none;
}

.wpcom-reblog-snapshot .reblogger-note-content blockquote p:last-child {
	margin-bottom: 0;
}
.geolocation-chip .noticon {
  display: inline-block;
  vertical-align: middle;
}

.geolocation-chip {
  margin-bottom: 1em;
}