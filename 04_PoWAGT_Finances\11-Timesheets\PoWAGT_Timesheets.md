# PoWAGT Timesheets


## PoWAGT Timesheet Notes

Creating bitbucket repository following task allocation from <PERSON>. Work on configuring <PERSON><PERSON>. Getting records on Big Bell out of the database and using them to create a story about the events using Gemini.  Multiple attempts at getting it correct.

Email re getting database for requirements. Getting a new Atlassian organisation and accounts etc. Saving materials. Emails re the Atlassian and the Google accounts, verifying etc.


## PoWAGT Timesheets Recorded Entries - fhwa03





2025-05-001	<EMAIL>	<EMAIL>	fhwa03	fhwa03: Telcon Tracie Mulvey 20250501t1456-1527	<br><br><span>Discussed the detailed Phase 1 document that I sent through - varous questions on what it meant.  Many people in the group where overwhelmed by the document.</span><br><span>Next steps are to organise a meeting with <PERSON>.</span><br><span>This will require a short presentation from me (ts), including to new members of the committee and invitees, especially about future plans.</span>	01 May 2025   14:56	01 May 2025   15:27	0.52	fhwa03: Telcon Tracie Mulvey 20250501t1456-1527 <br><br><span>Discussed the detailed Phase 1 document that I sent through - varous questions on what it meant.  Many people in the group where overwhelmed by the document.</span><br><span>Next steps are to organise a meeting with <PERSON>.</span><br><span>This will require a short presentation from me (ts), including to new members of the committee and invitees, especially about future plans.</span>			0.52	fhwa	Family History WA	0.00	0.00	2025	5	1								
2025-05-002	<EMAIL>	<EMAIL>	fhwa03	fhwa03: Family History WA Ghosts Project ICT Virtual Meeting 20250520t1900-1950	<br><br><span><b>## fhwa03: Family History WA Ghosts Project ICT Virtual Meeting 20250520t1900-1950</b></span><br><span>[</span><span>Google Meet virtual meeting</span><span>](</span><span><u><a href="https://meet.google.com/sat-hwdf-wih">https://meet.google.com/sat-hwdf-wih</a></u></span><span>)</span><br><span>Ghosts Online Data Entry Tool - (Google Meet)</span><br><span>Tuesday, 20 May · 19:00 – 20:00</span><br><span>Time zone: Australia/Perth</span><br><span>Google Meet joining info</span><br><span>Video call link: <a href="https://meet.google.com/sat-hwdf-wih">https://meet.google.com/sat-hwdf-wih</a></span><br><span>Or dial: ‪(AU) +61 2 9051 7316‬ PIN: ‪***********‬#</span><br><span>More phone numbers: <a href="https://tel.meet/sat-hwdf-wih?pin=8406112478336)">https://tel.meet/sat-hwdf-wih?pin=8406112478336)</a></span><br><span>Attendees:</span><br><span>    Tracie Mulvey</span><br><span>    Christine Harris</span><br><span>    Samuel Bradley</span><br><span>    Tony Sutherland</span><br><span>Notes:</span><br><span>Started at 1900 finished at 1950 - then wrote email to Tracie re my email address.</span><br><span>Introductions by each person</span><br><span>Sam - have done app dev since 2008, currently at CSIRO, turning spreadsheets into database apps, primarily a web developer, does a lot of python</span><br><span>has been experimenting with AI</span><br><span>TS explained the concept of the document which has been produced and the work required</span><br><span>Trello used by all volunteers and committees for project management etc</span><br><span>Also use the full Google suite</span><br><span>no existing platform for code storage</span><br><span>questions from Sam:</span><br><span>  is existing data structure OK? No, needs to be looked at.  Tracie: there is another project which wants to use the new system</span><br><span>  use Directus - has lots of plugins for WYSIWYG etc etc</span><br><span>    define data structure, then get data entry screens for free</span><br><span>Karen - is a key stakeholder. she is head of the Projects Group.</span><br><span>Anne - webmaster. also key stakeholder.</span><br><span>Chris: concept of "soft" releases of a portion of the data - presenting the partial database</span><br><span>Tracie: will send through the document produced by TS to Sam, for him to query etc, then do a quote for the first phase</span><br><br><br><span>Initial Notes / Points To Make:</span><br><span>    - FamilyHistory wants to ensure that all requirements are well understood prior to commencing.</span><br><span>    - Additionally, want to ensure that the design accommodates everything needed, including future capabilities and needs (this is an early design exercise - ??examples??)</span><br><span>      - Examples:</span><br><span>        - Names of tables (generic)</span><br><span>        - Make fields generic</span><br><span>        - Most processing driven by configuration (in database, or config files), rather than hard coded.  Examples, display titles and field names, page/screen names, report names.</span><br><span>        - Data structure explicitly allows for new system to be readily built. Data is typed from within the data structure.</span><br><span>    - FamilyHistory would also like to ensure that the look and feel will be as they want it to be</span><br><span>    - Thus, proposing an initial contract to document all this. Once satisfactory, can contract for the rest of the work (at which time a more precise estimate can also be made).</span><br><span>    - The document produced is meant more as a guide and checklist.</span><br><span>      - It is not meant to be precisely prescriptive, but rather representative of the items/areas which FamilyHistory would like to see addressed</span><br><span>      - Samuel can choose to fulfil the need in any manner, as long as it satisfies the requirement and fulfils the intent and need</span><br><span>      - Various approaches can be taken.</span><br><span>        - For instance, screen designs can be done using simple drawn wireframes, but they could also be done using Figma or Canva etc; or could even be initial code (if that is quicker and there are templates etc).  Each will work, as long as FamilyHistory will be able to see what the screens will look like and can sign-off on what they see</span><br><span>    - Much of the requirements have been expressed in the documentation already provided, but much of that is based on a specific prototype implementation.  It does not necessarily accommodate all future needs.  It needs to be expressed in terms of requirements, now and future, across releases.</span><br><span>      - Feature Driven Design</span><br><span>    - Some of the design decisions etc etc have already been made.  Thus, it is about documenting them.</span><br><span>    - Use whatever tools Samuel wants to use for the documentation and design etc, but needs to have online collaborative capabilities if possible.</span><br><span>      - The Atlassian suite (Jira, Confluence, etc) might be a good possibility</span><br><span>    - The initial phase is also about ensuring that early design decisions are valid and can accommodate the future re-use of the system</span><br><span>    - Much of the work done in this initial phase will be the foundation for later work, and should be able to be re-used</span><br><span>    - Doing the initial phase provides more certainty for all parties, and allows Samuel to be more precise in terms of costings and timings</span>	20 May 2025   19:00	20 May 2025   19:50	0.83	fhwa03: Family History WA Ghosts Project ICT Virtual Meeting 20250520t1900-1950 <br><br><span><b>## fhwa03: Family History WA Ghosts Project ICT Virtual Meeting 20250520t1900-1950</b></span><br><span>[</span><span>Google Meet virtual meeting</span><span>](</span><span><u><a href="https://meet.google.com/sat-hwdf-wih">https://meet.google.com/sat-hwdf-wih</a></u></span><span>)</span><br><span>Ghosts Online Data Entry Tool - (Google Meet)</span><br><span>Tuesday, 20 May · 19:00 – 20:00</span><br><span>Time zone: Australia/Perth</span><br><span>Google Meet joining info</span><br><span>Video call link: <a href="https://meet.google.com/sat-hwdf-wih">https://meet.google.com/sat-hwdf-wih</a></span><br><span>Or dial: ‪(AU) +61 2 9051 7316‬ PIN: ‪***********‬#</span><br><span>More phone numbers: <a href="https://tel.meet/sat-hwdf-wih?pin=8406112478336)">https://tel.meet/sat-hwdf-wih?pin=8406112478336)</a></span><br><span>Attendees:</span><br><span>    Tracie Mulvey</span><br><span>    Christine Harris</span><br><span>    Samuel Bradley</span><br><span>    Tony Sutherland</span><br><span>Notes:</span><br><span>Started at 1900 finished at 1950 - then wrote email to Tracie re my email address.</span><br><span>Introductions by each person</span><br><span>Sam - have done app dev since 2008, currently at CSIRO, turning spreadsheets into database apps, primarily a web developer, does a lot of python</span><br><span>has been experimenting with AI</span><br><span>TS explained the concept of the document which has been produced and the work required</span><br><span>Trello used by all volunteers and committees for project management etc</span><br><span>Also use the full Google suite</span><br><span>no existing platform for code storage</span><br><span>questions from Sam:</span><br><span>  is existing data structure OK? No, needs to be looked at.  Tracie: there is another project which wants to use the new system</span><br><span>  use Directus - has lots of plugins for WYSIWYG etc etc</span><br><span>    define data structure, then get data entry screens for free</span><br><span>Karen - is a key stakeholder. she is head of the Projects Group.</span><br><span>Anne - webmaster. also key stakeholder.</span><br><span>Chris: concept of "soft" releases of a portion of the data - presenting the partial database</span><br><span>Tracie: will send through the document produced by TS to Sam, for him to query etc, then do a quote for the first phase</span><br><br><br><span>Initial Notes / Points To Make:</span><br><span>    - FamilyHistory wants to ensure that all requirements are well understood prior to commencing.</span><br><span>    - Additionally, want to ensure that the design accommodates everything needed, including future capabilities and needs (this is an early design exercise - ??examples??)</span><br><span>      - Examples:</span><br><span>        - Names of tables (generic)</span><br><span>        - Make fields generic</span><br><span>        - Most processing driven by configuration (in database, or config files), rather than hard coded.  Examples, display titles and field names, page/screen names, report names.</span><br><span>        - Data structure explicitly allows for new system to be readily built. Data is typed from within the data structure.</span><br><span>    - FamilyHistory would also like to ensure that the look and feel will be as they want it to be</span><br><span>    - Thus, proposing an initial contract to document all this. Once satisfactory, can contract for the rest of the work (at which time a more precise estimate can also be made).</span><br><span>    - The document produced is meant more as a guide and checklist.</span><br><span>      - It is not meant to be precisely prescriptive, but rather representative of the items/areas which FamilyHistory would like to see addressed</span><br><span>      - Samuel can choose to fulfil the need in any manner, as long as it satisfies the requirement and fulfils the intent and need</span><br><span>      - Various approaches can be taken.</span><br><span>        - For instance, screen designs can be done using simple drawn wireframes, but they could also be done using Figma or Canva etc; or could even be initial code (if that is quicker and there are templates etc).  Each will work, as long as FamilyHistory will be able to see what the screens will look like and can sign-off on what they see</span><br><span>    - Much of the requirements have been expressed in the documentation already provided, but much of that is based on a specific prototype implementation.  It does not necessarily accommodate all future needs.  It needs to be expressed in terms of requirements, now and future, across releases.</span><br><span>      - Feature Driven Design</span><br><span>    - Some of the design decisions etc etc have already been made.  Thus, it is about documenting them.</span><br><span>    - Use whatever tools Samuel wants to use for the documentation and design etc, but needs to have online collaborative capabilities if possible.</span><br><span>      - The Atlassian suite (Jira, Confluence, etc) might be a good possibility</span><br><span>    - The initial phase is also about ensuring that early design decisions are valid and can accommodate the future re-use of the system</span><br><span>    - Much of the work done in this initial phase will be the foundation for later work, and should be able to be re-used</span><br><span>    - Doing the initial phase provides more certainty for all parties, and allows Samuel to be more precise in terms of costings and timings</span>	## # #</span><br><span>More 		0.83	fhwa	Family History WA	0.00	0.00	2025	5	20								
2025-04-002	<EMAIL>	<EMAIL>	fhwa03	fhwa03: Attending meeting with committee to discuss the quotes and make decisions as to who to work with and next steps. Left at 1319 arrived at their office at 1352 into meeting at 1354 finished at 1512 left at 1513 and arrived at 1551.		11 Apr 2025   13:19	11 Apr 2025   15:51	2.53	fhwa03: Attending meeting with committee to discuss the quotes and make decisions as to who to work with and next steps. Left at 1319 arrived at their office at 1352 into meeting at 1354 finished at 1512 left at 1513 and arrived at 1551. 			2.53	fhwa	Family History WA	0.00	0.00	2025	4	11								
2025-04-003	<EMAIL>	<EMAIL>	fhwa03	fhwa03: Creating a document as project plan and scope etc for the Phase 1 of the work.  Initial reading and editing of the document.		13 Apr 2025   14:14	13 Apr 2025   15:51	1.62	fhwa03: Creating a document as project plan and scope etc for the Phase 1 of the work.  Initial reading and editing of the document. 			1.62	fhwa	Family History WA	0.00	0.00	2025	4	13								
2025-04-004	<EMAIL>	<EMAIL>	fhwa03	fhwa03: Receiving document on progressing Phase 1 from Tracie and Chris.  Reading through it, making comments.		14 Apr 2025   11:11	14 Apr 2025   12:21	1.17	fhwa03: Receiving document on progressing Phase 1 from Tracie and Chris.  Reading through it, making comments. 			1.17	fhwa	Family History WA	0.00	0.00	2025	4	14								
2025-04-005	<EMAIL>	<EMAIL>	fhwa03	fhwa03: Further work on the Phase 1 scope/plan following reviewing document sent.		14 Apr 2025   14:24	14 Apr 2025   16:54	2.50	fhwa03: Further work on the Phase 1 scope/plan following reviewing document sent. 			2.50	fhwa	Family History WA	0.00	0.00	2025	4	14								
2025-04-006	<EMAIL>	<EMAIL>	fhwa03	fhwa03: Further work on the Phase 1 scope/plan.		15 Apr 2025   07:47	15 Apr 2025   09:39	1.87	fhwa03: Further work on the Phase 1 scope/plan. 			1.87	fhwa	Family History WA	0.00	0.00	2025	4	15								
2025-04-007	<EMAIL>	<EMAIL>	fhwa03	fhwa03: Finalising first draft of the Phase 1 scope/plan. Exporting then emailing to Tracie with a covering email.		15 Apr 2025   11:11	15 Apr 2025   14:41	3.50	fhwa03: Finalising first draft of the Phase 1 scope/plan. Exporting then emailing to Tracie with a covering email. 			3.50	fhwa	Family History WA	0.00	0.00	2025	4	15								

