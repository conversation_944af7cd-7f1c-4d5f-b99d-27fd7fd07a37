(()=>{var t={2058:(t,e,r)=>{var n;!function(){"use strict";var a={not_string:/[^s]/,not_bool:/[^t]/,not_type:/[^T]/,not_primitive:/[^v]/,number:/[diefg]/,numeric_arg:/[bcdiefguxX]/,json:/[j]/,not_json:/[^j]/,text:/^[^\x25]+/,modulo:/^\x25{2}/,placeholder:/^\x25(?:([1-9]\d*)\$|\(([^\)]+)\))?(\+)?(0|'[^$])?(-)?(\d+)?(?:\.(\d+))?([b-gijostTuvxX])/,key:/^([a-z_][a-z_\d]*)/i,key_access:/^\.([a-z_][a-z_\d]*)/i,index_access:/^\[(\d+)\]/,sign:/^[\+\-]/};function i(t){return function(t,e){var r,n,o,s,l,u,p,c,f,d=1,g=t.length,h="";for(n=0;n<g;n++)if("string"==typeof t[n])h+=t[n];else if(Array.isArray(t[n])){if((s=t[n])[2])for(r=e[d],o=0;o<s[2].length;o++){if(!r.hasOwnProperty(s[2][o]))throw new Error(i('[sprintf] property "%s" does not exist',s[2][o]));r=r[s[2][o]]}else r=s[1]?e[s[1]]:e[d++];if(a.not_type.test(s[8])&&a.not_primitive.test(s[8])&&r instanceof Function&&(r=r()),a.numeric_arg.test(s[8])&&"number"!=typeof r&&isNaN(r))throw new TypeError(i("[sprintf] expecting number but found %T",r));switch(a.number.test(s[8])&&(c=r>=0),s[8]){case"b":r=parseInt(r,10).toString(2);break;case"c":r=String.fromCharCode(parseInt(r,10));break;case"d":case"i":r=parseInt(r,10);break;case"j":r=JSON.stringify(r,null,s[6]?parseInt(s[6]):0);break;case"e":r=s[7]?parseFloat(r).toExponential(s[7]):parseFloat(r).toExponential();break;case"f":r=s[7]?parseFloat(r).toFixed(s[7]):parseFloat(r);break;case"g":r=s[7]?String(Number(r.toPrecision(s[7]))):parseFloat(r);break;case"o":r=(parseInt(r,10)>>>0).toString(8);break;case"s":r=String(r),r=s[7]?r.substring(0,s[7]):r;break;case"t":r=String(!!r),r=s[7]?r.substring(0,s[7]):r;break;case"T":r=Object.prototype.toString.call(r).slice(8,-1).toLowerCase(),r=s[7]?r.substring(0,s[7]):r;break;case"u":r=parseInt(r,10)>>>0;break;case"v":r=r.valueOf(),r=s[7]?r.substring(0,s[7]):r;break;case"x":r=(parseInt(r,10)>>>0).toString(16);break;case"X":r=(parseInt(r,10)>>>0).toString(16).toUpperCase()}a.json.test(s[8])?h+=r:(!a.number.test(s[8])||c&&!s[3]?f="":(f=c?"+":"-",r=r.toString().replace(a.sign,"")),u=s[4]?"0"===s[4]?"0":s[4].charAt(1):" ",p=s[6]-(f+r).length,l=s[6]&&p>0?u.repeat(p):"",h+=s[5]?f+r+l:"0"===u?f+l+r:l+f+r)}return h}(function(t){if(s[t])return s[t];for(var e,r=t,n=[],i=0;r;){if(null!==(e=a.text.exec(r)))n.push(e[0]);else if(null!==(e=a.modulo.exec(r)))n.push("%");else{if(null===(e=a.placeholder.exec(r)))throw new SyntaxError("[sprintf] unexpected placeholder");if(e[2]){i|=1;var o=[],l=e[2],u=[];if(null===(u=a.key.exec(l)))throw new SyntaxError("[sprintf] failed to parse named argument key");for(o.push(u[1]);""!==(l=l.substring(u[0].length));)if(null!==(u=a.key_access.exec(l)))o.push(u[1]);else{if(null===(u=a.index_access.exec(l)))throw new SyntaxError("[sprintf] failed to parse named argument key");o.push(u[1])}e[2]=o}else i|=2;if(3===i)throw new Error("[sprintf] mixing positional and named placeholders is not (yet) supported");n.push(e)}r=r.substring(e[0].length)}return s[t]=n}(t),arguments)}function o(t,e){return i.apply(null,[t].concat(e||[]))}var s=Object.create(null);e.sprintf=i,e.vsprintf=o,"undefined"!=typeof window&&(window.sprintf=i,window.vsprintf=o,void 0===(n=function(){return{sprintf:i,vsprintf:o}}.call(e,r,e,t))||(t.exports=n))}()}},e={};function r(n){var a=e[n];if(void 0!==a)return a.exports;var i=e[n]={exports:{}};return t[n](i,i.exports,r),i.exports}r.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return r.d(e,{a:e}),e},r.d=(t,e)=>{for(var n in e)r.o(e,n)&&!r.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},r.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),r.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var n={};(()=>{"use strict";r.r(n),r.d(n,{__:()=>__,_n:()=>_n,_nx:()=>_nx,_x:()=>_x,createI18n:()=>h,defaultI18n:()=>v,getLocaleData:()=>_,hasTranslation:()=>k,isRTL:()=>F,resetLocaleData:()=>m,setLocaleData:()=>y,sprintf:()=>i,subscribe:()=>w});var t=r(2058),e=r.n(t);const a=function(t,e){var r,n,a=0;function i(){var i,o,s=r,l=arguments.length;t:for(;s;){if(s.args.length===arguments.length){for(o=0;o<l;o++)if(s.args[o]!==arguments[o]){s=s.next;continue t}return s!==r&&(s===n&&(n=s.prev),s.prev.next=s.next,s.next&&(s.next.prev=s.prev),s.next=r,s.prev=null,r.prev=s,r=s),s.val}s=s.next}for(i=new Array(l),o=0;o<l;o++)i[o]=arguments[o];return s={args:i,val:t.apply(null,i)},r?(r.prev=s,s.next=r):n=s,a===e.maxSize?(n=n.prev).next=null:a++,r=s,s.val}return e=e||{},i.clear=function(){r=null,n=null,a=0},i}(console.error);function i(t,...r){try{return e().sprintf(t,...r)}catch(e){return e instanceof Error&&a("sprintf error: \n\n"+e.toString()),t}}var o,s,l,u;o={"(":9,"!":8,"*":7,"/":7,"%":7,"+":6,"-":6,"<":5,"<=":5,">":5,">=":5,"==":4,"!=":4,"&&":3,"||":2,"?":1,"?:":1},s=["(","?"],l={")":["("],":":["?","?:"]},u=/<=|>=|==|!=|&&|\|\||\?:|\(|!|\*|\/|%|\+|-|<|>|\?|\)|:/;var p={"!":function(t){return!t},"*":function(t,e){return t*e},"/":function(t,e){return t/e},"%":function(t,e){return t%e},"+":function(t,e){return t+e},"-":function(t,e){return t-e},"<":function(t,e){return t<e},"<=":function(t,e){return t<=e},">":function(t,e){return t>e},">=":function(t,e){return t>=e},"==":function(t,e){return t===e},"!=":function(t,e){return t!==e},"&&":function(t,e){return t&&e},"||":function(t,e){return t||e},"?:":function(t,e,r){if(t)throw e;return r}};var c={contextDelimiter:"",onMissingKey:null};function f(t,e){var r;for(r in this.data=t,this.pluralForms={},this.options={},c)this.options[r]=void 0!==e&&r in e?e[r]:c[r]}f.prototype.getPluralForm=function(t,e){var r,n,a,i,c=this.pluralForms[t];return c||("function"!=typeof(a=(r=this.data[t][""])["Plural-Forms"]||r["plural-forms"]||r.plural_forms)&&(n=function(t){var e,r,n;for(e=t.split(";"),r=0;r<e.length;r++)if(0===(n=e[r].trim()).indexOf("plural="))return n.substr(7)}(r["Plural-Forms"]||r["plural-forms"]||r.plural_forms),i=function(t){var e=function(t){for(var e,r,n,a,i=[],p=[];e=t.match(u);){for(r=e[0],(n=t.substr(0,e.index).trim())&&i.push(n);a=p.pop();){if(l[r]){if(l[r][0]===a){r=l[r][1]||r;break}}else if(s.indexOf(a)>=0||o[a]<o[r]){p.push(a);break}i.push(a)}l[r]||p.push(r),t=t.substr(e.index+r.length)}return(t=t.trim())&&i.push(t),i.concat(p.reverse())}(t);return function(t){return function(t,e){var r,n,a,i,o,s,l=[];for(r=0;r<t.length;r++){if(o=t[r],i=p[o]){for(n=i.length,a=Array(n);n--;)a[n]=l.pop();try{s=i.apply(null,a)}catch(t){return t}}else s=e.hasOwnProperty(o)?e[o]:+o;l.push(s)}return l[0]}(e,t)}}(n),a=function(t){return+i({n:t})}),c=this.pluralForms[t]=a),c(e)},f.prototype.dcnpgettext=function(t,e,r,n,a){var i,o,s;return i=void 0===a?0:this.getPluralForm(t,a),o=r,e&&(o=e+this.options.contextDelimiter+r),(s=this.data[t][o])&&s[i]?s[i]:(this.options.onMissingKey&&this.options.onMissingKey(r,t),0===i?r:n)};const d={plural_forms:t=>1===t?0:1},g=/^i18n\.(n?gettext|has_translation)(_|$)/,h=(t,e,r)=>{const n=new f({}),a=new Set,i=()=>{a.forEach((t=>t()))},o=(t,e="default")=>{n.data[e]={...n.data[e],...t},n.data[e][""]={...d,...n.data[e]?.[""]},delete n.pluralForms[e]},s=(t,e)=>{o(t,e),i()},l=(t="default",e,r,a,i)=>(n.data[t]||o(void 0,t),n.dcnpgettext(t,e,r,a,i)),u=(t="default")=>t,_x=(t,e,n)=>{let a=l(n,e,t);return r?(a=r.applyFilters("i18n.gettext_with_context",a,t,e,n),r.applyFilters("i18n.gettext_with_context_"+u(n),a,t,e,n)):a};if(t&&s(t,e),r){const t=t=>{g.test(t)&&i()};r.addAction("hookAdded","core/i18n",t),r.addAction("hookRemoved","core/i18n",t)}return{getLocaleData:(t="default")=>n.data[t],setLocaleData:s,addLocaleData:(t,e="default")=>{n.data[e]={...n.data[e],...t,"":{...d,...n.data[e]?.[""],...t?.[""]}},delete n.pluralForms[e],i()},resetLocaleData:(t,e)=>{n.data={},n.pluralForms={},s(t,e)},subscribe:t=>(a.add(t),()=>a.delete(t)),__:(t,e)=>{let n=l(e,void 0,t);return r?(n=r.applyFilters("i18n.gettext",n,t,e),r.applyFilters("i18n.gettext_"+u(e),n,t,e)):n},_x,_n:(t,e,n,a)=>{let i=l(a,void 0,t,e,n);return r?(i=r.applyFilters("i18n.ngettext",i,t,e,n,a),r.applyFilters("i18n.ngettext_"+u(a),i,t,e,n,a)):i},_nx:(t,e,n,a,i)=>{let o=l(i,a,t,e,n);return r?(o=r.applyFilters("i18n.ngettext_with_context",o,t,e,n,a,i),r.applyFilters("i18n.ngettext_with_context_"+u(i),o,t,e,n,a,i)):o},isRTL:()=>"rtl"===_x("ltr","text direction"),hasTranslation:(t,e,a)=>{const i=e?e+""+t:t;let o=!!n.data?.[null!=a?a:"default"]?.[i];return r&&(o=r.applyFilters("i18n.has_translation",o,t,e,a),o=r.applyFilters("i18n.has_translation_"+u(a),o,t,e,a)),o}}},x=window.wp.hooks,b=h(void 0,void 0,x.defaultHooks),v=b,_=b.getLocaleData.bind(b),y=b.setLocaleData.bind(b),m=b.resetLocaleData.bind(b),w=b.subscribe.bind(b),__=b.__.bind(b),_x=b._x.bind(b),_n=b._n.bind(b),_nx=b._nx.bind(b),F=b.isRTL.bind(b),k=b.hasTranslation.bind(b)})(),(window.wp=window.wp||{}).i18n=n})();
//# sourceMappingURL=index.min.js.map