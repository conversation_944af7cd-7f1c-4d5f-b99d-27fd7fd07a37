window.addEventListener("DOMContentLoaded",(function(){const e=document.querySelector("#commentform");e&&(window.IntersectionObserver?new IntersectionObserver((function(e){for(const n of e)if(n.isIntersecting){const e=Date.now();return WP_Enqueue_Dynamic_Script.loadScript("verbum").then((()=>{const n=Date.now();VerbumComments.fullyLoadedTime=n-e})),void this.disconnect()}})).observe(e):WP_Enqueue_Dynamic_Script.loadScript("verbum"))}));