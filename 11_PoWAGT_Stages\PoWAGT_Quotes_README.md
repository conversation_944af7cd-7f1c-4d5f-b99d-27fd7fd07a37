# People of Western Australia's <PERSON>


## fhwa03: Responding to <PERSON>'s Queries re Phase 1 20250603t0858-0952
From: <PERSON> <<EMAIL>>
Date: <PERSON><PERSON>, 3 Jun 2025 at 09:42
Subject: <PERSON>'s comments Re: <PERSON> queries 20250603t0942
To: <PERSON><PERSON><PERSON> <<EMAIL>>
Cc: <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>
<PERSON> (and <PERSON>)
Comments interspersed below.
<PERSON>, 3 Jun 2025 at 08:41, <PERSON><PERSON><PERSON> <<EMAIL>> wrote:

    Hi <PERSON> and <PERSON>
    Please see below questions and comments from <PERSON>.  I would appreciate your input before responding back to him.  I've started the ball rolling with comments in italics.  Please add/change where required.

    I have a few questions and comments from reading through the Project Concept and Plan document.

    I'm pretty comfortable with most of what is being asked for. This document very reads like this project will be run and managed through Family History WA and If successful I will be a resource taking on many of the roles associated with the project, so I would just like a bit more clarity on the other people and roles associated with the project and what I am not responsible for. 
    I'm particularly interested in the requirements gathering process and have a few more questions.

        Does the previous document you shared act as a starting point for the requirements? How much still needs to be determined?  Yes this is a starting point for the ghost project, however it does not cover requirements for other FHWA projects. As such, it also does not cover requirements associated with developing a more generic solution which can accommodate other FHWA projects into the future (even if those projects are not specifically considered at the current time - they may not have even been conceptualised at this stage).

        Do you have a rough idea of the number of interviews/workshops we would be conducting?  Ideally I would want one meeting where we get all participants together, eg Webmaster, FHWA Projects and Ghost Project leads.  However, that can prove difficult with people's calendars.  Recommend including 2. I think at least 2 workshops.  One workshop specifically for the Ghosts project and everything to do with that. Second workshop for requirements around additional projects for FHWA, as well as the requirements relating to the generic extensible solution. Specific details pertaining to web and infrastructure management, etc can be handled via emails and/or individual virtual meeting sessions, as needed.
        Would I be leading these sessions? What support would I have?  Recommend Chris, Tony and I to lead and provide support for the sessions. Tony Sutherland quite happy to lead/facilitate the workshops. Sam, Chris and Tracie to take notes and provide raw materials to Sam to integrate into the requirements documentation.

    If you were looking for roles where Family History staff could be more involved, I think splitting the Business Analyst role between myself and someone else would be ideal. I find projects work best when there can be some push and pull between the business analyst and the lead developer.  Recommend Tony as business analyst. Yes, fine for Tony to act as a business analyst. Will ensure liaison with others in FHWA as needed. In addition, Tony is willing to provide asisstance in relation to the software product design (has a lot of experience with small/medium/large scale systems and packaged solutions suitable for multiple clients/projects/implementations).

    In regard to the software development lifecycle information, I will note that this project as a whole is structured as a waterfall project, but I can see there is a strong appetite for allowing for review and updates based on feedback which is good, and perhaps some room for a simplified agile approach during development.  Quite happy for the core development to follow an agile approach.  The waterfall aspects are more in relation to the nature of facilitating releases of the software, in that we need to ensure that migrating to production is smooth. There are lots of personnel in FHWA of varying levels of comuting capability who will need to use the product, so it needs to be quite stable prior to release, especially in relation to training etc.  Thus, get the product signed off ready for production and training then go into the final phase.

    For this study phase a hybrid approach is proposed that involves iteration, which I see as a good common-sense way of ensuring each deliverable is able to be reviewed and updated based on the new information provided when each new document or deliverable is drafted.  Happy with approach.  Absolutely. And, hence, ensure that a good set of collaboration and commenting facilities are put into place. As discussed, the Atlassian toolset ( Jira for project management, Jira Product Discovery for ongoing product management and roadmaps (this is for the future projects in FHWA), Confluence for knowledge sharing, Bitbucket for code management, and Compass for navigating a distributed architecture. Loom may be a possibility but could be too limited in the free version ) may be a good fit. This could be used in conjunction with various Google apps/tools.


## fhwa03: Family History WA Ghosts Project ICT Virtual Meeting 20250520t1900-1950
[Google Meet virtual meeting](https://meet.google.com/sat-hwdf-wih)
Ghosts Online Data Entry Tool - (Google Meet)
Tuesday, 20 May · 19:00 – 20:00
Time zone: Australia/Perth
Google Meet joining info
Video call link: https://meet.google.com/sat-hwdf-wih
Or dial: ‪(AU) +61 2 9051 7316‬ PIN: ‪***********‬#
More phone numbers: https://tel.meet/sat-hwdf-wih?pin=8406112478336)
Attendees:
    Tracie Mulvey
    Christine Harris
    Samuel Bradley
    Tony Sutherland
Notes:
Started at 1900 finished at 1950 - then wrote email to Tracie re my email address.
Introductions by each person
Sam - have done app dev since 2008, currently at CSIRO, turning spreadsheets into database apps, primarily a web developer, does a lot of python
has been experimenting with AI
TS explained the concept of the document which has been produced and the work required
Trello used by all volunteers and committees for project management etc
Also use the full Google suite
no existing platform for code storage
questions from Sam:
  is existing data structure OK? No, needs to be looked at.  Tracie: there is another project which wants to use the new system
  use Directus - has lots of plugins for WYSIWYG etc etc
    define data structure, then get data entry screens for free
Karen - is a key stakeholder. she is head of the Projects Group.
Anne - webmaster. also key stakeholder.
Chris: concept of "soft" releases of a portion of the data - presenting the partial database
Tracie: will send through the document produced by TS to Sam, for him to query etc, then do a quote for the first phase


Initial Notes / Points To Make:
    - FamilyHistory wants to ensure that all requirements are well understood prior to commencing.
    - Additionally, want to ensure that the design accommodates everything needed, including future capabilities and needs (this is an early design exercise - ??examples??)
      - Examples:
        - Names of tables (generic)
        - Make fields generic
        - Most processing driven by configuration (in database, or config files), rather than hard coded.  Examples, display titles and field names, page/screen names, report names.
        - Data structure explicitly allows for new system to be readily built. Data is typed from within the data structure.
    - FamilyHistory would also like to ensure that the look and feel will be as they want it to be
    - Thus, proposing an initial contract to document all this. Once satisfactory, can contract for the rest of the work (at which time a more precise estimate can also be made).
    - The document produced is meant more as a guide and checklist.
      - It is not meant to be precisely prescriptive, but rather representative of the items/areas which FamilyHistory would like to see addressed
      - Samuel can choose to fulfil the need in any manner, as long as it satisfies the requirement and fulfils the intent and need
      - Various approaches can be taken.
        - For instance, screen designs can be done using simple drawn wireframes, but they could also be done using Figma or Canva etc; or could even be initial code (if that is quicker and there are templates etc).  Each will work, as long as FamilyHistory will be able to see what the screens will look like and can sign-off on what they see
    - Much of the requirements have been expressed in the documentation already provided, but much of that is based on a specific prototype implementation.  It does not necessarily accommodate all future needs.  It needs to be expressed in terms of requirements, now and future, across releases.
      - Feature Driven Design
    - Some of the design decisions etc etc have already been made.  Thus, it is about documenting them.
    - Use whatever tools Samuel wants to use for the documentation and design etc, but needs to have online collaborative capabilities if possible.
      - The Atlassian suite (Jira, Confluence, etc) might be a good possibility
    - The initial phase is also about ensuring that early design decisions are valid and can accommodate the future re-use of the system
    - Much of the work done in this initial phase will be the foundation for later work, and should be able to be re-used
    - Doing the initial phase provides more certainty for all parties, and allows Samuel to be more precise in terms of costings and timings



## Telcon Tracie Mulvey 20250501t1456-1527
fhwa03: Telcon Tracie Mulvey 20250501t1456-1527
Discussed the detailed Phase 1 document that I sent through - varous questions on what it meant.  Many people in the group where overwhelmed by the document.
Next steps are to organise a meeting with Sam Bradley.
This will require a short presentation from me (ts), including to new members of the committee and invitees, especially about future plans.

## Meeting with Family History WA Committee 20250411t1354-1512
fhwa03: Attending meeting with committee to discuss the quotes and make decisions as to who to work with and next steps. Left at 1319 arrived at their office at 1352 into meeting at 1354 finished at 1512 left at 1513 and arrived at 1551.

In attendance:
Tracie; Christine Harris; Anne; Giles; Rob Bennett via Zoom; Tony Sutherland

Make a phase 1 which is the requirements verification; detailed design, UX/mockups
performed by Samuel Bradley
and paid for as a separate phase, say $4000
need to write a letter about this

Samuel has some history and understanding of Family History WA, so should be a good fit.

Christine has organised a good deal with Media Cloud for hosting etc and has a lot of spare capacity available - Family History WA have their own server at Media Cloud.

Next Steps:
1. Prepare a brief for Samuel to do a Phase 1 - Tony to send something.
2. Meeting with Samuel - arrange Phase 1 contract.


## Comments on Various Quotes 20250319

* Summed the costs for development as presented in the quotes, for both the complete development as well as for the Data Entry Initial Stage (as an initial provision of a system).
* Incorporated the cost of hosting for 12 months, based on the minimum hosting environment suggested by the people quoting.
* Did not include the cost of providing a monthly maintenance service.  Codevelopment was the only organisation offering this service (and costing it - but with 2 different costs between the data entry stage and the complete development). The total annual cost for Codevelopment could be $8,448 p.a. incGST.
* All costs had GST applied, to make the comparisons valid (since the hosting costs included GST).
* The final costs are likely to be somewhat different from the costs outlined below (since the latter are for comparison purposes).
* All of the vendors apart from PWD basically stated they would make the data entry stage the first part of the complete development. PWD could be asked to revise their data entry quote to make it the first part of the complete development.
* As a side note, the Codevelopment contract states that the IP remains with Codevelopment.  This is not acceptable. Sam Bradley specifically said that Family History would own the IP.  Not mentioned by the others.
* From an initial technical appraisal, all of Wordpress (PWD), PHP Laravel (Six Character) and Drupal (Codevelopment) are well known technologies and one could find support for these elsewhere. Directus (Sam Bradley) is not as well used in WA but is used world-wide quite a bit.  It is open-source and it is likely one could find people to work with it if needed.
  * Without more detailed analysis, it is difficult to ascertain which technology would be more likely to be able to be further developed. Each of the technology "stacks" has its own strengths and weaknesses.
    * For instance, Drupal has a module which allows for AI integration, Wordpress has some plugins for AI, and PHP can be programmed to integrate with AI systems (PHP is a general purpose language). Directus is basically a system to connect a database with webpages, and thus requires programming with another language, which could be used to integrate with some AI systems.
    * Mostly, one way or another, each of the technologies would be able to be extended for further development purposes.
* The costs comparison which I worked up is as follows:

Costs Comparisons (Data Entry Stage vs Complete Development):

| Vendor (Technologies)        | Data Entry | Complete Development |
|:-----------------------------|-----------:|---------------------:|
| Sam Bradley (Directus):      |  $9,428.88 |  $14,378.88 |
| Six Character (PHP Laravel): | $12,151.38 |  $16,424.88 |
| PWD (Wordpress Gravity):     | $12,675.30 | $132,391.00 |
| Codevelopment (Drupal):      | $22,353.88 |  $65,283.76 |

* I think the IP clause by Codevelopment discounts using them, but their costs are also quite high, including a high hourly rate.
* The complete development costs for PWD are very high.
* Did you meet with Sam Bradley and Morgan Leek of Six Character? What was the impression of their capabilities?
* Since both Sam Bradley and Six Character have reasonably low total prices, it would be worthwhile having some further discussions with them.

There is a possible alternative whereby I can generate the code for the system using the technologies proposed by them, and see whether they will accept taking over the code and providing ongoing support.  In this manner, the initial cost can be much lower but there is still support provided.

For further discussion.


## Website Development

Costs for Data Entry Stage plus 12 months hosting:

| Sam Bradley (Directus):      |  $9,428.88 |
| Six Character (PHP Laravel): | $12,151.38 |
| PWD (Wordpress Gravity):     | $12,675.30 |
| Codevelopment (Drupal):      | $22,353.88 |


Complete Development of Solution 12 months total costs:

| Sam Bradley (Directus)       |  $14,378.88 |
| Six Characters (PHP Laravel) |  $16,424.88 |
| Codevelopment (Drupal)       |  $65,283.76 |
| PWD (Wordpress)              | $132,391.00 |

Costs Comparisons (Data Entry Stage vs Complete Development):

| Sam Bradley (Directus):      |  $9,428.88 |  $14,378.88 |
| Six Character (PHP Laravel): | $12,151.38 |  $16,424.88 |
| PWD (Wordpress Gravity):     | $12,675.30 | $132,391.00 |
| Codevelopment (Drupal):      | $22,353.88 |  $65,283.76 |



### Various Organisations Stage 1 Quote Comments
20250313_email_Project_quote_for_online_data_collection_tool_20250313t175421_b8xs.pdf



#### PWD
20250307_PWD_Chris_Merton_People_of_Western_Australia_s_Ghost_Towns_Stage_1_20250316t130712_Ztda.pdf

See comments below.
Wordpress, Gravity Forms, WPEngine.

```
99.00 x1 = 99.00 per month (for 6 months)

Subtotal   GST 10%
99.00      9.90
Total AUD including GST: $108.90 per month (for 6 months)
Total over 6 months AUD including GST: $653.40
Total over 12 months AUD including GST: $1306.80

Subtotal   GST 10%
10,335.00  1,033.50
Total AUD including GST: $11,368.50

Total for 6 months hosting and development AUD including GST: $12,021.90
Total for 12 months hosting and development AUD including GST: $12,675.30

```


#### Codevelopment
20250228_Codevelopment_David_Malan_20250316t130712_RzRv.pdf

Uses Drupal
    as used by WA Museum and various others.
Media Cloud WA hosting https://www.mediacloud.net.au/
(possibly could manage with Shared Hosting @$21/mth = $252 p.a.  May need VPS Hosting @$98.24/mth = 1178.88 p.a. )
CONSULTATION & PLANNING $2,800
TECHNICAL REQUIREMENTS (Development) $16,450
TOTAL $19,250 exGST  $21,175 incGST

Stage 1 Development plus 12 months hosting: 21,175 + 1178.88 = $22,353.88

Website managed services (database, administration tools, security and compatibility updates): $470/month = $5,640 p.a.
 Additional services: $260ph, or per estimate.

IP remains with Codevelopment - really should be with Family History.
The agreement is unclear about what happens to the software upon termination.  A cursory read suggests that it has to return to Codevelopment (or remain with it).



#### Six Character
20250312_Six_Character_Media_6cm_Morgan_Leek_20250316t130712_d32C.pdf

Uses PHP, Laravel (Laravel Scout and Typesense, Laravel Sanctum), MySQL, RESTful API, NextJS and React,
The cost for this build would be $9,975 +GST (9975*1.1 = $10,972.50)
Requires a LinuxVPS from MediaCloud (VPS Hosting @$98.24/mth = 1178.88 p.a.)

Stage 1 Development: $9,975 exGST ($10,972.50 incGST)
 
Stage 1 Development plus 12 months hosting: 10,972.50 + 1178.88 = $12,151.38

Adhoc Hourly cost:  $105/hour exGST ($115.50 incGST)


#### Sam Bradley
20250313_SB_Sam_Bradley_Project_Quotation_WA_Ghost_Towns_Website_20250316t130712_DmAK.pdf

Directus headless CMS product (https://directus.io/solutions/headless-cms)
?? not sure what the SQL database is?

 Milestone 1: Project Planning and Setup
 Milestone 2: Directus Setup and Database Integration
 Milestone 3: Finialise Directus for Data Entry
 Milestone 4: Website Data Views
 Milestone 5: Static Content and Blog Module
 Milestone 5 (sic - 6): Map functionality and User Submissions
 Milestone 7: System Optimisation and Compliance
 Milestone 8: Final Deployment and Training

Family History owns IP.

Website hosting will be provided by Media Cloud as specified in the requirements.
Use the VPS Hosting for comparison @$98.24/mth = 1178.88 p.a.

$15,000 in total. 25% deposit ($3,750) upon project commencement, 25%
 ($3,750) after delivery of milestone 3, 50% ($7,500) upon project completion

Data Entry only is Milestone 3 = $3,750 + $3,750 = $7,500 ex GST ($8,250 inc GST)
Data Entry Stage plus 12 months hosting = $8,250 + 1178.88 = $9,428.88


===========================================================================


#### PWD Stage 1 Quote Comments
Comments made on 20250316
20250313_PWD_quote_People_of_Western_Australias_Ghost_Towns_Comments_01_20250314t101229.docx
20250313_PWD_quote_People_of_Western_Australias_Ghost_Towns_Stage_1_20250313t175404_6Cm8.pdf
20250313_PWD_quote_People_of_Western_Australias_Ghost_Towns_Stage_1_20250313t175404_6Cm8.txt


There are a range of considerations posed by the proposal.
Give me a call to discuss further.
If you want me to discuss with PWD as well, then happy to do so.

##### Executive Summary

1. Stage 1 also needs editing capability.
2. Duplication of user registration - issue or not?
3. What does Stage 2 actually do?
4. Stage 1 - A Component or a Throwaway?
    Should Stage 1 actually be the initial build of the final solution?
    If Staged 1 is effectively a throwaway, then alternative solutions could be considered which would save the $12,000 cost of the throwaway solution, which is then applied to the final solution.

##### Recommendation

a. Ask PWD to consider whether Stage 1 should be the initial build of the final solution, rather than a throwaway solution.
b. Ask PWD to provide the cost for each stage (based on Stage 1 being the initial build), and provide a little more detail on what is being proposed in each stage.
c. If PWD still considers that Stage 1 should effectively be a throwaway (as per their current proposal), then consider some low-cost no-code alternatives for Stage 1 and confirm the full cost of a PWD final solution build importing data from the Stage 1 alternative.


More detail follows, which elucidates on the points above ... ...

##### PWD Staging
It is usually a good idea to break the project down into stages or phases,
but I am not sure that I understand whether the stages proposed are actually
that useful or workable in practice.

They propose the following stages, which will theoretically deliver the whole project:

- Stage 1 - Data Collection Module (and exporting existing database)
- Stage 2 - Centralise data for better search functionality.
- Stage 3 - Enhance the design and functionalities based on the collected data to create a full custom public facing website

Some comments below in re each stage.

##### Stage 1 - Data Collection Module (and exporting existing database)
PWD proposes using Gravity Forms running within/under Wordpress using WPEngine to host the Wordpress site.

###### Gravity Forms
Gravity Forms allows for entry of data in a form. The form can be saved and then edited prior to submission, but when submitted, the form data is no longer available for editing (this is the standard form behaviour - verify with PWD whether this is incorrect).

###### Users
The Wordpress site containing the Gravity Form will have to be controlled to only allow registered user access - otherwise anyone on the internet could enter bogus data (and cause all sorts of problems).

How many users are you proposing to allow entry of data in the initial stage?
Are these the same users as already registered in main FHWA site?
Will the new data.fhwa.org.au site share the registered user base, or will it require a separate registration process? 

Based on what PWD state ("Volunteers will be able to create accounts"), it is likely to be a separate registration process.

Not much of a problem if only a few users, but potentially problematical if wanting to allow everyone in FHWA to be able to enter records, and if there are many registered users.

The presumption is that the final ghostswa.org.au site will share the registered user base with fhwa.org.au? Is this correct? If this is the case, having too many people having to multiply register will cause some confusion etc, although this may be a relatively minor concern and could be managed.

###### Data Storage
When the form is submitted, where is the data stored?  In a relational database (like PostgreSQL) or as CSV files etc or some other mechanism?
It appears that it will be stored using Wordpress facilities ("Backend (Wordpress) database setup to store and manage submitted data").

###### Attachments
If photos/documents etc are a part of the data entry, where and how are these stored? How many can be attached per each form?

Large attachments like photos/documents etc can not be exported in CSV (unless one used various advanced techniques like Base64 conversion, and then usually the line lengths are far too long to handle in CSV).

How will attachments be handled in the conversion process to the new platform?

###### Data Editing
Once the Gravity Form is submitted, it is unclear that any of the data entered will be able to be edited, e.g. to fix a typo, to add some additional information, to upload another photo or document etc. The presumption is that the data will NOT be able to edited, because this is not what normal Gravity Forms does.
I did find an add-on product called GravityView which is part of GravityKit (https://www.gravitykit.com/). It costs (https://www.gravitykit.com/intro-pricing/) either USD$86 or USD$132 for the first year and then increasing thereafter.

There is also a technical administrative backend inside Wordpress which would allow editing, but I would suggest that that would not be for ordinary users.

###### Stage 1 - A Component or a Throwaway
The big question that I have is whether this initial Stage 1 solution is going to be (a) a component of the final solution, or (b) will the final solution replace the data entry with another product/solution?

If the former (a), then why would there not be an editing capability?

If the latter (b) - which is more likely, given their statement "Export existing database into new platfrom (from CSV)" (sic) - then will the Gravity Forms interface be completely abandoned (most likely)?

As mentioned above, migrating attachments into the new platform/database may pose some additional coding work, all of which is "wasted" from a future perspective.

###### Overall Costings and Proposed Solution Savings
How does the proposed $12,000 cost for Stage 1 fit into the overall project costings?
Does it save money overall, or add to the final total cost?
(Incidentally, what was the proposed cost for the final solution?)

###### Stage 1 should be initial build of final solution
Given all the above, why would PWD not use stage 1 to build the initial aspect of the final solution?

These steps would presumably be:

1. Implement basic platform facility (e.g. if the final solution is all Wordpress based and uses a relational database backend, then create the Wordpress configuration and installation, install and configure the database, provide basic user management functionality). As an adjunct, maybe implement the connection between registered users on FHWA and the new site, to avoid the overlapping of registrations;
2. Design and create the data structures for the final solution, and implement in the database;
3. Design the basic data entry and editing screen(s) for the main data entry (as per what was proposed for Stage 1 by PWD but including editing capability);
4. Build and test the data entry and editing screen(s) for the main data entry, implementing basic CRUD functionality (Create Read Update Delete), allowing for attachments to be processed.

The initial implementation would have limited search capabilities, and possibly just provide a list of records entered, which one could scroll down to find the record to view and edit if required.

This would then neatly lead into Stage 2 and follow-on to Stage 3.

This solution obviates the need for GravityForms and other add-ons.


##### Stage 2 - Centralise data for better search functionality.

###### What does Stage 2 actually do
In the PWD proposal, I did not understand what Stage 2 was supposed to do at all.
What does "Centralise data" mean?
What exactly is being proposed in terms of development etc?

Following on from the outline and queries in re Stage 1, at some point, it appears that PWD are proposing to have to develop a new platform, with all data entry, editing, search and other functionality built into it. The GravityForms solution would then be abandoned after the data migration to the new platform.

Is this what Stage 2 is supposed to be?

If not, then what is Stage 2 actually implementing in what environment?

And if this new development is what Stage 2 is, then why not commence it in Stage 1 (as outlined above) and then extend it in Stage 2 for further search and view functionality?


##### Stage 3 - Enhance the design and functionalities based on the collected data to create a full custom public facing website

I then presume that Stage 3 is taking the new platform that was implemented in Stage 2 (and if it was not implemented in Stage 2, it would have to be implemented in Stage 3 - further exacerbating the issues of wasted development in earlier stages) and adding all the rest of the functionality required, such as different views of the data, advanced search capabilities, etc.

Once again, the issue is whether Stage 1 and Stage 2 are just the early stages of the complete development on the new platform, or whether the work in any of these stages is thrown away?

##### Alternatives for a Stage 1 Data Entry Throwaway

###### Number of Data Entry Registered Users Required
How many people might have to be registered to do data entry in Stage 1?
Or alternatively, do you think it would work to have just 2 or 3 registered users (i.e. FH1, FH2, FH3) and people share the login when doing some data entry (saves on costs)?

###### Data Entry NoCode Products
There are quite a few products which allow you to define really nice data entry forms and store the entries in a database, for later display, editing, etc. They allow uploading of multiple photos/documents etc. They also allow for exporting the data in various formats, and have interfaces to download all the photos/documents as well.

These products are very easy to setup in the first instance and pretty easy to use.

All the products offer a free tier which limits the number of users and the number of records that can be stored. This can be used to test out the design for the data entry app and pick which solution you like the best.

For instance Baserow (https://baserow.io) does all the above and much more, for either USD$12/user/month or USD$22/user/month. See https://baserow.io/pricing.

Other options (which are of a similar pricing nature to baserow.io) include:

   * https://www.clappia.com/
   * https://www.getgrist.com/
   * https://www.airtable.com/
   * https://www.notion.com/
   * https://clickup.com/


###### Data Entry Development
If you want a simple data entry solution and save money for the final build,
then one could use one of the above NoCode products to do the data entry, and then export the data to CSV and import into the final solution (in the same manner that PWD were proposing for their Stage 1 to subsequent stages proposal).

Given the fields which the Ghosts form wants to populate (you would provide that), I could easily generate a solution using a free tier of a good product and that prototype could be iterated to make sure it works really well.

It would not take long to get something going to see what it looks like - I could do that within a day if you provide me with all the fields.

A simple manual could be written and people could get going with the data entry.

I would have to implement the data export facility, which requires a small amount of work to export all the attachments as well as the data itself (the data export is built-in to the product and requires no work - it is just getting all the attachments which requires a little bit of work.  PWD would have to do the same work in their GravityForms solution.)

The data export could be run regularly to provide backups.

When PWD are ready with the basics of their final solution (enough to enter and edit data and read in date from an export), then the latest export could be provided to PWD.

----------------------------

On a side note, some of the costing PWD provided seemed a little strange ...

###### WP Engine hosting
PWD suggest that the data entry needs to be hosted on WPEngine (https://wpengine.com/au/plans/), at a cost of $99 per month. But the actual WPEngine site states that the cost is AUD$83 per month if one pays monthly and only AUD$69 per month if pay annually (i.e. $826/year). Why the pricing discrepancy?

More than that, why is such a high-end hosting solution needed for data entry only. If it goes down and takes some time to recover (which is highly unlikely even for the most basic web hosting solution), it simply means that some data entry is delayed for a little while. As long as backups and data copies are made regularly, no data should be lost.

When the full system is in place, obviously a robust infrastructure solution is necessary (365/24/7 operation, auto-failover, recovery, etc). But it is very unclear from the quote whether a Wordpress site is the end-point infrastructure for the final solution.

The https://ghostswa.au/ website is obviously Wordpress, using WordPress.com (because of the promotional header which still appears).
I am presuming that the main site (https://www.fhwa.org.au/) is not Wordpress (based on the PWD statement: "Although the main FHWA site is not built on WordPress") - presumably it is MemberJungle.



#### PWD Quote
20250313_PWD_quote_People_of_Western_Australias_Ghost_Towns_Stage_1_20250313t175404_6Cm8.pdf

Full Project Objectives:

* Develop a comprehensive genealogical index of people who resided in Western Australian ghost towns
* Include historical, demographic, and geographical information on all known ghost towns, abandoned towns/settlements, and similar places in Western Australia
* Make the information accessible through a dedicated website with advanced search / filtering capabilities
* Aim for the project to be self-funding through public and private grants, public donations, and monetisation of the collected information
* Proposed Stages:
    - Stage 1 - Data Collection Module (and exporting existing database)
    - Stage 2 - Centralise data for better search functionality.
    - Stage 3 - Enhance the design and functionalities based on the collected data to create a full custom public facing website

53 Hours Allocated: 195.00 x 53 = 10,335.00


##### Data Collection Module - Stage 1

We will need to create a subdomain on WordPress for this project (e.g., data.fhwa.org.au) to host the data collection module. Although the main FHWA site is not built on WordPress, the subdomain will be configured to run on a separate WordPress instance, ensuring smooth integration with the main site while maintaining flexibility for future expansion. This approach allows the data collection tool to function independently, with its own database and access controls, while still aligning with the overall FHWA infrastructure.

Stage 1 of the project focuses on developing a secure, role-based data collection module for the "People of Western Australian Ghost Towns" initiative. Volunteers will be able to create accounts and submit data via a password-protected subdomain, using Gravity Forms integrated with a backend database. The module will allow for efficient data entry and future scalability, with an emphasis on secure access and ease of use. This phase will be completed with a design, development, testing, and volunteer account setup, ensuring smooth integration into Phase 2.

##### Design: - Estimated at 8 hours

* User-friendly, intuitive interface for easy data submission
* Clean and accessible layout
* High visibility design for an older demographic
* Fully responsive, compatible across devices
* Simple, easy-to-navigate structure
* Supports role-based access for volunteers and admins
* Scalable design to accommodate future integration with the data collection module

##### Development: - Estimated at 39 Hours

* Development of a secure, password-protected subdomain for data collection
* Integration of Gravity Forms for data entry and submission
* Backend (Wordpress) database setup to store and manage submitted data
* Role-based access system to manage volunteer and admin accounts
* Implementation of quality assurance and testing to ensure functionality and security
* Export existing database into new platfrom (from CSV)
* Full QA and testing prior to launch
* Scalable architecture to support future phases and database centralisation

##### Project Management: - Estimated at 6 hours

* Dedicated project manager
* Development progress meetings
* Pre-launch review meeting
* Training for volunteers

##### WP Engine Premium Hosting - Recommended for Security
WP Engine is a premium managed WordPress hosting provider designed to deliver high performance, secure, and scalable solutions for WordPress websites including features such as automated backups, managed security, a content delivery network (CDN), and expert WordPress support, ensuring your website operates at peak performance.

##### Costs

```
99.00 x1 = 99.00 per month (for 6 months)

Subtotal   GST 10%
99.00      9.90
Total AUD including GST: $108.90 per month (for 6 months)
Total over 6 months AUD including GST: $653.40

Subtotal   GST 10%
10,335.00  1,033.50
Total AUD including GST: $11,368.50

Total for hosting and development AUD including GST: $12,021.90

```



===============================================================
===============================================================

### Various Organisations Complete Development Quotes Comments

Complete Development of Solution 12 months total costs:

| Codevelopment | Drupal | $58,170 + $1178.88 = $59,348.88 exGST $65,283.76 incGST |
| PWD | Wordpress | $109,440.00 + $9,608.64 + $1306.80 = $120,355.44 exGST $132,391.00 incGST |
| Six Characters | PHP Laravel | $15,246 + $1,178.88 = $16,424.88 incGST |
| Sam Bradley | Directus | $13,200 + $1,178.88 = $14,378.88 incGST |



#### Codevelopment
20240725_Codevelopment_Quote_2024_07_25_Family_History_WA_WEBSITE_DEVELOPMENT_20250316t130734_TCoV.pdf

25th July 2024
Uses Drupal (as used by WA Museum and various others), Apache SOLR
Media Cloud WA hosting https://www.mediacloud.net.au/

Key Requirements, Setup, Search: $18,550
UX REQUIREMENTS:  $15,890
Backend:  $18,130
Consultation, planning and communication: $5,600

Complete development total: $58,170 exGST $63,987 incGST

WEBSITE MAINTENANCE monthly fee: $640 per month exGST $704 per month incGST (12 months is $7680 exGST $8448 incGST)
Additional services: $260 per hour, or per estimate

Media Cloud WA hosting https://www.mediacloud.net.au/
(possibly could manage with Shared Hosting @$21/mth = $252 p.a.  May need VPS Hosting @$98.24/mth = 1178.88 p.a. )

12 months total cost with no support, for comparison purposes: $58,170 + $1178.88 = $59,348.88 exGST $65,283.76 incGST
12 months total cost including monthly support: $58,170 + $8,448 + $1,178.88 = $67,896.88 exGST $74,686.56 incGST


#### PWD
20240729_PWD_Quote_People_of_Western_Australia_s_Ghost_Towns_Website_Project_20250316t130734_Hasx.pdf

Built using Wordpress CMS

Website Development-Stage1
 Stage 1 will be ensuring that the required functionality is achieved. 
 $180.00/hr x 640 hours with a 5% discount = $109,440.00 exGST $115,368.00 incGST

Priority Support and Maintenance Retainer: $180.00/hr x 5 hours with a 5% discount = $853.55 exGST $900.72 incGST per month, which is $9,608.64 exGST $10,088.64 incGST for 12 months.

Adhoc hourly rate $180 exGST $198 incGST

12 months total cost: $109,440.00 + $9,608.64 + $1306.80 = $120,355.44 exGST $132,391.00 incGST


#### Six Character
20240801_6CM_Morgan_Leek_Quote_People_of_WA_s_Ghost_Towns_20250316t130734_J8Aq.pdf

Uses PHP, Laravel (Laravel Scout and Typesense, Laravel Sanctum), MySQL, RESTful API, NextJS and React,
The cost for this build would be $9,975 +GST (9975*1.1 = $10,972.50)
Requires a LinuxVPS from MediaCloud (VPS Hosting @$98.24/mth = 1178.88 p.a.)

Adhoc Hourly cost:  $105/hour exGST ($115.50 incGST)

Complete Build cost: $13,860 exGST $15,246 incGST

Complete Development plus 12 months hosting: $15,246 + $1,178.88 = $16,424.88 incGST


#### Sam Bradley
20240807_Samuel_Bradley_Website_Quote_20250316t130734_MfLt.pdf
20240807_SB_Samuel_Bradley_wa_ghost_towns_architecture_20250316t130712_3l6c.png

Directus headless CMS product (https://directus.io/solutions/headless-cms)
?? not sure what the SQL database is?

Deploy and configure Directus CMS: $3,000 exGST $3,300 incGST
Directus CMS Initial support and training: $1,500 exGST $1,650 incGST
Build a Website to Display content from Directus: $6,000 exGST $6,600 incGST
Website testing, fixes and visual enhancements: $1,500 exGST $1,650 incGST

Complete development cost: $12,000 exGST $13,200 incGST 

Website hosting will be provided by Media Cloud as specified in the requirements.
Use the VPS Hosting for comparison @$98.24/mth = 1178.88 p.a.

Complete Development plus 12 months hosting: $13,200 + $1,178.88 = $14,378.88 incGST


